-- 增加字段
DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_search_log_new' AND column_name='djsy')
THEN
    ALTER TABLE t_search_log_new ADD djsy longtext DEFAULT NULL COMMENT '登记事由';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;



