package com.trs.police.ulportal.common.enums;

import lombok.Getter;

/**
 * 社区分析警情类枚举
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
public enum PersonClueWarningReasonEnum {
    /**
     * 线索类别 0：盗窃；1：吸毒人员驾驶机动车；2：无证驾驶；
     * 3：涉邪；4：设疆；5：蝙蝠聊天；6：涉黄人员；100：盗窃三车；101：剽窃
     */
    DQ(0,"盗窃前科人员“{0}（{1}） ” 于 {2}（{3}） 案发前后24小时在案发地点出现，触发预警。"),
    DRUG_DRIVER(1,"吸毒前科人员“ {0}（{1}） ” 于 {2} 在驾驶证被 {3} 的情况下驾驶车牌号为 {4} 的汽车经过 {5}，触发预警。"),
    DRIVER_WITHOUT_LICENCE(2,"“ {0}（{1}） ” 于 {2} 在驾驶证被 {3} 的情况下驾驶车牌号为 {4} 的汽车经过 {5}，触发预警。"),
    SX_PERSON(3,"发现人员“{0}（{1}）”的手机中{2}，现预测此人员为涉邪人员，触发预警。"),
    SJ_PERSON(4,"发现人员“{0}（{1}）”的手机系统语言为维语，现预测此人员为疆籍人员，触发预警。"),
    BAT_CHECK_SH(5,"蝙蝠聊天疑似涉黄人员“{0}（{1}）”于 {2} 在“ {3}”{4}群聊（{5}）中发布涉黄信息，触发预警。"),
    SH_PERSON(6,"涉黄前科人员“{0}（{1}）”涉黄人员重点部位风险分数达到 {2}，{3}风险分数阈值（{4}），触发预警。"),
    DQSC_PERSON(100,"盗窃三车前科人员“{0}（{1}）”盗窃三车风险分数达到 {2}，{3}风险分数阈值（{4}），触发预警。"),
    PQ_PERSON(101,"扒窃前科人员“{0}（{1}）”目前扒窃风险分数累计达到{2}，{3}风险分数阈值（{4}），触发预警。"),
    YSLD_PERSON(104,"发现非高新户籍人员“{0}（{1}）”近期在高新区活跃且现住址也不在高新区，预测此人为高新区流动人口。"),
    SSSQ_PERSON(110,"关注人员“{0}（{1}）”于 {2} 驾驶车牌号为 {3} 的 汽车 经过 {4}，触发预警。"),
    SDRYJJFX(112,"多名涉毒人员出现在{0}，触发预警。"),
    WSYP_RISK_PERSON(114,"发现人员”{0}（{1}）“在高新区活跃其征信异常并且发现手机中安装了翻墙APP和涉政APP，预测此人员为五失一偏风险人员，触发预警。"),
    CFBJ_RISK_PERSON(115,"{0} 发现人员”{1}（{2}）”存在多次报警行为，触发预警。"),
    MZX_RISK_PERSON(116,"{0} 发现人员”{1}（{2}）“的报警内容具有民转刑风险，触发预警。");
    @Getter
    private final Integer code;

    @Getter
    private final String name;

    PersonClueWarningReasonEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


    /**
     * 根据code获取警情名称
     *
     * @param code 警情代码
     * @return 警情名称
     */
    public static String getNameByCode(Integer code) {
        for (PersonClueWarningReasonEnum jq : values()) {
            if (jq.getCode().equals(code)) {
                return jq.getName();
            }
        }
        return null;
    }
}
