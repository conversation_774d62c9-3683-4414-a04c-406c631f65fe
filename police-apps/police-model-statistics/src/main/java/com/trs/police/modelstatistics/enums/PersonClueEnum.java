package com.trs.police.modelstatistics.enums;

import lombok.Getter;

/**
 * 社区分析警情类枚举
 *
 * <AUTHOR>
 * @date 2024/12/09
 */
public enum PersonClueEnum {
    /**
     * 线索类别 0：盗窃；1：吸毒人员驾驶机动车；2：无证驾驶；
     * 3：涉邪；4：设疆；5：蝙蝠聊天；6：涉黄人员；100：盗窃三车；101：剽窃
     */
    DQ(0,"盗窃案件疑似人员"),
    DRUG_DRIVER(1,"吸毒人员驾驶机动车"),
    DRIVER_WITHOUT_LICENCE(2,"无证驾驶"),
    SX_PERSON(3,"隐形涉邪人员线索挖掘"),
    SJ_PERSON(4,"隐形涉疆人员线索挖掘"),
    BAT_CHECK_SH(5,"蝙蝠聊天疑似涉黄人员"),
    SH_PERSON(6,"涉黄人员重点部位预警"),
    DQSC_PERSON(100,"盗窃三车风险预警"),
    PQ_PERSON(101,"扒窃风险预警"),
    LDRK_PERSON(104,"疑似流动人口"),
    SSSQ_PERSON(110,"三失三气驾驶机动车"),
    SDRYJJFX(112,"涉毒人员聚集风险"),
    WSYP_RISK_PERSON(114,"五失一偏风险人员"),
    CFBJ_RISK_PERSON(115,"重复报警人员"),
    MZX_RISK_PERSON(116,"民转刑风险人员");
    @Getter
    private final Integer code;

    @Getter
    private final String name;

    PersonClueEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


    /**
     * 根据code获取警情名称
     *
     * @param code 警情代码
     * @return 警情名称
     */
    public static String getNameByCode(Integer code) {
        for (PersonClueEnum jq : values()) {
            if (jq.getCode().equals(code)) {
                return jq.getName();
            }
        }
        return null;
    }
}
