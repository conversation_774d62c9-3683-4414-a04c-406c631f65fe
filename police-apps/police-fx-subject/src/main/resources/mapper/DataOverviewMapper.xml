<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.DataOverviewMapper">


    <select id="getBdgkkItem" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select distinct id_card as `key`
        from t_warning_fk_person p
        <if test="dto.areaCode != null and dto.areaCode != ''">
            join
            JSON_TABLE(p.zrpcs, '$[*]' COLUMNS ( dept_id INT PATH '$' )) AS jt
            JOIN
            t_dept d ON jt.dept_id = d.id
        </if>
        <where>
            <if test="status != null">
                and on_record = #{status}
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and d.code like concat('',#{dto.areaCode},'%') and d.level = 3
            </if>
        </where>
    </select>

    <select id="selectWarning" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select count(1) as `count` from t_warning_fkrxyj
        <where>
            <if test="dto.keys != null and dto.keys.size() > 0">
                and id_card in
                <foreach collection="dto.keys" item="key" open="(" close=")" separator=",">
                    #{key}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getXsId" resultType="java.lang.Long">
        select distinct(clue_id) from t_profile_group_clue_relation
        <where>
            <if test="groupIds != null and groupIds.size() > 0">
                group_id in
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getZddwItem" resultType="com.trs.police.statistic.domain.bean.CountItem">
       select count(1) as `count` from
        t_control_important_area
        <where>
            police_category = (select code from t_dict td where td.`type` = 'police_kind' and name = '反恐')
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and district_code like concat('',#{dto.areaCode},'%')
            </if>
        </where>
    </select>
    <select id="getQtIds" resultType="java.lang.Long">
        select distinct id
        from t_profile_group_police_control
        <where>
            deleted = 0
            and police_kind = (select code from t_dict td where td.`type` = 'police_kind' and name = '反恐')
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and control_station like concat('',#{dto.areaCode},'%')
            </if>
        </where>
    </select>
    <select id="getBdgkItemMap" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select distinct id_card as `key`,count(distinct p.id) as `count`
        from t_warning_fk_person p
        join
        JSON_TABLE(p.zrpcs, '$[*]' COLUMNS ( dept_id INT PATH '$' )) AS jt
        JOIN
        t_dept d ON jt.dept_id = d.id
        <where>
            <if test="status != null">
                and on_record = #{status}
            </if>
        </where>
        group by d.district_code
    </select>
</mapper>