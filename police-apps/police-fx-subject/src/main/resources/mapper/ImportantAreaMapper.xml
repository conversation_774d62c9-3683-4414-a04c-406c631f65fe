<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.ImportantAreaMapper">

    <resultMap id="ImportantAreaVOMapper" type="com.trs.police.subject.domain.vo.ImportantAreaVO">
        <result column="geometries" property="geometries"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>


    <select id="selectImportantTargetDept" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select
        <choose>
            <when test="isPcs == true">
                category as `key`,
            </when>
            <otherwise>
                district_code as `key`,
            </otherwise>
        </choose>
        count(1) as `count`
        from t_control_important_area area
        <where>
            police_category = (select code from t_dict td where td.`type` = 'police_kind' and name = '反恐')
            <choose>
                <when test="isPcs == true">

                </when>
                <otherwise>
                    <if test="keyValueList != null and keyValueList.size() > 0">
                        and area.district_code in
                        <foreach collection="keyValueList" item="item" open="(" close=")" separator=",">
                            #{item.value}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
        </where>
        group by
        <choose>
            <when test="isPcs == true">
                category
            </when>
            <otherwise>
                district_code
            </otherwise>
        </choose>
    </select>
    <select id="fkImportantTargetUnits" resultMap="ImportantAreaVOMapper">
        select
        a.`id` as id,
        a.name as name,
        (select d.name from t_dict d where d.type = 'control_warning_source_category' and d.code = a.category) as type,
        a.geometries as geometries,
        a.district_code as districtCode,
        a.district_name as districtName,
        (
        select count(distinct id_card)
        from t_warning_fkrxyj
        where JSON_CONTAINS(area_id, CAST(a.id AS JSON))
        <if test="dto.startTime != null and dto.startTime != ''">
            and create_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and create_time &lt;= #{dto.endTime}
        </if>
        ) as activityPersonCount
        from t_control_important_area a
        <where>
            a.deleted = 0 and a.police_category = 12
            <if test="dto.areaCode != null">
                and a.district_code = #{dto.areaCode}
            </if>
        </where>
    </select>

</mapper>