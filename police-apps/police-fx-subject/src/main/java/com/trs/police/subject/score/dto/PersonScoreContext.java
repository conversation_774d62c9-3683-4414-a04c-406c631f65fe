package com.trs.police.subject.score.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;

/**
 * 人员积分计算上下文
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 包含人员基本信息和积分计算所需的各种数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PersonScoreContext {

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 性别（1-男，2-女）
     */
    private String gender;

    /**
     * 出生日期
     */
    private LocalDate birthDate;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 民族
     */
    private String nation;

    /**
     * 政治面貌
     */
    private String politicalStatus;

    /**
     * 文化程度
     */
    private String education;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 职务
     */
    private String position;

    /**
     * 工作年限
     */
    private Integer workYears;

    /**
     * 现住址
     */
    private String currentAddress;

    /**
     * 户籍地址
     */
    private String registeredAddress;

    /**
     * 联系电话
     */
    private String phoneNumber;

    /**
     * 婚姻状况
     */
    private String maritalStatus;

    /**
     * 获奖情况列表
     */
    private String awards;

    /**
     * 违法犯罪记录
     */
    private String criminalRecord;

    /**
     * 社会关系
     */
    private String socialRelations;

    /**
     * 经历信息
     */
    private String experiences;

    /**
     * 风险等级
     */
    private String riskLevel;

    /**
     * 标签信息
     */
    private String tags;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 扩展属性，用于存储其他动态数据
     */
    @Builder.Default
    private Map<String, Object> extendedProperties = new HashMap<>();

    /**
     * 添加扩展属性
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void addExtendedProperty(String key, Object value) {
        if (extendedProperties == null) {
            extendedProperties = new HashMap<>();
        }
        extendedProperties.put(key, value);
    }

    /**
     * 获取扩展属性
     *
     * @param key          属性键
     * @param defaultValue 默认值
     * @param <T>          属性值类型
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtendedProperty(String key, T defaultValue) {
        if (extendedProperties == null || !extendedProperties.containsKey(key)) {
            return defaultValue;
        }
        try {
            return (T) extendedProperties.get(key);
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }

    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @param <T> 属性值类型
     * @return 属性值
     */
    public <T> T getExtendedProperty(String key) {
        return getExtendedProperty(key, null);
    }

    /**
     * 计算年龄（如果年龄为空且出生日期不为空）
     *
     * @return 年龄
     */
    public Integer calculateAge() {
        if (age != null) {
            return age;
        }
        if (birthDate != null) {
            LocalDate now = LocalDate.now();
            age = now.getYear() - birthDate.getYear();
            if (now.getDayOfYear() < birthDate.getDayOfYear()) {
                age--;
            }
        }
        return age;
    }

    /**
     * 检查是否包含指定标签
     *
     * @param tag 标签名称
     * @return 是否包含
     */
    public boolean hasTag(String tag) {
        if (tags == null || tag == null) {
            return false;
        }
        return tags.contains(tag);
    }

    /**
     * 检查是否有犯罪记录
     *
     * @return 是否有犯罪记录
     */
    public boolean hasCriminalRecord() {
        return criminalRecord != null && !criminalRecord.trim().isEmpty() && !"无".equals(criminalRecord.trim());
    }

    /**
     * 检查是否有获奖记录
     *
     * @return 是否有获奖记录
     */
    public boolean hasAwards() {
        return awards != null && !awards.trim().isEmpty() && !"无".equals(awards.trim());
    }

    /**
     * 获取工作年限（如果为空则返回0）
     *
     * @return 工作年限
     */
    public Integer getWorkYearsOrDefault() {
        return workYears != null ? workYears : 0;
    }

    /**
     * 获取年龄（如果为空则计算）
     *
     * @return 年龄
     */
    public Integer getAgeOrCalculate() {
        return calculateAge();
    }

    /**
     * 检查是否为高风险人员
     *
     * @return 是否为高风险人员
     */
    public boolean isHighRisk() {
        return "高".equals(riskLevel) || "HIGH".equalsIgnoreCase(riskLevel);
    }

    /**
     * 检查是否为中风险人员
     *
     * @return 是否为中风险人员
     */
    public boolean isMediumRisk() {
        return "中".equals(riskLevel) || "MEDIUM".equalsIgnoreCase(riskLevel);
    }

    /**
     * 检查是否为低风险人员
     *
     * @return 是否为低风险人员
     */
    public boolean isLowRisk() {
        return "低".equals(riskLevel) || "LOW".equalsIgnoreCase(riskLevel);
    }
}
