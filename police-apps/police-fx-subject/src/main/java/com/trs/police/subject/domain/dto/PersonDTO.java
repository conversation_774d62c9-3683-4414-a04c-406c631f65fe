package com.trs.police.subject.domain.dto;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.dto.BaseListDTO;
import lombok.Data;

/**
 * 人员管控查询
 *
 * <AUTHOR>
 * @date 2024/4/23
 */
@Data
public class PersonDTO extends BaseListDTO {
    /**
     * 人员类型
     */
    private String personType;

    /**
     * 人员类别
     */
    private String personCategory;

    /**
     * 人员状态
     */
    private String personStatus;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域类型，1.重点场所，2.疑似窝点
     */
    private Long locationType;

    /**
     * 列控状态
     */
    private String lkStatus;

    /**
     * 思想状况
     */
    private String thought;

    /**
     * 列控开始时间
     */
    private String lkStartTime;

    /**
     * 列控结束时间
     */
    private String lkEndTime;

    /**
     * 最小分数
     */
    private Integer minScore;

    /**
     * 最大分数
     */
    private Integer maxScore;

    /**
     * 简单模式
     */
    private Boolean sampleMode;

    /**
     * id列表
     */
    private String ids;

    /**
     * 导出的字段
     */
    private String exportFields;


    /**
     * 研判状态
     */
    private String judgeStatus;

    private String startTime;

    private String endTime;

    private String areaCode;

    private String flowStatus;

    private String idCard;

    private String gjlx;

    private String topCondition;

    private String activeTime;

    private String controlAreaCodes;

    /**
     * 专题类型
     */
    private String subjectType;

    /**
     * 标签
     */
    private String tags;

    /**
     * 预警状态
     */
    private Integer warningStatus;

    private Integer signOverdue;

    /**
     * 是否关注
     */
    private Integer careStatus;

    private Long hitSubject;

    private Long hitSubjectScene;

    /**
     * 有无价值：0-无价值，1-有价值
     */
    private Integer worth;

    /**
     * 转换排序字段
     *
     * @return 排序字段
     */
    public String buildOrderField(){
        if("orgJoinTime".equals(getOrderField())){
            return "org_join_time";
        }else if("lkTime".equals(getOrderField())){
            return "lk_time";
        }
        return getOrderField();
    }

    @Override
    protected boolean checkParams() throws ServiceException {
        return false;
    }
}
