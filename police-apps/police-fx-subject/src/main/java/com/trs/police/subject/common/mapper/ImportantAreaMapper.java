package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.subject.domain.dto.FkTsDto;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.vo.ImportantAreaVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 重点管控区域(ImportantArea)持久层
 *
 * <AUTHOR>
 * @date 2022-06-01 15:46:21
 */
@Mapper
public interface ImportantAreaMapper extends BaseMapper<ImportantAreaEntity> {

    /**
     * 获取风险防控-重点目标单位
     *
     * @param dto dto
     * @param page page
     * @return {@link Page}<{@link ImportantAreaVO}>
     */
    Page<ImportantAreaVO> fkImportantTargetUnits(@Param("dto") PersonDTO dto, Page<Object> page);

    /**
     * 获取重点目标单位
     *
     * @param dto dto
     * @param keyValueList 派出所/区域list
     * @param isPcs 是否派出所
     * @return 结果
     */
    List<CountItem> selectImportantTargetDept(@Param("dto") FkTsDto dto, @Param("keyValueList") List<KeyValueVO> keyValueList,
                                              @Param("isPcs") Boolean isPcs);
}