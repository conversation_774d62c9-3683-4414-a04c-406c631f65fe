package com.trs.police.subject.fk.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.FkWarningMapper;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.entity.WarningFkrxyjEntity;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员管控-人员动向
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public class FkPersonTrendServiceImpl extends AbstractPersonControlAnalysisImpl<PersonTrackVO> {

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private FkWarningMapper fkWarningMapper;

    @Override
    protected RestfulResultsV2<PersonTrackVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        try {
            PersonDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());
            Page<Object> page = new Page<>(context.getPageNum(), context.getPageSize());
            Page<PersonTrackVO> pageList = personMapper.fkPersonLocalTrend(dto, page);
            if (CollectionUtils.isEmpty(pageList.getRecords())) {
                return RestfulResultsV2.ok(new ArrayList<>());
            }
            getPersonLabels(pageList.getRecords());
            List<String> idCardList = pageList.getRecords().stream()
                    .distinct().map(PersonTrackVO::getIdCard).collect(Collectors.toList());
            buildWarningInfo(idCardList, pageList);
            return RestfulResultsV2.ok(pageList.getRecords())
                    .addTotalCount(pageList.getTotal())
                    .addPageNum(context.getPageNum())
                    .addPageSize(context.getPageSize());
        } catch (Exception e) {
            throw new RuntimeException("人员动向查询异常", e);
        }
    }

    private void buildWarningInfo(List<String> idCardList, Page<PersonTrackVO> pageList) {
        Map<String, WarningFkrxyjEntity> map = fkWarningMapper.selectList(new QueryWrapper<WarningFkrxyjEntity>()
                        .in("id_card", idCardList))
                .stream()
                .collect(Collectors.groupingBy(
                        WarningFkrxyjEntity::getIdCard,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(WarningFkrxyjEntity::getCaptureTime)),
                                Optional::get
                        )
                ));
        pageList.getRecords().forEach(vo -> {
            WarningFkrxyjEntity entity = map.getOrDefault(vo.getIdCard(), new WarningFkrxyjEntity());
            if (Objects.nonNull(entity)){
                vo.setPersonCategory(entity.getWarningModel());
                vo.setActivityTime(TimeUtil.getSimpleTimeNoHmsSerializer(entity.getCaptureTime()));
                vo.setActivityAddress(entity.getCaptureAddress());
                vo.setCapturePhoto(entity.getFacePhoto());
            }
        });
    }

    private void getPersonLabels(List<PersonTrackVO> records) {
        records.forEach(vo -> {
            if (vo.getPersonType() != null && vo.getPersonType().startsWith("[") && vo.getPersonType().endsWith("]")) {
                // 移除方括号并分割字符串
                String[] labelIds = vo.getPersonType().substring(1, vo.getPersonType().length() - 1).split(",");
                List<Long> labelIdList = Arrays.stream(labelIds)
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Long::parseLong)
                        .collect(Collectors.toList());

                // 如果personLabelIds为空，则使用从personType解析出的值
                if (!CollectionUtils.isEmpty(labelIdList)) {
                    vo.setPersonLabelIds(labelIdList);
                }
            }
        });
        // 获取人员标签名称
        Set<Long> personLabelIds = records.stream()
                .filter(vo -> !CollectionUtils.isEmpty(vo.getPersonLabelIds()))
                .flatMap(o -> o.getPersonLabelIds().stream())
                .collect(Collectors.toSet());
        // 如果personLabelIds为空，直接返回
        if (CollectionUtils.isEmpty(personLabelIds)) {
            return;
        }
        Map<String, String> personLabelMap = fkPersonMapper.getPersonLabelName(personLabelIds).stream()
                .collect(Collectors.toMap(KeyValueVO::getKey, KeyValueVO::getValue));
        // 设置人员标签名称和预警级别名称
        records.forEach(vo -> {
            vo.setPersonType(vo.getPersonLabelIds().stream()
                    .map(v -> personLabelMap.get(v.toString()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(",")));
        });
    }

    @Override
    public String key() {
        return "fk-personTrend";
    }

    @Override
    public String desc() {
        return "人员动向";
    }
}
