package com.trs.police.subject.score.adapter;

import com.trs.police.profile.generic.dto.GenericScoreResult;
import com.trs.police.profile.generic.dto.ScoreContext;
import com.trs.police.profile.generic.dto.ScoreRule;
import com.trs.police.profile.generic.service.GenericScoreService;
import com.trs.police.subject.score.dto.PersonScoreContext;
import com.trs.police.subject.score.dto.ScoreResult;
import com.trs.police.subject.domain.vo.FxActionExcavateDictVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 人员积分适配器
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 将原有的人员积分系统适配到新的通用积分系统
 */
@Slf4j
@Component
public class PersonScoreAdapter {

//    @Autowired
    private GenericScoreService genericScoreService;

    /**
     * 计算人员积分（使用通用积分引擎）
     *
     * @param personContext 人员积分上下文
     * @param configs       积分配置列表
     * @return 积分计算结果
     */
    public ScoreResult calculatePersonScore(PersonScoreContext personContext, List<FxActionExcavateDictVO> configs) {
        log.info("使用通用积分引擎计算人员积分，身份证号：{}", personContext.getIdCard());

        try {
            // 1. 转换人员上下文为通用上下文
            ScoreContext genericContext = convertToGenericContext(personContext);

            // 2. 转换积分配置为通用规则
            List<ScoreRule> genericRules = convertToGenericRules(configs);

            // 3. 使用通用积分引擎计算
            GenericScoreResult genericResult = genericScoreService.calculateScore(genericContext, genericRules);

            // 4. 转换结果为人员积分结果
            ScoreResult personResult = convertToPersonResult(genericResult);

            log.info("人员积分计算完成，身份证号：{}，总分：{}", personContext.getIdCard(), personResult.getTotalScore());
            return personResult;

        } catch (Exception e) {
            log.error("人员积分计算失败，身份证号：{}，错误：{}", personContext.getIdCard(), e.getMessage(), e);
            return createErrorPersonResult(personContext, e.getMessage());
        }
    }

    /**
     * 批量计算人员积分
     *
     * @param personContexts 人员积分上下文列表
     * @param configs        积分配置列表
     * @return 积分计算结果列表
     */
    public List<ScoreResult> batchCalculatePersonScore(List<PersonScoreContext> personContexts, List<FxActionExcavateDictVO> configs) {
        if (CollectionUtils.isEmpty(personContexts)) {
            return new ArrayList<>();
        }

        log.info("批量计算人员积分，人员数量：{}", personContexts.size());

        try {
            // 1. 转换为通用上下文列表
            List<ScoreContext> genericContexts = personContexts.stream()
                    .map(this::convertToGenericContext)
                    .collect(Collectors.toList());

            // 2. 转换积分配置为通用规则
            List<ScoreRule> genericRules = convertToGenericRules(configs);

            // 3. 使用通用积分引擎批量计算
            List<GenericScoreResult> genericResults = genericScoreService.batchCalculateScore(genericContexts, genericRules);

            // 4. 转换结果为人员积分结果列表
            List<ScoreResult> personResults = genericResults.stream()
                    .map(this::convertToPersonResult)
                    .collect(Collectors.toList());

            log.info("批量人员积分计算完成，成功计算：{}人", personResults.size());
            return personResults;

        } catch (Exception e) {
            log.error("批量人员积分计算失败，错误：{}", e.getMessage(), e);
            return personContexts.stream()
                    .map(context -> createErrorPersonResult(context, e.getMessage()))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 转换人员上下文为通用上下文
     *
     * @param personContext 人员积分上下文
     * @return 通用积分上下文
     */
    private ScoreContext convertToGenericContext(PersonScoreContext personContext) {
        ScoreContext genericContext = ScoreContext.builder()
                .primaryKey(personContext.getIdCard())
                .name(personContext.getPersonName())
                .type("person")
                .category("individual")
                .status("active")
                .tags(personContext.getTags())
                .createTime(personContext.getCreateTime())
                .updateTime(personContext.getUpdateTime())
                .build();

        // 添加人员特有属性
        genericContext.addProperty("gender", personContext.getGender());
        genericContext.addProperty("birthDate", personContext.getBirthDate());
        genericContext.addProperty("age", personContext.getAge());
        genericContext.addProperty("nation", personContext.getNation());
        genericContext.addProperty("politicalStatus", personContext.getPoliticalStatus());
        genericContext.addProperty("education", personContext.getEducation());
        genericContext.addProperty("occupation", personContext.getOccupation());
        genericContext.addProperty("workUnit", personContext.getWorkUnit());
        genericContext.addProperty("position", personContext.getPosition());
        genericContext.addProperty("workYears", personContext.getWorkYears());
        genericContext.addProperty("currentAddress", personContext.getCurrentAddress());
        genericContext.addProperty("registeredAddress", personContext.getRegisteredAddress());
        genericContext.addProperty("phoneNumber", personContext.getPhoneNumber());
        genericContext.addProperty("maritalStatus", personContext.getMaritalStatus());
        genericContext.addProperty("awards", personContext.getAwards());
        genericContext.addProperty("criminalRecord", personContext.getCriminalRecord());
        genericContext.addProperty("socialRelations", personContext.getSocialRelations());
        genericContext.addProperty("experiences", personContext.getExperiences());
        genericContext.addProperty("riskLevel", personContext.getRiskLevel());

        // 添加扩展属性
        if (personContext.getExtendedProperties() != null) {
            personContext.getExtendedProperties().forEach(genericContext::addProperty);
        }

        return genericContext;
    }

    /**
     * 转换积分配置为通用规则
     *
     * @param configs 积分配置列表
     * @return 通用规则列表
     */
    private List<ScoreRule> convertToGenericRules(List<FxActionExcavateDictVO> configs) {
        if (CollectionUtils.isEmpty(configs)) {
            return new ArrayList<>();
        }

        return configs.stream()
                .map(this::convertToGenericRule)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个积分配置为通用规则
     *
     * @param config 积分配置
     * @return 通用规则
     */
    private ScoreRule convertToGenericRule(FxActionExcavateDictVO config) {
        ScoreRule rule = ScoreRule.builder()
                .ruleId(config.getId().toString())
                .ruleName(config.getName())
//                .description(config.getDescription())
                .ruleType(mapToGenericRuleType(config.getName(), config.getTagType()))
                .category(config.getTagType())
                .parentRuleId(config.getParentId() != null ? config.getParentId().toString() : null)
                .baseScore(config.getScore())
                .coefficient(config.getCoefficient())
                .formula(config.getFormula())
                .enabled(true)
                .scope("person")
//                .createTime(config.getCreateTime())
//                .updateTime(config.getUpdateTime())
                .build();

        // 转换子规则
        if (!CollectionUtils.isEmpty(config.getChildren())) {
            List<ScoreRule> childRules = config.getChildren().stream()
                    .map(this::convertToGenericRule)
                    .collect(Collectors.toList());
            rule.setChildren(childRules);
        }

        // 添加规则特定配置
        addRuleSpecificConfig(rule, config);

        return rule;
    }

    /**
     * 映射为通用规则类型
     *
     * @param name    配置名称
     * @param tagType 标签类型
     * @return 通用规则类型
     */
    private String mapToGenericRuleType(String name, String tagType) {
        if (name == null) {
            return "unknown";
        }

        // 根据名称映射规则类型
        if (name.contains("年龄")) {
            return "age";
        } else if (name.contains("工作年限") || name.contains("工龄")) {
            return "workYears";
        } else if (name.contains("职务") || name.contains("职位")) {
            return "position";
        } else if (name.contains("获奖") || name.contains("奖励")) {
            return "awards";
        } else if (name.contains("经历") || name.contains("履历")) {
            return "experience";
        } else if (name.contains("教育") || name.contains("学历")) {
            return "education";
        } else if (name.contains("收入") || name.contains("薪资")) {
            return "income";
        } else {
            // 根据标签类型映射
            if ("背景类".equals(tagType)) {
                return "numericRange";
            } else if ("行为类".equals(tagType)) {
                return "keywordMatch";
            } else {
                return "custom";
            }
        }
    }

    /**
     * 添加规则特定配置
     *
     * @param rule   通用规则
     * @param config 原始配置
     */
    private void addRuleSpecificConfig(ScoreRule rule, FxActionExcavateDictVO config) {
        // 根据规则类型添加特定配置
        switch (rule.getRuleType()) {
            case "age":
                rule.addConfig("propertyName", "age");
                break;
            case "workYears":
                rule.addConfig("propertyName", "workYears");
                break;
            case "position":
                rule.addConfig("propertyName", "position");
                break;
            case "awards":
                rule.addConfig("propertyName", "awards");
                break;
            case "experience":
                rule.addConfig("propertyName", "experiences");
                break;
            case "education":
                rule.addConfig("propertyName", "education");
                break;
            default:
                // 默认配置
                break;
        }
    }

    /**
     * 转换通用结果为人员积分结果
     *
     * @param genericResult 通用积分结果
     * @return 人员积分结果
     */
    private ScoreResult convertToPersonResult(GenericScoreResult genericResult) {
        ScoreResult personResult = ScoreResult.builder()
                .idCard(genericResult.getPrimaryKey())
                .personName(genericResult.getName())
                .totalScore(genericResult.getTotalScore())
                .calculateTime(genericResult.getCalculateTime())
                .scoreLevel(genericResult.getScoreLevel())
                .isHighScore(genericResult.getIsHighScore())
                .remark(genericResult.getRemark())
                .build();

        // 转换详细积分
        if (!CollectionUtils.isEmpty(genericResult.getDetailScores())) {
            List<ScoreResult.ScoreDetail> personDetails = genericResult.getDetailScores().stream()
                    .map(this::convertToPersonDetail)
                    .collect(Collectors.toList());
            personResult.setDetailScores(personDetails);
        }

        return personResult;
    }

    /**
     * 转换通用详细积分为人员详细积分
     *
     * @param genericDetail 通用详细积分
     * @return 人员详细积分
     */
    private ScoreResult.ScoreDetail convertToPersonDetail(GenericScoreResult.ScoreDetail genericDetail) {
        return ScoreResult.ScoreDetail.builder()
                .configName(genericDetail.getRuleName())
                .configId(genericDetail.getRuleId() != null ? Integer.parseInt(genericDetail.getRuleId()) : null)
                .tagType(genericDetail.getCategory())
                .originalScore(genericDetail.getOriginalScore())
                .finalScore(genericDetail.getFinalScore())
                .baseScore(genericDetail.getBaseScore())
                .coefficient(genericDetail.getCoefficient())
                .formula(genericDetail.getFormula())
                .hitData(genericDetail.getHitData())
                .hitCount(genericDetail.getHitCount())
                .description(genericDetail.getDescription())
                .dataSource(genericDetail.getDataSource())
                .calculateTime(genericDetail.getCalculateTime())
                .isValid(genericDetail.getIsValid())
                .errorMessage(genericDetail.getErrorMessage())
                .build();
    }

    /**
     * 创建错误的人员积分结果
     *
     * @param personContext 人员上下文
     * @param errorMessage  错误信息
     * @return 错误的人员积分结果
     */
    private ScoreResult createErrorPersonResult(PersonScoreContext personContext, String errorMessage) {
        return ScoreResult.builder()
                .idCard(personContext.getIdCard())
                .personName(personContext.getPersonName())
                .totalScore(0.0)
                .detailScores(new ArrayList<>())
                .scoreLevel("错误")
                .isHighScore(false)
                .remark("计算错误：" + errorMessage)
                .build();
    }
}
