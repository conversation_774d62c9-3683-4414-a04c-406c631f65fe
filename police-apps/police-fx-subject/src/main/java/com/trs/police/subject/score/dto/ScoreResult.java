package com.trs.police.subject.score.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 积分计算结果
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 包含积分计算的详细结果信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ScoreResult {

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 总积分
     */
    private Double totalScore;

    /**
     * 详细积分列表
     */
    @Builder.Default
    private List<ScoreDetail> detailScores = new ArrayList<>();

    /**
     * 计算时间
     */
    @Builder.Default
    private LocalDateTime calculateTime = LocalDateTime.now();

    /**
     * 积分等级
     */
    private String scoreLevel;

    /**
     * 是否为高分人员
     */
    private Boolean isHighScore;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 添加详细积分
     *
     * @param detail 详细积分
     */
    public void addDetailScore(ScoreDetail detail) {
        if (detailScores == null) {
            detailScores = new ArrayList<>();
        }
        detailScores.add(detail);
    }

    /**
     * 根据总分计算积分等级
     *
     * @return 积分等级
     */
    public String calculateScoreLevel() {
        if (totalScore == null) {
            return "未知";
        }

        if (totalScore >= 80) {
            scoreLevel = "高";
            isHighScore = true;
        } else if (totalScore >= 60) {
            scoreLevel = "中";
            isHighScore = false;
        } else if (totalScore >= 40) {
            scoreLevel = "中低";
            isHighScore = false;
        } else {
            scoreLevel = "低";
            isHighScore = false;
        }

        return scoreLevel;
    }

    /**
     * 积分详细信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ScoreDetail {

        /**
         * 配置项名称
         */
        private String configName;

        /**
         * 配置项ID
         */
        private Integer configId;

        /**
         * 标签类型
         */
        private String tagType;

        /**
         * 原始分值
         */
        private Double originalScore;

        /**
         * 最终分值
         */
        private Double finalScore;

        /**
         * 基础分值
         */
        private Integer baseScore;

        /**
         * 系数
         */
        private Double coefficient;

        /**
         * 计算公式
         */
        private String formula;

        /**
         * 命中的具体数据
         */
        private String hitData;

        /**
         * 命中次数
         */
        private Integer hitCount;

        /**
         * 计算说明
         */
        private String description;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 计算时间
         */
        @Builder.Default
        private LocalDateTime calculateTime = LocalDateTime.now();

        /**
         * 是否有效
         */
        @Builder.Default
        private Boolean isValid = true;

        /**
         * 错误信息（如果计算失败）
         */
        private String errorMessage;

        /**
         * 创建成功的积分详情
         *
         * @param configName    配置名称
         * @param originalScore 原始分值
         * @param finalScore    最终分值
         * @param hitData       命中数据
         * @return 积分详情
         */
        public static ScoreDetail createSuccess(String configName, Double originalScore, Double finalScore, String hitData) {
            return ScoreDetail.builder()
                    .configName(configName)
                    .originalScore(originalScore)
                    .finalScore(finalScore)
                    .hitData(hitData)
                    .hitCount(1)
                    .isValid(true)
                    .build();
        }

        /**
         * 创建失败的积分详情
         *
         * @param configName   配置名称
         * @param errorMessage 错误信息
         * @return 积分详情
         */
        public static ScoreDetail createError(String configName, String errorMessage) {
            return ScoreDetail.builder()
                    .configName(configName)
                    .originalScore(0.0)
                    .finalScore(0.0)
                    .isValid(false)
                    .errorMessage(errorMessage)
                    .build();
        }

        /**
         * 创建无命中的积分详情
         *
         * @param configName 配置名称
         * @return 积分详情
         */
        public static ScoreDetail createNoHit(String configName) {
            return ScoreDetail.builder()
                    .configName(configName)
                    .originalScore(0.0)
                    .finalScore(0.0)
                    .hitCount(0)
                    .description("未命中任何条件")
                    .isValid(true)
                    .build();
        }

        /**
         * 获取分值变化说明
         *
         * @return 分值变化说明
         */
        public String getScoreChangeDescription() {
            if (originalScore == null || finalScore == null) {
                return "分值计算异常";
            }

            if (originalScore.equals(finalScore)) {
                return String.format("基础分值：%.2f", finalScore);
            }

            StringBuilder desc = new StringBuilder();
            desc.append(String.format("原始分值：%.2f", originalScore));

            if (coefficient != null && coefficient != 1.0) {
                desc.append(String.format("，系数：%.2f", coefficient));
            }

            if (baseScore != null && baseScore > 0) {
                desc.append(String.format("，基础分：%d", baseScore));
            }

            if (formula != null && !formula.trim().isEmpty()) {
                desc.append(String.format("，公式：%s", formula));
            }

            desc.append(String.format("，最终分值：%.2f", finalScore));

            return desc.toString();
        }
    }
}
