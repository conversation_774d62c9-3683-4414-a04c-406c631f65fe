package com.trs.police.subject.domain.vo;

import com.trs.police.common.core.vo.GeometryVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 重点区域vo
 *
 * <AUTHOR>
 * @date 2025/5/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImportantAreaVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 区域名称
     */
    private String name;

    /**
     * 区域类型
     */
    private String type;

    /**
     * 活动人员数量
     */
    private Long activityPersonCount;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 画图信息
     */
    private GeometryVO[] geometries;
}
