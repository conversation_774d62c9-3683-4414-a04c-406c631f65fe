package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.subject.domain.dto.FkStatisticsDTO;
import com.trs.police.subject.domain.entity.WarningFkrxyjEntity;
import com.trs.police.subject.domain.vo.FkWarningModalVO;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * fk预警持久层
 *
 * <AUTHOR>
 * @date 2024/06/27
 */
@Mapper
public interface FkWarningMapper extends BaseMapper<WarningFkrxyjEntity> {

    /**
     * 流入人员的数据计
     *
     * @param dto 入参
     * @return 统计结果
     */
    List<FkWarningModalVO> influxFkWarningModalStat(@Param("dto") FkStatisticsDTO dto);

    /**
     * 在档fk预警统计
     *
     * @param dto 入参
     * @return 返回值
     */
    FkWarningModalVO onRecordFkWarningModalStat(@Param("dto") FkStatisticsDTO dto);

    /**
     * 根据idCard查询轨迹
     *
     * @param idCardList idcard
     * @param gjlx       gjlx
     * @param page       page
     * @return 返回值
     */
    Page<PersonTrackVO> fkPersonTrackListByIdCards(@Param("idCardList") List<String> idCardList,
                                                   @Param("gjlx") String gjlx,
                                                   Page<PersonTrackVO> page);

    /**
     * 根据人员id获取研判结果
     *
     * @param personId personId
     * @return 数量
     */
    Integer getPersonJudge(@Param("personId") Long personId);

    /**
     * 根据idCard查询轨迹
     *
     * @param idCardList idcard
     * @return 返回值
     */
    List<PersonTrackVO> findWarningInfoByIdCard(@Param("idCardList") List<String> idCardList);
}
