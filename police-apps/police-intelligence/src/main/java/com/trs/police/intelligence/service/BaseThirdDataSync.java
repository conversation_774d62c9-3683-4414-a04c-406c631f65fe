package com.trs.police.intelligence.service;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.intelligence.dto.third.PullDataDTO;
import com.trs.police.intelligence.entity.ProvinceClueFailReasonEntity;
import com.trs.police.intelligence.mapper.ProvinceClueFailReasonMapper;
import com.trs.police.intelligence.properties.ProvinceClueProperties;
import com.trs.police.intelligence.utils.ProvinceClueHelper;
import com.trs.police.intelligence.vo.third.PullDataVo;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.builder.util.KeyMgrFactory;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/25 10:01
 * @since 1.0
 */
@Slf4j
public abstract class BaseThirdDataSync implements IThirdDataSync {

    public static final String THIRD_DATA_SYNC_TO_MODULE = "intelligence.st.data.pull.to";

    @Autowired(required = false)
    @Getter
    protected ProvinceClueProperties provinceClueProperties;

    @Autowired(required = false)
    protected ProvinceClueHelper clueHelper;

    @Resource
    private ProvinceClueFailReasonMapper provinceClueFailReasonMapper;

    /**
     * needSync<BR>
     *
     * @param module 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/25 10:07
     */
    public Boolean canAction(String module) {
        if (StringUtils.isEmpty(module)) {
            return Boolean.FALSE;
        }
        String config = BeanFactoryHolder.getEnv().getProperty(THIRD_DATA_SYNC_TO_MODULE);
        if (StringUtils.isEmpty(config)) {
            return Boolean.FALSE;
        }
        return Objects.equals(module, config);
    }

    @Override
    public void syncData(String taskId) throws ServiceException {
        if (Objects.isNull(getProvinceClueProperties())) {
            return;
        }
        final Integer dateBeforeDay = getProvinceClueProperties().getDateBeforeDay();
        if (Objects.isNull(dateBeforeDay)) {
            throw new ServiceException("省厅线索同步-未配置同步数据时间范围！");
        }
        final String startTime = TimeUtils.dateBefOrAft(dateBeforeDay, TimeUtils.YYYYMMDD_HHMMSS);
        syncData(taskId, startTime, null);
    }

    @Override
    public void syncData(String taskId, String startTime, String endTime) throws ServiceException {
        String module = BeanFactoryHolder.getEnv().getProperty(THIRD_DATA_SYNC_TO_MODULE);
        if (StringUtils.isEmpty(module)) {
            log.info("[{}]未开启同步行为", taskId);
            return;
        }
        log.info("[{}]开始同步数据到模块：{}", taskId, module);
        try {
            var opt = Optional.ofNullable(KeyMgrFactory.findMgrByKeyNew(
                    BaseIntelligenceEntityService.class,
                    it -> Objects.equals(it.module(), module)
            ));
            if (opt.isPresent()) {
                if (Objects.isNull(getProvinceClueProperties())) {
                    log.warn("[{}]配置为空，跳过", taskId);
                    return;
                }
                final CurrentUser user = clueHelper.getSpecificConsumerThirdUser();
                final CurrentUser provinceUser = clueHelper.getSpecificProvinceUser();
                PullDataDTO pullDataDTO = new PullDataDTO();
                pullDataDTO.setTaskId(taskId);
                if (StringUtils.isNotEmpty(startTime)) {
                    pullDataDTO.setKssj(startTime);
                }
                if (StringUtils.isNotEmpty(endTime)) {
                    pullDataDTO.setJssj(endTime);
                }
                pullDataDTO.setMyts(9999);
                final var service = opt.get();
                for (PullDataVo pullDataVo : pullData(pullDataDTO)) {
                    try {
                        if (Objects.isNull(pullDataVo.getClueVO())) {
                            log.error("[{}]未获取到线索数据，跳过", taskId);
                            continue;
                        }
                        log.info("[{}]同步数据[{}]到模块：{}", taskId, pullDataVo.getClueVO().getXsbh(), module);
                        service.consumerThirdData(pullDataVo, user, provinceUser);
                        log.info("[{}]完成同步数据[{}]到模块：{}", taskId, pullDataVo.getClueVO().getXsbh(), module);
                    } catch (Exception e) {
                        log.error("[{}]同步数据[{}]到模块：{}，出现异常，ERR=[{}]", taskId, pullDataVo.getClueVO().getXsbh(), module, e.getMessage(), e);
                        log.error("[{}]同步数据[{}]到模块：{}，出现异常，原始数据:{}", taskId, pullDataVo.getClueVO().getXsbh(), module, JsonUtil.toJsonString(pullDataVo));
                        if (StringUtils.isNotEmpty(pullDataVo.getClueVO().getXsbh())) {
                            Try.run(() -> {
                                String stxsbh = pullDataVo.getClueVO().getXsbh();
                                ProvinceClueFailReasonEntity entity = new LambdaQueryChainWrapper<>(provinceClueFailReasonMapper)
                                        .eq(ProvinceClueFailReasonEntity::getStxsbh, stxsbh)
                                        .oneOpt()
                                        .orElseGet(() -> ProvinceClueFailReasonEntity.of(taskId, stxsbh));
                                entity.setStxsbh(stxsbh);
                                entity.setTaskId(StringUtils.showEmpty(taskId, "未知"));
                                entity.setUpdateTime(new Date());
                                entity.setReasonContent(StringUtils.showEmpty(e.getMessage(), "空指针异常"));
                                if (Objects.isNull(entity.getDataId()) || entity.getDataId() <= 0L) {
                                    provinceClueFailReasonMapper.insert(entity);
                                } else {
                                    provinceClueFailReasonMapper.updateById(entity);
                                }
                            });
                        }
                    }
                }
                service.finishedConsumerThirdData(pullDataDTO, user, provinceUser);
            }
        } catch (Exception e) {
            log.error("[{}]同步数据到模块：{}，出现异常！ERR=[{}]", taskId, module, e.getMessage(), e);
            throw new ServiceException("同步数据到模块：" + module + "，出现异常！", e);
        }
        log.info("[{}]结束同步数据到模块：{}", taskId, module);
    }

}
