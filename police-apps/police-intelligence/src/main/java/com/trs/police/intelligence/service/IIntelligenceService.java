package com.trs.police.intelligence.service;

import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.message.WebsocketMessageVO;
import com.trs.police.intelligence.dto.*;
import com.trs.police.intelligence.vo.*;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.web.builder.base.RestfulResultsV2;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * IIntelligenceService
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 18:29
 * @since 1.0
 */
public interface IIntelligenceService {

    /**
     * dataTypes<BR>
     *
     * @param path 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:29
     */
    List<AttributeTemplatesVo> dataTypes(String path) throws ServiceException;

    /**
     * attributeTemplates<BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:31
     */
    List<AttributeTemplatesVo> attributeTemplates(String path, AttributeTemplatesDTO dto) throws ServiceException;

    /**
     * saveOrUpdate<BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 09:10
     */
    Report<String> saveOrUpdate(String path, BaseSaveDTO dto) throws ServiceException;

    /**
     * parseExportAttributeItem<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/26 13:23
     */
    List<AttributeItemExportVo> parseExportAttributeItem(ExportParseDTO dto) throws ServiceException;

    /**
     * addExtInfoToReport<BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 09:18
     */
    Report<String> addExtInfo(String path, BaseSaveDTO dto) throws ServiceException;

    /**
     * addExtInfoToReport<BR>
     *
     * @param path      参数
     * @param dataClass 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 09:18
     */
    Integer getMaxNo(String path, String dataClass) throws ServiceException;

    /**
     * getOrderNo<BR>
     *
     * @param path      参数
     * @param dataClass 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 11:50
     */
    String getOrderNo(String path, String dataClass) throws ServiceException;

    /**
     * dataList<BR>
     *
     * @param path    参数
     * @param from    参数
     * @param request 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:57
     */
    RestfulResultsV2 dataList(String path, String from, ListParamsRequest request);

    /**
     * categoryList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/16 19:39
     */
    List<CountItem> categoryList(CategorySearchDto dto) throws ServiceException;

    /**
     * todoList<BR>
     *
     * @param path       参数
     * @param pageParams 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/22 19:47
     */
    PageResult<TodoTaskVO> todoList(String path, PageParams pageParams);

    /**
     * getUnReadNum<BR>
     *
     * @param path 参数
     * @param from 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/18 23:37
     */
    List<GroupVo> getUnReadNum(String path, String from) throws ServiceException;

    /**
     * detail<BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/14 23:51
     */
    BaseIntelligenceEntityVo detail(
            String path,
            EntityDetailDTO dto
    ) throws ServiceException;

    /**
     * messageList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 16:15
     */
    List<WebsocketMessageVO> messageList(MessageQueryDTO dto) throws ServiceException;

    /**
     * getMessageReply<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/18 10:09
     */
    List<WebsocketMessageVO> getMessageReply(MessageQueryDTO dto) throws ServiceException;

    /**
     * logList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 18:40
     */
    RestfulResultsV2<ActionLogVo> logList(LogQueryDTO dto);

    /**
     * logListGroupByDept<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/13 23:03
     */
    RestfulResultsV2<ActionLogGroupByDeptVo> logListGroupByDept(LogQueryDTO dto);

    /**
     * markMessageRead<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/17 00:24
     */
    Report<String> markMessageRead(MarkMessageReadDTO dto) throws ServiceException;

    /**
     * exportLog<BR>
     *
     * @param response 参数
     * @param dto      参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 16:34
     */
    void exportLog(HttpServletResponse response, LogExportDTO dto) throws ServiceException;

    /**
     * export<BR>
     *
     * @param response 参数
     * @param dto      参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/22 10:51
     */
    void export(HttpServletResponse response, ExportDTO dto) throws ServiceException;

    /**
     * baseAttributeList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/7 12:22
     */
    List<AttributeTypeVo> baseAttributeList(AttributeTypeDTO dto) throws ServiceException;

    /**
     * listBar<BR>
     *
     * @param type 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    RestfulResultsV2 listBar(String type) throws ServiceException;

    /**
     * xiansuoRelatedPersonList<BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/8 19:53
     */
    List<? extends BaseEntityVo> relatedPersonList(String path, RelatedPersonQueryDTO dto) throws ServiceException;

    /**
     * xiansuoSimpleList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 17:40
     */
    List<XianSuoEntityWithRepeatPersonVo> xiansuoSimpleList(XianSuoSimpleListDTO dto) throws ServiceException;

    /**
     * xiansuoPersonLibrary<BR>
     *
     * @param request 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/10 14:19
     */
    RestfulResultsV2<XianSuoPersonLibraryVo> xiansuoPersonLibrary(ListParamsRequest request) throws ServiceException;

    /**
     * labelList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    RestfulResultsV2<CommonLabelVO> labelList(LabelListDTO dto) throws ServiceException;

    /**
     * saveOrUpdateCategory<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    Report<String> saveOrUpdateCategory(SaveOrEditCategoryDTO dto) throws ServiceException;

    /**
     * saveOrUpdateLabel<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    Report<String> saveOrUpdateLabel(SaveOrEditLabelDTO dto) throws ServiceException;

    /**
     * removeCategory<BR>
     *
     * @param type 参数
     * @param id   参数
     * @return 结果
     * @throws ServiceException 异常
     */
    Report<String> removeCategory(String type, Long id) throws ServiceException;

    /**
     * removeLabel<BR>
     *
     * @param type    参数
     * @param dataIds 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    Report<String> removeLabel(String type, String dataIds) throws ServiceException;

    /**
     * enableLabel<BR>
     *
     * @param type    参数
     * @param dataIds 参数
     * @param enable  启用/禁用
     * @return 结果
     * @throws ServiceException 异常
     */
    Report<String> enableLabel(String type, String dataIds, Integer enable) throws ServiceException;

    /**
     * yuqingVersionData<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/15 17:01
     */
    RestfulResultsV2<YuQingVersionDataVo> yuqingVersionData(YuQingVersionDataDTO dto);

    /**
     * renwuSheAnXinXiList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:05
     */
    RestfulResultsV2<RenWuDaShuJuHeChaAnJianVo> renwuSheAnXinXiList(RenWuDaShuJuHeChaQueryActionDTO dto);

    /**
     * renwuSheAnXinXiDetail<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:05
     */
    RenWuDaShuJuHeChaAnJianVo renwuSheAnXinXiDetail(RenWuDaShuJuHeChaQueryActionDTO dto) throws ServiceException;

    /**
     * renwuJiaoTongWeiFaList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:05
     */
    RestfulResultsV2<RenWuDaShuJuHeChaJiaoTongWeiFaVo> renwuJiaoTongWeiFaList(RenWuDaShuJuHeChaQueryActionDTO dto);

    /**
     * @param dataId 主键
     * @param flag   置顶/取消标识位
     * @return {@link String }
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-09-09 16:53:46
     */
    String topOrCancel(Long dataId, Boolean flag);

    /**
     * 任务关联群体
     *
     * @param dataId         任务数据id
     * @param relatedGroupId 群体ID
     * @return 结果
     * @throws ServiceException 异常
     */
    String associateGroup(Long dataId, Long relatedGroupId) throws ServiceException;
}
