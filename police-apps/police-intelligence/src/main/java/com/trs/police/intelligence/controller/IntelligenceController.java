package com.trs.police.intelligence.controller;

import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.message.WebsocketMessageVO;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.dto.*;
import com.trs.police.intelligence.service.IIntelligenceService;
import com.trs.police.intelligence.vo.*;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

import static com.trs.police.common.core.utils.JsonUtil.OBJECT_MAPPER;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * IntelligenceController
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 17:56
 * @since 1.0
 */
@RestController
@AllArgsConstructor
@Slf4j
public class IntelligenceController {

    private final IIntelligenceService service;

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48370">要情，指令，通知等字段属性模板</a><BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @GetMapping("{path}/attributeTemplates")
    public RestfulResultsV2<AttributeTemplatesVo> attributeTemplates(
            @PathVariable String path,
            AttributeTemplatesDTO dto
    ) {
        return RestfulResultsV2.checkedBuild(() -> service.attributeTemplates(path, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48568">要情，指令，通知等类型列表</a><BR>
     *
     * @param path 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @GetMapping("{path}/dataTypes")
    public RestfulResultsV2<AttributeTemplatesVo> dataTypes(@PathVariable String path) {
        return RestfulResultsV2.checkedBuild(() -> service.dataTypes(path));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48394">要情，指令，通知等获取目前最大编号</a><BR>
     *
     * @param path      参数
     * @param dataClass 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @GetMapping("{path}/getMaxNo")
    public RestfulResultsV2<Integer> getMaxNo(
            @PathVariable String path,
            String dataClass
    ) {
        return RestfulResultsV2.checkedBuild(() -> service.getMaxNo(path, dataClass));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48856">获取指令号</a><BR>
     *
     * @param path      参数
     * @param dataClass 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @GetMapping("{path}/getOrderNo")
    public RestfulResultsV2<String> getOrderNo(
            @PathVariable String path,
            String dataClass
    ) {
        return RestfulResultsV2.checkedBuild(() -> service.getOrderNo(path, dataClass));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48376">新建/编辑要情</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("yaoqing/saveOrUpdate")
    public RestfulResultsV2<Report<String>> yaoqingSaveOrUpdate(YaoQingSaveDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.saveOrUpdate(Constants.YAOQING, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48382">新建/编辑指令</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("zhiling/saveOrUpdate")
    public RestfulResultsV2<Report<String>> zhilingSaveOrUpdate(ZhiLingSaveDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.saveOrUpdate(Constants.ZHILING, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48388">新建/编辑指令</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("tongzhitongbao/saveOrUpdate")
    public RestfulResultsV2<Report<String>> tongzhitongbaoSaveOrUpdate(TongZhiTongBaoSaveDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.saveOrUpdate(Constants.TONGZHITONGBAO, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48430">续报/终报</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("yaoqing/addExtInfoToReport")
    public RestfulResultsV2<Report<String>> yaoqingAddExtInfoToReport(YaoQingSaveDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.addExtInfo(Constants.YAOQING, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48754">指令修订</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("zhiling/addExtInfo")
    public RestfulResultsV2<Report<String>> zhilingAddExtInfoToReport(ZhiLingSaveDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.addExtInfo(Constants.ZHILING, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/9764">线索统计</a><BR>
     *
     * @param path       参数
     * @param from       参数
     * @param request    参数
     * @param groupField 参数
     * @param dateFormat 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("{path}/categoryList/{from}")
    public RestfulResultsV2<CountItem> categoryList(
            @PathVariable String path,
            @PathVariable String from,
            @RequestParam(value = "groupField", defaultValue = "cr_time") String groupField,
            @RequestParam(value = "dateFormat", defaultValue = "day") String dateFormat,
            @RequestBody ListParamsRequest request
    ) {
        return RestfulResultsV2.checkedBuild(() -> {
            CategorySearchDto dto = new CategorySearchDto();
            dto.setPath(path);
            dto.setFrom(from);
            dto.setGroupField(groupField);
            dto.setDateFormat(dateFormat);
            dto.setRequest(request);
            return service.categoryList(dto);
        });
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48358">列表</a><BR>
     *
     * @param path    参数
     * @param from    参数
     * @param request 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("{path}/dataList/{from}")
    public RestfulResultsV2 dataList(
            @PathVariable String path,
            @PathVariable String from,
            @RequestBody ListParamsRequest request
    ) {
        return service.dataList(path, from, request);
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49244">标签列表接口</a><BR>
     *
     * @param type 参数
     * @param dto  参数
     * @return 结果
     */
    @GetMapping("resource/{type}/dataList")
    public RestfulResultsV2<CommonLabelVO> dataList(@PathVariable("type") String type, LabelListDTO dto) {
        return Try.of(() -> {
                    if (Constants.CLUEPERSON.equals(type)) {
                        dto.setType(Constants.CLUE_PERSON);
                    } else {
                        dto.setType(type);
                    }
                    return service.labelList(dto);
                }).onFailure(e -> log.error("查询标签异常", e))
                .getOrElseGet(e -> RestfulResultsV2.error("查询标签异常：" + e.getMessage()));
    }

    /**
     * 待办列表
     *
     * @param path       参数
     * @param pageParams 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("{path}/todoList")
    public PageResult<TodoTaskVO> todoList(
            @PathVariable String path,
            @RequestBody PageParams pageParams
    ) {
        return service.todoList(path, pageParams);
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48826">要情，指令，通知通报未读记录</a><BR>
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/1576">舆情未读记录</a><BR>
     *
     * @param path 参数
     * @param from 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @GetMapping("{path}/getUnReadNum/{from}")
    public RestfulResultsV2<GroupVo> getUnReadNum(
            @PathVariable String path,
            @PathVariable String from
    ) {
        return RestfulResultsV2.checkedBuild(() -> service.getUnReadNum(path, from));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48364">细览</a><BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @GetMapping("{path}/detail")
    public RestfulResultsV2<BaseIntelligenceEntityVo> detail(
            @PathVariable String path,
            EntityDetailDTO dto
    ) {
        return RestfulResultsV2.checkedBuild(() -> service.detail(path, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48472">会话中消息列表</a><BR>
     *
     * @param path   参数
     * @param chatId 参数
     * @param dto    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 16:13
     */
    @GetMapping("/{path}/{chatId}/messageList")
    public RestfulResultsV2<WebsocketMessageVO> messageList(
            @PathVariable("path") String path,
            @PathVariable("chatId") Long chatId,
            MessageQueryDTO dto
    ) {
        return RestfulResultsV2.checkedBuild(() -> {
            dto.setPath(path);
            dto.setChatId(chatId);
            dto.isValid();
            return service.messageList(dto);
        });
    }


    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48448">要情，指令，通知等流转记录</a><BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 20:54
     */
    @RequestMapping(
            value = "/{path}/logList",
            method = {
                    RequestMethod.GET,
                    RequestMethod.POST
            }
    )
    public RestfulResultsV2<ActionLogVo> logList(@PathVariable("path") String path, LogQueryDTO dto) {
        dto.setObjType(path);
        return service.logList(dto);
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/78">任务下相关人员工作记录（按照单位分组）</a><BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 20:54
     */
    @RequestMapping(
            value = "/{path}/logListGroupByDept",
            method = {
                    RequestMethod.GET,
                    RequestMethod.POST
            }
    )
    public RestfulResultsV2<ActionLogGroupByDeptVo> logListGroupByDept(@PathVariable("path") String path, LogQueryDTO dto) {
        dto.setObjType(path);
        return service.logListGroupByDept(dto);
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48682">批量标记消息已读</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/17 00:23
     */
    @PostMapping("markMessageRead")
    public RestfulResultsV2<Report<String>> markMessageRead(MarkMessageReadDTO dto) {
        return RestfulResultsV2
                .checkedBuild(() -> service.markMessageRead(dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48838">流转记录导出</a><BR>
     *
     * @param path     参数
     * @param dto      参数
     * @param response 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 01:11
     */
    @PostMapping("/{path}/exportLog")
    public void exportLog(
            @PathVariable("path") String path,
            LogExportDTO dto,
            HttpServletResponse response
    ) throws Exception {
        try {
            dto.setObjType(path);
            service.exportLog(response, dto);
        } catch (Exception e) {
            response.setContentType("application/json;charset=UTF-8");
            try (final PrintWriter writer = response.getWriter()) {
                ResponseMessage message = ResponseMessage.error(e.getMessage());
                String text = OBJECT_MAPPER.writeValueAsString(message);
                writer.write(text);
                log.error("导出流转记录出错", e);
            } catch (Exception ex) {
                log.error("导出流转记录出错！", ex);
                throw ex;
            }
        }
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/48466">要情，指令，通知等导出</a><BR>
     *
     * @param path     参数
     * @param dto      参数
     * @param response 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 01:11
     */
    @PostMapping("/{path}/export")
    public void export(
            @PathVariable("path") String path,
            ExportDTO dto,
            HttpServletResponse response
    ) throws Exception {
        try {
            dto.setObjType(path);
            switch (path) {
                case Constants.YAOQING:
                    dto.setActionEnum(ActionEnum.YAO_QING_EXPORT_DOC);
                    break;
                case Constants.ZHILING:
                    dto.setActionEnum(ActionEnum.ZHILING_EXPORT_DOC);
                    break;
                case Constants.TONGZHITONGBAO:
                    dto.setActionEnum(ActionEnum.TONGZHITONGBAO_EXPORT_DOC);
                    break;
                case Constants.YUQING:
                    dto.setActionEnum(ActionEnum.YUQING_EXPORT_DOC);
                    break;
                case Constants.XIANSUO:
                    dto.setActionEnum(ActionEnum.XIANSUO_EXPORT_DOC);
                    break;
                default:
                    throw new ServiceException("暂不支持");
            }
            service.export(response, dto);
        } catch (Exception e) {
            response.setContentType("application/json;charset=UTF-8");
            try (final PrintWriter writer = response.getWriter()) {
                ResponseMessage message = ResponseMessage.error(e.getMessage());
                String text = OBJECT_MAPPER.writeValueAsString(message);
                writer.write(text);
                log.error("导出出错!", e);
            } catch (Exception ex) {
                log.error("导出出错！", ex);
                throw ex;
            }
        }
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49234">线索填报的基础属性列表</a><BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/7 12:23
     */
    @GetMapping("/{path}/baseAttributeList")
    public RestfulResultsV2<AttributeTypeVo> baseAttributeList(
            @PathVariable("path") String path,
            AttributeTypeDTO dto
    ) {
        return RestfulResultsV2.checkedBuild(() -> {
            dto.setPath(path);
            return service.baseAttributeList(dto);
        });
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/478">预览时获取导出的属性结果</a><BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/7 12:23
     */
    @PostMapping("/{path}/parseExportAttributeItem")
    public RestfulResultsV2<AttributeItemExportVo> parseExportAttributeItem(
            @PathVariable("path") String path,
            ExportParseDTO dto
    ) {
        return RestfulResultsV2.checkedBuild(() -> {
            dto.setPath(path);
            return service.parseExportAttributeItem(dto);
        });
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49206">人员布控列表接口</a><BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @GetMapping("/{path}/relatedPersonList")
    public RestfulResultsV2<? extends BaseEntityVo> relatedPersonList(
            @PathVariable("path") String path,
            RelatedPersonQueryDTO dto
    ) {
        return RestfulResultsV2.checkedBuild(() -> service.relatedPersonList(path, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49236">分类列表接口</a><BR>
     *
     * @param type 线索人员标签类型
     * @return 结果
     */
    @GetMapping("resource/category/listBar")
    public RestfulResultsV2 listBar(String type) {
        return Try.of(() -> service.listBar(type))
                .onFailure(e -> log.error("获取分类出错", e))
                .getOrElseGet(e -> RestfulResultsV2.error("获取分类出错：" + e.getMessage()));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49642">新建任务</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("renwu/saveOrUpdate")
    public RestfulResultsV2<Report<String>> renWuSaveOrUpdate(RenWuSaveDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.saveOrUpdate(Constants.RENWU, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/2892">获取任务中相关人员的涉案信息列表</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 19:42
     */
    @PostMapping("renwu/sheAnXinXiList")
    public RestfulResultsV2<RenWuDaShuJuHeChaAnJianVo> renwuSheAnXinXiList(RenWuDaShuJuHeChaQueryActionDTO dto) {
        return service.renwuSheAnXinXiList(dto);
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/2908">获取任务中相关人员的涉案信息详情</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 19:42
     */
    @PostMapping("renwu/sheAnXinXiDetail")
    public RestfulResultsV2<RenWuDaShuJuHeChaAnJianVo> renwuSheAnXinXiDetail(RenWuDaShuJuHeChaQueryActionDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.renwuSheAnXinXiDetail(dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/2916">获取任务中相关人员的交通违法信息列表</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 19:42
     */
    @PostMapping("renwu/jiaoTongWeiFaList")
    public RestfulResultsV2<RenWuDaShuJuHeChaJiaoTongWeiFaVo> renwuJiaoTongWeiFaList(RenWuDaShuJuHeChaQueryActionDTO dto) {
        return service.renwuJiaoTongWeiFaList(dto);
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49150">新建/编辑线索</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("xiansuo/saveOrUpdate")
    public RestfulResultsV2<Report<String>> xiansuoSaveOrUpdate(XianSuoSaveDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.saveOrUpdate(Constants.XIANSUO, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49202">
     * 根据线索ID串查询线索标题跟编号接口（用于已合并线索跟重复线索列表）
     * </a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @RequestMapping(
            value = "xiansuo/simpleList",
            method = {
                    RequestMethod.GET,
                    RequestMethod.POST
            }
    )
    public RestfulResultsV2<XianSuoEntityWithRepeatPersonVo> xiansuoSimpleList(XianSuoSimpleListDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.xiansuoSimpleList(dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49202">
     * 根据线索ID串查询线索标题跟编号接口（用于已合并线索跟重复线索列表）
     * </a><BR>
     *
     * @param request 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @PostMapping("xiansuo/personLibrary")
    public RestfulResultsV2<XianSuoPersonLibraryVo> xiansuoPersonLibrary(@RequestBody ListParamsRequest request) {
        return Try.of(() -> service.xiansuoPersonLibrary(request))
                .onFailure(e -> log.error("查询线索人员库异常", e))
                .getOrElseGet(e -> RestfulResultsV2.error("查询线索人员库异常：" + e.getMessage()));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49238">新增或编辑分类</a><BR>
     *
     * @param dto 参数
     * @return 结果
     */
    @PostMapping("resource/category/saveOrEdit")
    public RestfulResultsV2<Report<String>> saveOrUpdate(SaveOrEditCategoryDTO dto) {
        return RestfulResultsV2
                .checkedBuild(() -> service.saveOrUpdateCategory(dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49242">新增或编辑标签</a><BR>
     *
     * @param type 参数
     * @param dto  参数
     * @return 结果
     */
    @PostMapping("resource/{type}/saveOrEdit")
    public RestfulResultsV2<Report<String>> saveOrUpdate(@PathVariable("type") String type, SaveOrEditLabelDTO dto) {
        return RestfulResultsV2
                .checkedBuild(() -> {
                    if (Constants.CLUEPERSON.equals(type)) {
                        dto.setType(Constants.CLUE_PERSON);
                    } else {
                        dto.setType(type);
                    }
                    return service.saveOrUpdateLabel(dto);
                });
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49240">删除分类</a><BR>
     *
     * @param type 参数
     * @param id   参数
     * @return 结果
     */
    @PostMapping("resource/category/remove")
    public RestfulResultsV2<Report<String>> remove(String type, Long id) {
        return RestfulResultsV2
                .checkedBuild(() -> service.removeCategory(type, id));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49246">删除标签</a><BR>
     *
     * @param type    参数
     * @param dataIds 参数
     * @return 结果
     */
    @PostMapping("resource/{type}/remove")
    public RestfulResultsV2<Report<String>> remove(@PathVariable("type") String type, String dataIds) {
        return RestfulResultsV2
                .checkedBuild(() -> service.removeLabel(type, dataIds));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/49248">停用/启用标签</a><BR>
     *
     * @param type    参数
     * @param dataIds 参数
     * @param enable  参数
     * @return 结果
     */
    @PostMapping("resource/{type}/enable")
    public RestfulResultsV2<Report<String>> enable(@PathVariable("type") String type, String dataIds, Integer enable) {
        return RestfulResultsV2
                .checkedBuild(() -> service.enableLabel(type, dataIds, enable));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/1500">新增/编辑舆情</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/08/14 14:36:21
     */
    @PostMapping("yuqing/saveOrUpdate")
    public RestfulResultsV2<Report<String>> yuqingSaveOrUpdate(YuQingSaveDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.saveOrUpdate(Constants.YUQING, dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/1516">舆情内容版本列表</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:01
     */
    @GetMapping("yuqing/versionData")
    public RestfulResultsV2<YuQingVersionDataVo> yuqingVersionData(YuQingVersionDataDTO dto) {
        return service.yuqingVersionData(dto);
    }

    /**
     * 指令置顶相关操作
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/2100">置顶/取消置顶</a>
     *
     * @param dataId 数据id
     * @param flag   是否置顶
     * @return {@link RestfulResultsV2 }<{@link String }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-09-09 17:29:26
     */
    @PostMapping("zhiling/topOrCancel")
    public RestfulResultsV2<String> topOrCancel(Long dataId, Boolean flag) {
        return RestfulResultsV2.checkedBuild(() -> service.topOrCancel(dataId, flag));
    }

    /**
     * 任务关联上指定群体
     * <a href="https://trsyapi.trscd.com.cn/project/432/interface/api/10598">关联上指定群体</a>
     *
     * @param dataId         任务数据id
     * @param relatedGroupId 关联的群体ID
     * @return 结果
     */
    @PostMapping("renwu/associateGroup")
    public RestfulResultsV2<String> associateGroup(Long dataId, Long relatedGroupId) {
        return RestfulResultsV2.checkedBuild(() -> service.associateGroup(dataId, relatedGroupId));
    }
}
