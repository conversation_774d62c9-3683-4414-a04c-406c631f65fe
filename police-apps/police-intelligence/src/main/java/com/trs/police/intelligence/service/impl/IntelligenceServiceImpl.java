package com.trs.police.intelligence.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.util.PoitlIOUtils;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.Dept;
import com.trs.police.common.core.mapper.DeptMapper;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.PageUtils;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.message.WebsocketMessageVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.dto.*;
import com.trs.police.intelligence.entity.*;
import com.trs.police.intelligence.mapper.*;
import com.trs.police.intelligence.mgr.AttributeTemplatesMgr;
import com.trs.police.intelligence.service.BaseAction;
import com.trs.police.intelligence.service.BaseIntelligenceEntityService;
import com.trs.police.intelligence.service.IIntelligenceService;
import com.trs.police.intelligence.service.IMessageService;
import com.trs.police.intelligence.utils.FieldUtils;
import com.trs.police.intelligence.utils.WordUtils;
import com.trs.police.intelligence.vo.*;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.constant.CommonConstants;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.builder.util.KeyMgrFactory;
import com.trs.web.restful.REQUEST_STATUS_CODE;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * IntelligenceServiceImpl
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 18:02
 * @since 1.0
 */
@Service
@AllArgsConstructor
@Slf4j
public class IntelligenceServiceImpl implements IIntelligenceService {

    private static CommonCategoryVO defaultCategory;

    private final AttributeTemplatesMgr attributeTemplatesMgr;

    private final IMessageService messageService;

    private final ActionLogMapper logMapper;

    private final CommonCategoryMapper categoryMapper;

    private final CommonLabelMapper labelMapper;

    private final XianSuoPersonLibraryMapper libraryMapper;

    private final XianSuoRelatedPersonMapper personMapper;

    private final DeptMapper deptMapper;

    private final PermissionService permissionService;

    private final ZhiLingBaseInfoMapper zhiLingBaseInfoMapper;

    private final RenWuBaseInfoMapper renWuBaseInfoMapper;

    private final RenWuRelatedPersonMapper renWuRelatedPersonMapper;

    private final ProfileService profileService;

    /**
     * dataTypes<BR>
     *
     * @param path 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:28
     */
    @Override
    public List<AttributeTemplatesVo> dataTypes(String path) throws ServiceException {
        PreConditionCheck.checkNotEmpty(path, new ParamInvalidException("异常"));
        return attributeTemplatesMgr.dataTypes(path);
    }

    /**
     * attributeTemplates<BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 18:28
     */
    @Override
    public List<AttributeTemplatesVo> attributeTemplates(String path, AttributeTemplatesDTO dto)
            throws ServiceException {
        PreConditionCheck.checkNotEmpty(path, new ParamInvalidException("异常"));
        dto.isValid();
        return attributeTemplatesMgr.attributeTemplates(path, dto);
    }

    /**
     * saveOrUpdate<BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 09:10
     */
    @Override
    public Report<String> saveOrUpdate(String path, BaseSaveDTO dto) throws ServiceException {
        var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, path, true);
        return base.saveOrUpdate(dto);
    }

    /**
     * parseExportAttributeItem<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/26 13:23
     */
    @Override
    public List<AttributeItemExportVo> parseExportAttributeItem(ExportParseDTO dto) throws ServiceException {
        dto.isValid();
        var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, dto.getPath(), true);
        AttributeTemplatesVo templatesVo = base.findAttributeTemplates(dto.getDataType(), dto.getDataClass());
        if (templatesVo == null) {
            return Collections.emptyList();
        }
        return base.parseExportAttributeItem(
                dto.getDataType(),
                dto.getDataClass(),
                FieldUtils.getAttributes(templatesVo, dto.makeDbMap(templatesVo))
        );
    }

    /**
     * addExtInfoToReport<BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 09:18
     */
    @Override
    public Report<String> addExtInfo(String path, BaseSaveDTO dto) throws ServiceException {
        AddLogDTO log = new AddLogDTO();
        switch (path) {
            case Constants.YAOQING:
                log.setActionEnum(ActionEnum.YAO_QING_ADD_EXT_INFO);
                break;
            case Constants.ZHILING:
                log.setActionEnum(ActionEnum.ZHILING_ADD_EXT_INFO);
                break;
            default:
                throw new ServiceException("非法请求");
        }
        PreConditionCheck.checkArgument(
                Objects.equals(Constants.VERSION_FLAG_XUBAO_OR_XIUDING, dto.getVersionFlag())
                        || Objects.equals(Constants.VERSION_FLAG_ZHONGBAO, dto.getVersionFlag()),
                new ParamInvalidException("版本标识传参错误")
        );
        Report<String> report = saveOrUpdate(path, dto);
        log.setDataId(dto.getDataId());
        log.setVersionFlag(dto.getVersionFlag());
        log.setDataTitle(dto.getDataTitle());
        log.setReport(report);
        BaseAction.findAction(log.getActionEnum()).doAction(log);
        return report;
    }

    /**
     * addExtInfoToReport<BR>
     *
     * @param path      参数
     * @param dataClass 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 09:18
     */
    @Override
    public Integer getMaxNo(String path, String dataClass) throws ServiceException {
        var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, path, true);
        return base.getMaxNo(dataClass);
    }

    /**
     * getOrderNo<BR>
     *
     * @param path      参数
     * @param dataClass 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 11:50
     */
    @Override
    public String getOrderNo(String path, String dataClass) throws ServiceException {
        var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, path, true);
        return base.getOrderNo(dataClass);
    }

    /**
     * dataList<BR>
     *
     * @param path    参数
     * @param from    参数
     * @param request 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:57
     */
    @Override
    public RestfulResultsV2 dataList(String path, String from, ListParamsRequest request) {
        return Try.of(() -> {
            var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, path, true);
            return base.queryList(from, request);
        }).onFailure(e -> log.error("数据查询异常", e)).getOrElseGet(e -> RestfulResultsV2.error(e.getMessage()));
    }

    @Override
    public List<CountItem> categoryList(CategorySearchDto dto) throws ServiceException {
        dto.isValid();
        var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, dto.getPath(), true);
        return base.categoryList(dto);
    }

    /**
     * todoList<BR>
     *
     * @param path       参数
     * @param pageParams 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/22 19:47
     */
    @Override
    public PageResult<TodoTaskVO> todoList(String path, PageParams pageParams) {
        return Try.of(() -> {
            var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, path, true);
            return base.todoList(pageParams);
        }).onFailure(e -> log.error("待办数据查询异常", e)).getOrElseGet(e -> PageResult.empty(pageParams));

    }

    /**
     * getUnReadNum<BR>
     *
     * @param path 参数
     * @param from 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/18 23:37
     */
    @Override
    public List<GroupVo> getUnReadNum(String path, String from) throws ServiceException {
        return KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, path, true)
                .getUnReadNum(from);
    }

    /**
     * detail<BR>
     *
     * @param path 参数
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/14 23:51
     */
    @Override
    public BaseIntelligenceEntityVo detail(String path, EntityDetailDTO dto) throws ServiceException {
        var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, path, true);
        return base.detail(dto);
    }

    /**
     * messageList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 16:15
     */
    @Override
    public List<WebsocketMessageVO> messageList(MessageQueryDTO dto) throws ServiceException {
        dto.isValid();
        return messageService.messageList(dto);
    }

    @Override
    public List<WebsocketMessageVO> getMessageReply(MessageQueryDTO dto) throws ServiceException {
        dto.isValid();
        checkNotNull(dto.getMessageId(), new ParamInvalidException("消息的ID不能为空！"));
        return messageService.getMessageReply(dto);
    }

    /**
     * logList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 18:40
     */
    @Override
    public RestfulResultsV2<ActionLogVo> logList(LogQueryDTO dto) {
        return Try.of(() -> {
            dto.isValid();
            var page = logMapper.queryList(
                    new Page<>(dto.getPageNum(), dto.getPageSize()),
                    dto,
                    dto.makeObjIds(),
                    dto.makeObjRootIds(),
                    dto.makeOrder()
                            .stream()
                            .map(it -> String.format("log.%s %s", it.getFieldName(), it.getOrderType()))
                            .collect(Collectors.joining(StringUtils.SEPARATOR_COMMA))
            );
            return RestfulResultsV2.ok(page.getRecords())
                    .addPageNum(dto.getPageNum())
                    .addPageSize(dto.getPageSize())
                    .addTotalCount(page.getTotal());
        }).onFailure(e -> log.error("数据查询异常", e)).getOrElseGet(e -> RestfulResultsV2.error(e.getMessage()));
    }

    /**
     * logListGroupByDept<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/13 23:03
     */
    @Override
    public RestfulResultsV2<ActionLogGroupByDeptVo> logListGroupByDept(LogQueryDTO dto) {
        dto.setPageNum(1);
        dto.setPageSize(9999);
        RestfulResultsV2<ActionLogVo> resultsV2 = logList(dto);
        if (Objects.equals(REQUEST_STATUS_CODE.SUCCESSED.getCode(), resultsV2.getCode())) {
            List<ActionLogVo> logs = resultsV2.getDatas();
            Map<Long, List<ActionLogVo>> map = new HashMap<>(logs.size());
            for (ActionLogVo actionLogVo : logs) {
                List<ActionLogVo> list = map.getOrDefault(actionLogVo.getDeptId(), new ArrayList<>(1));
                list.add(actionLogVo);
                map.put(actionLogVo.getDeptId(), list);
            }
            if (CollectionUtils.isEmpty(map.keySet())) {
                return RestfulResultsV2.ok(Collections.emptyList());
            }
            return RestfulResultsV2.ok(deptMapper.selectBatchIds(map.keySet())
                    .stream()
                    .map(it -> it.toDto().toSimpleVO())
                    .map(it -> new ActionLogGroupByDeptVo(it, map.getOrDefault(it.getDeptId(), Collections.emptyList())))
                    .collect(Collectors.toList()));
        } else {
            return RestfulResultsV2.error("数据查询异常");
        }
    }

    /**
     * markMessageRead<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/17 00:24
     */
    @Override
    public Report<String> markMessageRead(MarkMessageReadDTO dto) throws ServiceException {
        dto.isValid();
        List<Long> chatIds = Arrays.stream(
                        StringUtils.showEmpty(dto.getChatIds()).split(StringUtils.SEPARATOR_COMMA)
                ).filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        List<Long> messageIds = Arrays.stream(
                        StringUtils.showEmpty(dto.getMessageIds()).split(StringUtils.SEPARATOR_COMMA)
                ).filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        messageService.markMessageRead(AuthHelper.getNotNullUser().getId(), chatIds, messageIds);
        return new Report<>("标记已读", "成功操作");
    }

    /**
     * exportLog<BR>
     *
     * @param response 参数
     * @param dto      参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 16:34
     */
    @Override
    public void exportLog(HttpServletResponse response, LogExportDTO dto) throws ServiceException {
        dto.isValid();
        dto.setPageNum(1);
        dto.setPageSize(9999);
        List<ActionLogVo> logs = logList(dto).getDatas();
        if (CollectionUtils.isEmpty(logs)) {
            throw new ServiceException("没有查询到可导出的数据");
        }
        var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, dto.getObjType(), true);
        Long id = dto.makeTargetId();
        BaseIntelligenceEntity entity = base.findById(id);
        PreConditionCheck.checkNotNull(entity, new ServiceException(base.desc() + "对象不存在"));
        List<LogExportVo.Item> items = new ArrayList<>(0);
        int num = 1;
        for (ActionLogVo log : logs) {
            items.add(log.toItem(num++));
        }
        final LogExportVo vo = LogExportVo.builder()
                .title(entity.getDataTitle() + "的" + entity.makeShowName() + "记录")
                .items(items)
                .build();
        try {
            String fileName = StringUtils.showEmpty(dto.getFileName(), vo.getTitle() + ".docx");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            exportLogWord(response, vo, entity);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void exportLogWord(HttpServletResponse response, LogExportVo vo, BaseIntelligenceEntity entity) throws IOException {
        Map<String, String> parmaMap = new HashMap<>();
        Dept dept = deptMapper.selectById(entity.getCrDeptId());
        parmaMap.put("title", vo.getTitle());
        parmaMap.put("dataNo", String.valueOf(entity.getDataNo()));
        parmaMap.put("publishDeptName", dept.getName());
        parmaMap.put("publishPerson", entity.getCrUserTrueName());
        parmaMap.put("publishTime", TimeUtils.dateToString(entity.getCrTime(), TimeUtils.YYYYMMDD_HHMMSS));
        parmaMap.put("exportTime", TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS));
        List<String[]> list = new ArrayList<>();
        vo.getItems().forEach(obj ->
                list.add(
                        new String[]{
                                obj.getCrUserTrueName(),
                                obj.getDeptShortName(),
                                obj.getTime(),
                                obj.getContent()
                        }
                )
        );
        // 获取模板
        InputStream resourceAsStream = Objects.requireNonNull(
                getClass().getClassLoader().getResourceAsStream("templates/qzx.action.log.template.new.docx")
        );
        XWPFDocument document = new XWPFDocument(resourceAsStream);
        WordUtils.buildParamText(document, parmaMap);
        WordUtils.buildWordTableText(document, list, 0);
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        document.write(bos);
        PoitlIOUtils.closeQuietlyMulti(document, bos, out);
    }

    /**
     * export<BR>
     *
     * @param response 参数
     * @param dto      参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/22 10:51
     */
    @Override
    public void export(HttpServletResponse response, ExportDTO dto) throws ServiceException {
        dto.isValid();
        var base = KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, dto.getObjType(), true);
        try {
            Tuple2<String, String> fileNameAndRawName = base.export(dto);
            PreConditionCheck.checkNotEmpty(fileNameAndRawName._2(), new ServiceException("生成导出文件出错！"));
            File file = new File(fileNameAndRawName._2());
            final byte[] bytes = FileUtils.readFileToByteArray(file);
            String fileName = URLEncoder.encode(
                    StringUtils.showEmpty(dto.getFileName(), fileNameAndRawName._1()),
                    StandardCharsets.UTF_8
            );
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(bytes);
            outputStream.flush();
            outputStream.close();
            // 记录日志
            try {
                BaseAction.findAction(dto.getActionEnum()).doAction(dto);
            } catch (Exception e) {
                log.error("记录导出日志出错", e);
            }
        } catch (Exception ex) {
            log.error("[{}]导出出错", base.desc(), ex);
            throw new ServiceException(String.format("[%s]导出出错", base.desc()), ex);
        }
    }

    /**
     * baseAttributeList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/7 12:24
     */
    @Override
    public List<AttributeTypeVo> baseAttributeList(AttributeTypeDTO dto) throws ServiceException {
        dto.isValid();
        String key = String.format(
                "intelligence.%s.attribute.%s",
                dto.getPath(),
                dto.getAttributeType()
        );
        String value = BeanFactoryHolder.getEnv().getProperty(key);
        PreConditionCheck.checkNotEmpty(value, new ServiceException("暂不支持[" + dto.getAttributeType() + "]"));
        if (!JsonUtils.isValidArray(value)) {
            throw new ServiceException("[" + dto.getAttributeType() + "]配置格式错误");
        }
        return JSON.parseArray(value, AttributeTypeVo.class);
    }

    @Override
    public RestfulResultsV2 listBar(String type) throws ServiceException {
        PreConditionCheck.checkNotEmpty(type, new ServiceException("线索人员标签类型不能为空！"));
        QueryWrapper<CommonCategoryEntity> queryWrapper = new QueryWrapper<>();
        // 需要查询包含缺省分类的数据（即未分类）
        queryWrapper
                .in("`type`", type, getDefaultCategory().getType())
                .eq("is_del", 0);
        queryWrapper.orderByAsc("order_num")
                .orderByDesc("cr_time");
        List<CommonCategoryVO> data = categoryMapper.selectList(queryWrapper)
                .stream()
                .map(CommonCategoryEntity::toVO)
                .collect(Collectors.toList());
        return convertTree(type, data);
    }

    @Override
    public List<XianSuoRelatedPersonVo> relatedPersonList(String path, RelatedPersonQueryDTO dto)
            throws ServiceException {
        dto.isValid();
        return KeyMgrFactory.findMgrByKey(BaseIntelligenceEntityService.class, path, true)
                .relatedPersonList(dto);
    }

    /**
     * xiansuoSimpleList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 17:40
     */
    @Override
    public List<XianSuoEntityWithRepeatPersonVo> xiansuoSimpleList(XianSuoSimpleListDTO dto) throws ServiceException {
        dto.isValid();
        return BeanUtil.getBean(XianSuoEntityServiceImpl.class).simpleList(dto);
    }

    /**
     * xiansuoPersonLibrary<BR>
     *
     * @param request 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/10 14:19
     */
    @Override
    public RestfulResultsV2<XianSuoPersonLibraryVo> xiansuoPersonLibrary(ListParamsRequest request)
            throws ServiceException {
        return BeanUtil.getBean(XianSuoEntityServiceImpl.class).personLibrary(request);
    }

    /**
     * convertTree<BR>
     *
     * @param type 类型
     * @param data 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    private RestfulResultsV2 convertTree(String type, List<CommonCategoryVO> data) throws ServiceException {
        List<CommonCategoryVO> tmp = new ArrayList<>(data.size());
        tmp.addAll(data);
        // 获取到所有分类ID与自身的对象Map
        Map<Long, CommonCategoryVO> map = tmp
                .stream()
                .collect(Collectors.toMap(
                        CommonCategoryVO::getId,
                        r -> r,
                        (a, b) -> a
                ));
        Map<Long, Long> countMap = new HashMap<>(0);
        for (Long id : map.keySet()) {
            Long count = labelMapper.selectCount(new QueryWrapper<CommonLabelEntity>()
                    .eq("is_del", 0)
                    .in("category_id", id));
            countMap.put(id, count);
        }
        // 获取到所有分类父级ID与子集的关系Map
        final Map<Long, List<CommonCategoryVO>> parentIdAndChildrenMap = tmp.stream()
                .collect(Collectors.groupingBy(CommonCategoryVO::getParentId));
        List<CommonCategoryVO> returnData = new ArrayList<>(map.size());
        for (CommonCategoryVO vo : map.values()) {
            Long pId = Optional.ofNullable(vo.getParentId()).orElse(0L);
            if (pId > 0L) {
                CommonCategoryVO pVO = map.get(pId);
                if (pVO != null) {
                    pVO.addChildren(vo);
                    vo.setParentName(pVO.getName());
                }
            } else {
                vo.setParentName("全部");
                returnData.add(vo);
            }
            Long num = countMap.get(vo.getId());
            if (vo.getId() > 0) {
                List<CommonCategoryVO> list = parentIdAndChildrenMap.get(vo.getId());
                if (CollectionUtils.isNotEmpty(list)) {
                    List<CommonCategoryVO> allChild = findAllChild(list, parentIdAndChildrenMap);
                    List<Long> categoryIds = allChild.stream().map(CommonCategoryVO::getId)
                            .collect(Collectors.toList());

                    for (Long id : countMap.keySet()) {
                        if (categoryIds.contains(id)) {
                            num += countMap.get(id);
                        }
                    }
                }
            }
            vo.setNum(num);
        }
        CommonCategoryVO all = new CommonCategoryVO();
        //获取在分类映射表中根据实体id排重后的总量，页面全部的数量需要显示这个
        Long reduceNum = returnData.stream().map(CommonCategoryVO::getNum).reduce(0L, Long::sum);
        all.setId(0L);
        all.setName("全部");
        all.setType(type);
        all.setNum(reduceNum);
        all.setCanDel(0);
        all.addChildren(returnData);
        return RestfulResultsV2.ok(all);
    }

    private List<CommonCategoryVO> findAllChild(List<CommonCategoryVO> list, Map<Long, List<CommonCategoryVO>> parentIdAndChildrenMap) {
        List<CommonCategoryVO> allChild = new ArrayList<>(0);
        if (CollectionUtils.isNotEmpty(list)) {
            allChild.addAll(list);
            list.forEach(vo -> {
                List<CommonCategoryVO> children = parentIdAndChildrenMap.get(vo.getId());
                if (CollectionUtils.isNotEmpty(children)) {
                    allChild.addAll(findAllChild(children, parentIdAndChildrenMap));
                }
            });
        }
        return allChild;
    }

    /**
     * 获取默认分类
     *
     * @return 结果
     */
    public CommonCategoryVO getDefaultCategory() {
        if (defaultCategory == null) {
            synchronized (IntelligenceServiceImpl.class) {
                if (defaultCategory == null) {
                    defaultCategory = categoryMapper.selectById(-1).toVO();
                }
            }
        }
        return defaultCategory;
    }

    /**
     * getPage<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/19 14:33
     */
    private static Page<CommonLabelEntity> getPage(LabelListDTO dto) {
        OrderItem item = OrderItem.desc("update_time");
        if (StringUtils.isNotEmpty(dto.getOrderField())) {
            if (Objects.isNull(dto.getOrderType())
                    || CommonConstants.ORDER_DESC.equals(dto.getOrderType())) {
                item = OrderItem.desc(dto.getOrderField());
            } else {
                item = OrderItem.asc(dto.getOrderField());
            }
        }
        Page<CommonLabelEntity> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        page.setOrders(Collections.singletonList(item));
        return page;
    }

    @Override
    public RestfulResultsV2<CommonLabelVO> labelList(LabelListDTO dto) throws ServiceException {
        dto.isValid();
        List<Long> ids = new ArrayList<>(0);
        var parentIds = StringUtils.getLongList(dto.getCategoryIds(), StringUtils.SEPARATOR_COMMA_OR_SEMICOLON);
        ids.addAll(parentIds);
        if (dto.getFindChildren()) {
            //拿到所有子分类id
            List<Long> childrenIds = new LambdaQueryChainWrapper<>(categoryMapper)
                    .eq(CommonCategoryEntity::getIsDel, 0)
                    .eq(CommonCategoryEntity::getType, dto.getType())
                    .in(CommonCategoryEntity::getParentId, parentIds)
                    .list()
                    .stream()
                    .map(CommonCategoryEntity::getDataId)
                    .collect(Collectors.toList());
            while (!childrenIds.isEmpty()) {
                ids.addAll(childrenIds);
                childrenIds = new LambdaQueryChainWrapper<>(categoryMapper)
                        .eq(CommonCategoryEntity::getIsDel, 0)
                        .eq(CommonCategoryEntity::getType, dto.getType())
                        .in(CommonCategoryEntity::getParentId, childrenIds)
                        .list()
                        .stream()
                        .map(CommonCategoryEntity::getDataId)
                        .collect(Collectors.toList());
            }
        }
        Page<CommonLabelVO> pageList = labelMapper.labelPageList(getPage(dto), ids, dto);
        var deptMapping = Optional.of(
                        pageList.getRecords()
                                .stream()
                                .map(CommonLabelVO::getCrDeptId)
                                .distinct()
                                .collect(Collectors.toList())
                ).filter(CollectionUtils::isNotEmpty)
                .map(permissionService::getDeptByIds)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(DeptDto::getId, DeptDto::toSimpleVO));
        pageList.getRecords()
                .forEach(it -> Optional.ofNullable(deptMapping.get(it.getCrDeptId())).ifPresent(it::setCrDept));
        return RestfulResultsV2.ok(pageList.getRecords())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addTotalCount(pageList.getTotal());
    }

    @Override
    public Report<String> saveOrUpdateCategory(SaveOrEditCategoryDTO dto) throws ServiceException {
        dto.isValid();
        CurrentUser user = getCurrentUser();
        CommonCategoryEntity data;
        if (Optional.ofNullable(dto.getId()).orElse(0L) > 0) {
            //编辑
            data = categoryMapper.selectById(dto.getId());
            checkNotNull(data, new ServiceException("不存在ID=[" + dto.getId() + "]的分类！"));
            checkArgument(dto.getType().equals(data.getType()),
                    new ParamInvalidException("数据库中分类类型与传入的不匹配"));
        } else {
            //新增
            data = CommonCategoryEntity.addInfoOnCreate(user, new CommonCategoryEntity());
            data.setType(dto.getType());
            data.setOrderNum(0);
        }
        if (Optional.ofNullable(dto.getParentId()).orElse(0L) > 0) {
            CommonCategoryEntity p = categoryMapper.selectById(dto.getParentId());
            checkNotNull(p, new ServiceException("不存在ID=[" + dto.getId() + "]的父分类！"));
            checkArgument(dto.getType().equals(p.getType()), new ParamInvalidException("父分类的类型与传入的不匹配"));
            if (Optional.ofNullable(data.getDataId()).orElse(0L) > 0) {
                checkArgument(!dto.getParentId().equals(data.getDataId()),
                        new ParamInvalidException("自己不能成为自己的子分类"));
                if (getParentIds(p.getType(), p.getDataId()).contains(data.getDataId())) {
                    throw new ParamInvalidException("分类层级出现了循环");
                }
            }
            data.setParentId(p.getDataId());
        } else {
            data.setParentId(0L);
        }
        //同父分类下不能存在同名分类
        if (categoryMapper.selectCount(new QueryWrapper<CommonCategoryEntity>()
                .ne(Optional.ofNullable(data.getDataId()).orElse(0L) > 0, "id", data.getDataId())
                .eq("`type`", dto.getType())
                .eq("parent_id", data.getParentId())
                .eq("name", dto.getName())) > 0) {
            throw new ServiceException("同父分类下存在同名同类型的分类！");
        }
        data.setName(dto.getName());
        String detail = "新增分类";
        if (Objects.isNull(data.getDataId())) {
            categoryMapper.insert(data);
        } else {
            data.setUpdateUser(user.getUsername());
            data.setUpdateUserTrueName(StringUtils.showEmpty(user.getRealName(), user.getUsername()));
            data.setUpdateTime(new Date());
            categoryMapper.updateById(data);
            detail = "修改分类";
        }
        return new Report<>("保存分类", detail + "成功", Report.RESULT.SUCCESS, data.getDataId());
    }

    /**
     * 获取父ID
     *
     * @param type 参数
     * @param id   参数
     * @return 结果
     * @throws ServiceException 异常
     */
    public List<Long> getParentIds(String type, Long id) throws ServiceException {
        List<Long> ids = new ArrayList<>();
        CommonCategoryEntity data = categoryMapper.selectOne(new QueryWrapper<CommonCategoryEntity>()
                .eq("data_id", id)
                .eq("`type`", type));
        if (Objects.nonNull(data)) {
            while (data.getParentId() > 0) {
                ids.add(data.getParentId());
                CommonCategoryEntity tmp = categoryMapper.selectOne(new QueryWrapper<CommonCategoryEntity>()
                        .eq("data_id", data.getDataId())
                        .eq("`type`", data.getType()));
                if (Objects.isNull(tmp)) {
                    throw new ServiceException("分类不存在，无法获取对应的父ID列表");
                }
                data = tmp;
            }
            return ids;
        } else {
            throw new ServiceException("分类不存在，无法获取对应的父ID列表");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Report<String> saveOrUpdateLabel(SaveOrEditLabelDTO dto) throws ServiceException {
        dto.isValid();
        CurrentUser user = getCurrentUser();
        CommonLabelEntity entity;
        if (Optional.ofNullable(dto.getDataId()).orElse(0L) > 0) {
            entity = labelMapper.selectById(dto.getDataId());
        } else {
            entity = CommonLabelEntity.addInfoOnCreate(user, new CommonLabelEntity());
        }
        CommonCategoryEntity category = categoryMapper.selectById(dto.getCategoryId());
        if (Objects.isNull(category)) {
            throw new ServiceException("指定分类不存在");
        }
        var size = new LambdaQueryChainWrapper<>(labelMapper)
                .eq(CommonLabelEntity::getIsDel, 0)
                .eq(CommonLabelEntity::getType, dto.getType())
                .eq(CommonLabelEntity::getCategoryId, category.getDataId())
                .eq(CommonLabelEntity::getLabelName, dto.getName())
                .ne(CommonLabelEntity::getDataId, dto.getDataId())
                .count();
        // 相同分类层级下 标签名称不能重复
        if (size > 0) {
            throw new ServiceException("同分类层级下存在同名标签！");
        }
        entity.setType(dto.getType());
        entity.setCategoryId(dto.getCategoryId());
        entity.setCategoryName(category.getName());
        String oldName = entity.getLabelName();
        entity.setLabelName(dto.getName());
        entity.setEnable(dto.getEnable());
        String detail = "新增标签";
        if (Objects.isNull(entity.getDataId())) {
            entity.setUpdateTime(new Date());
            labelMapper.insert(entity);
        } else {
            detail = "修改标签";
            entity.setUpdateTime(new Date());
            entity.setCrUser(user.getUsername());
            entity.setCrUserTrueName(StringUtils.showEmpty(user.getRealName(), user.getUsername()));
            labelMapper.updateById(entity);
            if (!oldName.equals(dto.getName())) {
                //同步更新关联线索中的标签名称
                updateRelationClues(dto.getDataId(), dto.getName(), oldName);
            }
        }
        return new Report<>("保存标签", detail + "成功", Report.RESULT.SUCCESS, entity.getDataId());
    }

    /**
     * 同步更新关联线索中的标签名称
     *
     * @param dataId  标签id
     * @param name    新的标签名称
     * @param oldName 旧的标签名称
     */
    private void updateRelationClues(Long dataId, String name, String oldName) {
        List<XianSuoRelatedPersonEntity> entities = labelMapper.selectPersonCount(Collections.singletonList(dataId));
        for (XianSuoRelatedPersonEntity entity : entities) {
            String[] labelNames = entity.getRemarksLabelName().split(",");
            StringBuilder sb = new StringBuilder();
            for (String labelName : labelNames) {
                if (!labelName.equals(oldName)) {
                    sb.append(labelName).append(",");
                } else {
                    sb.append(name).append(",");
                }
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
            entity.setRemarksLabelName(sb.toString());
            personMapper.updateById(entity);
            XianSuoPersonLibraryEntity library = libraryMapper.selectOne(new QueryWrapper<XianSuoPersonLibraryEntity>()
                    .eq("is_del", 0)
                    .eq("zjhm", entity.getZjhm()));
            library.setRemarksLabel(entity.getRemarksLabel());
            library.setRemarksLabelName(sb.toString());
            libraryMapper.updateById(library);
        }
    }

    @Override
    public Report<String> removeCategory(String type, Long id) throws ServiceException {
        PreConditionCheck.checkNotEmpty(type, "类型不能为空！");
        PreConditionCheck.checkNotNull(id, "分类id不能为空！");
        CommonCategoryEntity entity = categoryMapper.selectById(id);
        checkDept(entity.getCrDeptId(), entity.getDataId());
        final Integer zero = 0;
        if (zero.equals(entity.getCanDel())) {
            throw new ServiceException("该分类已被标记为不可删除！");
        }
        Long subCategories = categoryMapper.selectCount(new QueryWrapper<CommonCategoryEntity>()
                .eq("is_del", 0)
                .eq("parent_id", id));
        if (subCategories > zero.longValue()) {
            throw new ServiceException("该分类下存在子分类，请先删除子分类！");
        }
        Long labelCount = labelMapper.selectCount(new QueryWrapper<CommonLabelEntity>()
                .eq("is_del", 0)
                .eq("category_id", id));
        if (labelCount > zero.longValue()) {
            throw new ServiceException("该分类下存在标签，请先删除标签！");
        }
        entity.setIsDel(1);
        CurrentUser user = getCurrentUser();
        entity.setUpdateTime(new Date());
        entity.setUpdateUser(user.getUsername());
        entity.setUpdateUserTrueName(StringUtils.showEmpty(user.getRealName(), user.getUsername()));
        categoryMapper.updateById(entity);
        return new Report<>("删除分类", "成功删除分类", Report.RESULT.SUCCESS, entity.getDataId());
    }

    /**
     * 用户操作权限校验
     *
     * @param crDeptId 创建部门id
     * @param dataId   数据id
     * @throws ServiceException 异常
     */
    private void checkDept(Long crDeptId, Long dataId) throws ServiceException {
        CurrentUser user = getCurrentUser();
        //是否同部门权限校验
        if (!user.getDeptId().equals(crDeptId)) {
            throw new ServiceException(String.format("您与数据[dataId=%s]创建者的单位不同，没有操作权限！", dataId));
        }
    }

    @Override
    public Report<String> removeLabel(String type, String dataIds) throws ServiceException {
        PreConditionCheck.checkNotEmpty(type, "类型不能为空！");
        PreConditionCheck.checkNotEmpty(dataIds, "标签id不能为空！");
        if (Constants.CLUEPERSON.equals(type)) {
            type = Constants.CLUE_PERSON;
        }
        //当前用户权限校验
        List<CommonLabelEntity> labels = getLabels(dataIds);
        //校验标签是否已有关联
        List<Long> ids = labels.stream().map(CommonLabelEntity::getDataId).collect(Collectors.toList());
        if (Objects.equals(Constants.CLUE_PERSON, type)) {
            List<XianSuoRelatedPersonEntity> entities = labelMapper.selectPersonCount(ids);
            if (CollectionUtils.isNotEmpty(entities)) {
                throw new ServiceException("标签已被关联，请先解除关联！");
            }
        } else if (Objects.equals(Constants.YQFL, type)) {
            if (labelMapper.selectYuQingCount(ids) > 0) {
                throw new ServiceException("标签已被关联，请先解除关联！");
            }
        }
        CurrentUser user = getCurrentUser();
        labels.forEach(entity -> {
            entity.setIsDel(1);
            entity.setUpdateTime(new Date());
            entity.setCrUser(user.getUsername());
            entity.setCrUserTrueName(StringUtils.showEmpty(user.getRealName(), user.getUsername()));
            labelMapper.updateById(entity);
        });
        return new Report<>("删除标签", "成功删除标签", Report.RESULT.SUCCESS, dataIds);
    }

    /**
     * 获取标签
     *
     * @param dataIds 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    private List<CommonLabelEntity> getLabels(String dataIds) throws ServiceException {
        List<Long> ids = StringUtils.getLongList(dataIds, StringUtils.SEPARATOR_COMMA_OR_SEMICOLON);
        List<CommonLabelEntity> entities = labelMapper.selectBatchIds(ids);
        for (CommonLabelEntity entity : entities) {
            checkDept(entity.getCrDeptId(), entity.getDataId());
        }
        return entities;
    }

    @Override
    public Report<String> enableLabel(String type, String dataIds, Integer enable) throws ServiceException {
        PreConditionCheck.checkNotEmpty(type, "类型不能为空！");
        PreConditionCheck.checkNotEmpty(dataIds, "标签id不能为空！");
        PreConditionCheck.checkNotNull(enable, "启用状态不能为空！");
        if (Constants.CLUEPERSON.equals(type)) {
            type = Constants.CLUE_PERSON;
        }
        List<CommonLabelEntity> labels = getLabels(dataIds);
        final CurrentUser user = getCurrentUser();
        for (CommonLabelEntity entity : labels) {
            checkArgument(
                    Objects.equals(type, entity.getType()),
                    new ServiceException(String.format("[dataId=%s]类型不一致", entity.getDataId()))
            );
            entity.setEnable(enable);
            entity.setUpdateTime(new Date());
            entity.setCrUser(user.getUsername());
            entity.setCrUserTrueName(StringUtils.showEmpty(user.getRealName(), user.getUsername()));
            labelMapper.updateById(entity);
        }
        String detail = enable == 1 ? "启用标签" : "禁用标签";
        return new Report<>("设置标签状态", "成功" + detail, Report.RESULT.SUCCESS, dataIds);
    }

    @Override
    public RestfulResultsV2<YuQingVersionDataVo> yuqingVersionData(YuQingVersionDataDTO dto) {
        return BeanUtil.getBean(YuQingEntityServiceImpl.class).versionData(dto);
    }

    /**
     * renwuSheAnXinXiList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:05
     */
    @Override
    public RestfulResultsV2<RenWuDaShuJuHeChaAnJianVo> renwuSheAnXinXiList(RenWuDaShuJuHeChaQueryActionDTO dto) {
        return Try.of(() -> {
                    dto.isValid();
                    var list = BeanUtil.getBean(RenWuEntityServiceImpl.class).sheAnXinXiList(dto);
                    return RestfulResultsV2.ok(PageUtils.subList(dto.getPageNum(), dto.getPageSize(), list))
                            .addPageNum(dto.getPageNum())
                            .addPageSize(dto.getPageSize())
                            .addTotalCount((long) list.size());
                }).onFailure(e -> log.error("renwuSheAnXinXiList异常", e))
                .getOrElseGet(e -> RestfulResultsV2.error(e.getMessage()));
    }

    /**
     * renwuSheAnXinXiDetail<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:05
     */
    @Override
    public RenWuDaShuJuHeChaAnJianVo renwuSheAnXinXiDetail(RenWuDaShuJuHeChaQueryActionDTO dto) throws ServiceException {
        dto.isValid();
        return BeanUtil.getBean(RenWuEntityServiceImpl.class).sheAnXinXiDetail(dto);
    }

    /**
     * renwuJiaoTongWeiFaList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:05
     */
    @Override
    public RestfulResultsV2<RenWuDaShuJuHeChaJiaoTongWeiFaVo> renwuJiaoTongWeiFaList(RenWuDaShuJuHeChaQueryActionDTO dto) {
        return Try.of(() -> {
                    dto.isValid();
                    var list = BeanUtil.getBean(RenWuEntityServiceImpl.class).jiaoTongWeiFaList(dto);
                    return RestfulResultsV2.ok(PageUtils.subList(dto.getPageNum(), dto.getPageSize(), list))
                            .addPageNum(dto.getPageNum())
                            .addPageSize(dto.getPageSize())
                            .addTotalCount((long) list.size());
                }).onFailure(e -> log.error("jiaoTongWeiFaList异常", e))
                .getOrElseGet(e -> RestfulResultsV2.error(e.getMessage()));

    }

    @Override
    public String topOrCancel(Long dataId, Boolean flag) {
        PreConditionCheck.checkNotNull(dataId, "主键不能为空！");
        PreConditionCheck.checkNotNull(flag, "类型不能为空！");
        boolean bool = Boolean.TRUE.equals(flag);
        zhiLingBaseInfoMapper.update(null, new UpdateWrapper<ZhiLingBaseInfoEntity>().lambda()
                .eq(ZhiLingBaseInfoEntity::getDataId, dataId)
                .set(ZhiLingBaseInfoEntity::getTopMarking, bool ? 1 : 0)
                .set(ZhiLingBaseInfoEntity::getTopTime, bool ? new Date() : null)
        );
        return bool ? "置顶成功" : "取消置顶成功";
    }

    /**
     * 获取当前登录用户
     *
     * @return user
     * @throws ServiceException 异常
     */
    private CurrentUser getCurrentUser() throws ServiceException {
        return Optional.ofNullable(AuthHelper.getCurrentUser())
                .orElseThrow(() -> new ServiceException("获取当前登录用户失败!"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String associateGroup(Long dataId, Long relatedGroupId) throws ServiceException {
        final CurrentUser user = getCurrentUser();
        // 任务关联群体
        PreConditionCheck.checkNotNull(dataId, "任务id不能为空！");
        PreConditionCheck.checkNotNull(relatedGroupId, "关联群体id不能为空！");
        // 若该任务已绑定了群体，则不允许再次绑定
        RenWuBaseInfoEntity rw = renWuBaseInfoMapper.findById(dataId);
        PreConditionCheck.checkArgument(rw.getRelatedGroupId() == null, "该任务已经绑定了群体！");
        rw.setRelatedGroupId(relatedGroupId);
        renWuBaseInfoMapper.updateById(rw);
        // 相关人员 - 人员档案 - 关联群体
        List<RenWuRelatedPersonEntity> personList = renWuRelatedPersonMapper.relatedPersonList(dataId);
        List<Long> collect = new ArrayList<>(0);
        for (RenWuRelatedPersonEntity p : personList) {
            if (StringUtils.isEmpty(p.getZjhm())) {
                continue;
            }
            // 查询人档中是否存在对应档案数据
            PersonVO profilePerson = profileService.getPersonByIdNumber(p.getZjhm());
            List<Long> relatedPersonIds = profileService.queryByGroupId(relatedGroupId);
            Long personId;
            if (profilePerson != null) {
                personId = profilePerson.getId();
            } else {
                // 补录人档数据
                personId = addProfilePersonV2(p, user);
            }
            if (!relatedPersonIds.contains(personId)) {
                collect.add(personId);
            }
        }
        // 补充人员-群体关联关系
        profileService.addPersonGroupRelation(user.getId(), user.getDeptId(), relatedGroupId, collect);
        return "操作成功";
    }

    private Long addProfilePersonV2(RenWuRelatedPersonEntity p, CurrentUser user) throws ServiceException {
        PersonVO vo = new PersonVO();
        vo.setName(p.getXm());
        vo.setCertificateNumber(p.getZjhm());
        vo.setCertificateType(1);
        vo.setTel(Collections.singletonList(p.getSjh()));
        vo.setRegisteredResidence(p.getHjd());
        vo.setTargetType(new ArrayList<>(0));
        vo.setCurrentUser(user);
        Long personId = profileService.updatePersonInfo(vo);
        if (Objects.nonNull(personId) && personId > 0L) {
            return personId;
        } else {
            log.error("人员证件号码[{}]，在人员档案中补录失败，请在[profile]模块后台检查日志", p.getZjhm());
            throw new ServiceException("人员证件号码[" + p.getZjhm() + "]，在人员档案中补录失败!");
        }
    }

    private Long addProfilePersonV1(RenWuRelatedPersonEntity person, Long relatedGroupId, CurrentUser user) throws ServiceException {
        JSONObject object = new JSONObject();
        object.put("name", person.getXm());
        object.put("sfz", person.getZjhm());
        object.put("sjh", person.getSjh());
        object.put("hjdDm", person.getHjdDm());
        object.put("groupId", relatedGroupId);
        Long personId = profileService.saveGroupOrPerson(null, user.getId(), user.getDept().getId(), object.toJSONString());
        if (Objects.nonNull(personId) && personId > 0L) {
            return personId;
        } else {
            log.error("人员证件号码[{}]，在人员档案中补录失败，请在[profile]模块后台检查日志", person.getZjhm());
            throw new ServiceException("人员证件号码[" + person.getZjhm() + "]，在人员档案中补录失败!");
        }
    }
}
