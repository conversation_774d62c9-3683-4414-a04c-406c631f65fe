package com.trs.police.intelligence.constant;

import java.util.Set;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * Constants
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 23:11
 * @since 1.0
 */
public class Constants {
    /**
     * 身份证的正则表达式
     */
    public static final String ID_NUMBER_REX = "\\d{17}[\\dXx]";

    public static final String SYSTEM_FLAG = "intelligence";

    public static final String YAOQING = "yaoqing";
    public static final String XIANSUO = "xiansuo";
    public static final String RENWU = "renwu";
    public static final String XIANSUO_RELATED_PERSON = "xiansuoRelatedPerson";
    public static final String RENWU_RELATED_PERSON = "renwuRelatedPerson";

    public static final String ZHILING = "zhiling";

    public static final String TONGZHITONGBAO = "tongzhitongbao";

    public static final String YUQING = "yuqing";

    public static final String CHAT = "chat";
    public static final String USER = "user";
    public static final String DEPT = "dept";

    public static final String DEFAULT = "default";

    public static final String LABELS = "labels";

    public static final String FEEDBACK_LIMIT_TIME = "feedbackLimitTime";

    public static final String SIGN_LIMIT_TIME = "signLimitTime";

    public static final String DATA_TYPE = "data_type";

    public static final String DATA_CLASS = "data_class";

    public static final String ACCEPT_DEPT_IDS = "acceptDeptIds";
    public static final String ACCEPT_USERS = "acceptUsers";
    public static final String NOTICE = "notice";
    public static final String REPORT = "report";

    public static final String NOTICE_NO = "noticeNo";

    public static final String NOTICE_TYPE = "noticeType";

    public static final String INTELLIGENCE_PERSON_LABEL = "intelligence_person_label";
    public static final String ARRAY_FIND_IN_SET = "array_find_in_set";
    public static final String ARRAY = "array";
    public static final String IN = "in";
    public static final String FIND_IN_SET = "find_in_set";

    public static final String ATTRIBUTES_DOT = "attributes.";

    public static final String ATTRIBUTES_VALUE = ":VALUE";

    public static final String REPORT_TYPE_LEADER_USER_ID = "leaderUserId";
    public static final String REPORT_TYPE_LEADER_DEPT_ID = "leaderDeptId";

    /**
     * 0：首报（草稿箱编辑阶段）
     */
    public static final Integer VERSION_FLAG_SHOUBAO = 0;

    /**
     * 1：续报/修订
     */
    public static final Integer VERSION_FLAG_XUBAO_OR_XIUDING = 1;

    /**
     * 2：终报
     */
    public static final Integer VERSION_FLAG_ZHONGBAO = 2;

    /**
     * 上报上级
     */
    public static final String UP = "up";

    /**
     * 我处理的
     */
    public static final String PROCESSED = "processed";

    /**
     * 待办
     */
    public static final String TODO = "todo";

    /**
     * 下级推送
     */
    public static final String DOWN = "down";

    /**
     * 草稿箱
     */
    public static final String DRAFTS = "drafts";

    /**
     * 全部
     */
    public static final String ALL = "all";

    /**
     * 新的全部
     */
    public static final String REAL_ALL = "realAll";
    public static final String FUHE = "fuhe";
    public static final String ARCHIVE = "archive";

    public static final Set<String> FROM_LIST = Set.of(REAL_ALL, FUHE, ARCHIVE);

    /**
     * 大屏
     */
    public static final String BIG_SCREEN = "bigscreen";

    public static final String PUSHUP = "pushUp";
    public static final String PUSHDOWN = "pushDown";

    public static final String PERSON_TYPE_LOCAL = "local";
    public static final String PERSON_TYPE_NO_LOCAL = "noLocal";
    public static final String PERSON_TYPE_ALL = "all";
    public static final String CN_JIN_QI = "近期";
    public static final String CN_GONGZUOZHONG = "工作中";
    public static final String CN_YIWENKONG = "已稳控";
    public static final String CN_CHUZHIZHONG = "处置中";
    public static final String CN_WEIYANPAN = "未研判";
    public static final String CN_HECHACHUZHI = "核查处置";


    public static final String DATA_RELATION_STATUS_TYPE_DEFAULT = "0";

    public static final String DATA_RELATION_STATUS_TYPE_SIGN = "sign";

    public static final String DATA_RELATION_STATUS_TYPE_RECEIVE = "receive";

    public static final String DATA_RELATION_STATUS_TYPE_FEEDBACK = "feedback";
    public static final String DATA_RELATION_STATUS_TYPE_FORWARD = "forward";
    public static final String DATA_RELATION_STATUS_TYPE_CC = "cc";
    public static final String DATA_RELATION_STATUS_TYPE_PROCESSED = "processed";

    public static final String RELATION_PERSON_WORK_STATUS_WZP = "未指派";
    public static final String RELATION_PERSON_WORK_STATUS_GZZ = "工作中";

    public static final String SJDW = "市局单位";
    public static final String SGAT = "省公安厅";
    public static final String QYB = "区域办";
    public static final String GAB = "公安部";

    public static final Set<String> ST_LY = Set.of(
            SGAT, QYB, GAB
    );

    public static final String CLUEPERSON = "cluePerson";

    public static final String CLUE_PERSON = "CLUE_PERSON";
    public static final String YQFL = "YQFL";

    public static final String SJCZLC = "事件处置流程";
    public static final String DZZB = "党政专报";
    public static final String XXKB = "信息快报";

    /**
     * AI信息提取
     */
    public static final String AI_INFO_EXTRACT = "infoExtract";

    /**
     * 线索类型：普通(默认)
     */
    public static final Integer XSLX_COMMON = 0;

    /**
     * 线索类型：复核
     */
    public static final Integer XSLX_FUHE = 1;

    /**
     * 线索类型：已归档
     */
    public static final Integer XSLX_ARCHIVE = 2;
}
