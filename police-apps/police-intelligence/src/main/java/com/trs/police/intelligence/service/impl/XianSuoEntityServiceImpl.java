package com.trs.police.intelligence.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.SystemConstant;
import com.trs.police.common.core.constant.enums.DeptTypeEnum;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.BigScreenPinDataEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.*;
import com.trs.police.intelligence.entity.*;
import com.trs.police.intelligence.mapper.XianSuoBaseInfoMapper;
import com.trs.police.intelligence.mapper.XianSuoPersonLibraryMapper;
import com.trs.police.intelligence.mapper.XianSuoRelatedPersonMapper;
import com.trs.police.intelligence.mapper.XianSuoRelatedPersonMappingMapper;
import com.trs.police.intelligence.mgr.EntityConvertToVo;
import com.trs.police.intelligence.service.BaseAction;
import com.trs.police.intelligence.service.BaseIntelligenceNoVersionEntityService;
import com.trs.police.intelligence.utils.*;
import com.trs.police.intelligence.vo.*;
import com.trs.police.intelligence.vo.third.ProvinceCluePersonVO;
import com.trs.police.intelligence.vo.third.ProvinceClueVO;
import com.trs.police.intelligence.vo.third.PullDataVo;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.utils.AreaCodeUtil.*;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * ZhilingEntityServiceImpl
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/17 16:37
 * @since 1.0
 */
@Service
@Slf4j
@Getter
public class XianSuoEntityServiceImpl extends BaseIntelligenceNoVersionEntityService<XianSuoBaseInfoEntity, XianSuoVersionEntity, XianSuoSaveDTO, XianSuoEntityVo, XianSuoEntityVo> {

    @Resource
    private XianSuoBaseInfoMapper mapper;

    @Resource
    private XianSuoRelatedPersonMappingMapper relatedPersonMappingMapper;

    @Resource
    private XianSuoRelatedPersonMapper relatedPersonMapper;

    @Resource
    private XianSuoPersonLibraryMapper personLibraryMapper;

    @Autowired(required = false)
    protected ProvinceClueHelper clueHelper;

    @Override
    public XianSuoVersionEntity findVersion(EntityDetailDTO dto, Long versionId) throws ServiceException {
        XianSuoVersionEntity version = new XianSuoVersionEntity();
        version.setFrom(dto.getFrom());
        version.setIsDel(0);
        version.setYqDataId(dto.getDataId());
        version.setDataId(versionId);
        version.setStatusCode(versionId.intValue());
        var login = Optional.ofNullable(AuthHelper.getCurrentUser());
        if (login.isPresent()) {
            if (Objects.equals(Constants.UP, dto.getFrom())
                    || Objects.equals(Constants.TODO, dto.getFrom())
                    || Objects.equals(Constants.BIG_SCREEN, dto.getFrom())) {
                PreConditionCheck.checkNotEmpty(dto.getPushType(), new ParamInvalidException("推送类型不能为空"));
                getDataRelationMappingMgr().getRelationList(
                                module(),
                                dto.getDataId(),
                                Constants.DEPT,
                                dto.getPushType()
                        ).stream().filter(it -> Objects.equals(it.getObjId(), login.get().getDeptId()))
                        .findAny()
                        .ifPresent(it -> version.setStatusCode(it.getStatusCode()));
            }
        }
        return version;
    }

    @Override
    public XianSuoBaseInfoEntity findById(Long dataId) throws ServiceException {
        XianSuoBaseInfoEntity entity = getMapper().findById(dataId);
        checkArgument(
                Objects.nonNull(entity) && entity.getIsDel() == 0,
                new ServiceException(String.format("不存在ID=[%s]的[%s]对象", dataId, desc()))
        );
        return entity;
    }

    @Override
    public Integer getMaxNo(String dataClass, CurrentUser user) throws ServiceException {
        if (Objects.isNull(user)) {
            user = AuthHelper.getNotNullUser();
        }
        final String code = user.getDept().getDistrictCode();
        PreConditionCheck.checkNotEmpty(code, new ServiceException("用户部门编码不能为空"));
        return new LambdaQueryChainWrapper<>(getMapper())
                .select(XianSuoBaseInfoEntity::getDataNo)
                .eq(XianSuoBaseInfoEntity::getIsDel, 0)
                .eq(XianSuoBaseInfoEntity::getCrDistrictCode, code)
                .eq(XianSuoBaseInfoEntity::getCrDate, TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD))
                .eq(XianSuoBaseInfoEntity::getDataYear, TimeUtils.getFieldOfDate(new Date(), Calendar.YEAR))
                .orderByDesc(XianSuoBaseInfoEntity::getDataNo)
                .page(new Page<>(1, 1))
                .getRecords()
                .stream()
                .filter(Objects::nonNull)
                .map(XianSuoBaseInfoEntity::getDataNo)
                .findFirst()
                .orElse(0);
    }

    @Override
    public String getOrderNo(String dataClass, CurrentUser user) throws ServiceException {
        if (Objects.isNull(user)) {
            user = AuthHelper.getNotNullUser();
        }
        final String code = StringUtils.showEmpty(user.getDept().getDistrictCode());
        String date = TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD5);
        int dataNo = getMaxNo(dataClass, user) + 1;
        StringBuilder dataNoStr = new StringBuilder(Integer.toString(dataNo));
        while (dataNoStr.length() < 5) {
            dataNoStr.insert(0, "0");
        }
        return String.format("XS%s%s%s", code, date, dataNoStr);
    }

    @Override
    public XianSuoEntityVo mergeDataOnDetail(EntityDetailDTO dto, XianSuoBaseInfoEntity entity, XianSuoVersionEntity version) throws ServiceException {
        List<Long> deptIds = new ArrayList<>(0);
        // 待接收的
        Optional.ofNullable(findAcceptDeptIds(entity, version))
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(deptIds::addAll);
        // 抄送的
        Optional.ofNullable(entity.makeCcDeptIds())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(deptIds::addAll);
        // 创建的
        Optional.ofNullable(entity.getCrDeptId())
                .filter(it -> !deptIds.contains(it))
                .ifPresent(deptIds::add);
        // 来源的
        Optional.ofNullable(entity.getSourceDeptId())
                .filter(it -> !deptIds.contains(it))
                .ifPresent(deptIds::add);
        // 发布单位
        Optional.ofNullable(entity.getPubDeptId())
                .filter(it -> !deptIds.contains(it))
                .ifPresent(deptIds::add);
        Optional.ofNullable(entity.getRelatedPersons())
                .map(it -> it.stream()
                        .map(XianSuoRelatedPersonEntity::getCheckDeptId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet())
                ).filter(CollectionUtils::isNotEmpty)
                .ifPresent(deptIds::addAll);
        Map<Long, SimpleDeptVO> deptMapping = Optional.of(deptIds)
                .filter(CollectionUtils::isNotEmpty)
                .map(this::findDeptByIds)
                .map(it -> it.stream().collect(Collectors.toMap(
                        SimpleDeptVO::getDeptId,
                        r -> r,
                        (a, b) -> a
                ))).orElse(new HashMap<>(0));
        Map<String, UserDto> userMapping = new HashMap<>(1);
        findUserByName(entity.getCrUser()).ifPresent(it -> userMapping.put(it.getUsername(), it));
        final XianSuoEntityVo vo = makeVo(entity, deptMapping, userMapping);
        // 回填前端传入的状态码
        Optional.ofNullable(version)
                .map(XianSuoVersionEntity::getStatusCode)
                .filter(it -> it > 0)
                .ifPresent(vo::setStatusCode);
        return vo;
    }

    private static String makeWorkInfo(String from, CurrentUser user, XianSuoBaseInfoEntity entity) {
        var list = Optional.ofNullable(entity.getRelatedPersons())
                .map(it -> it.stream()
                        .filter(i -> Try.of(
                                () -> {
                                    switch (from) {
                                        case Constants.UP:
                                            // 线索的创建人(按照XMKFB-1596要求屏蔽创建人)以及指派给当前部门的人员都是可见的
                                            if (Objects.equals(i.getCheckDeptId(), user.getDeptId())) {
                                                return true;
                                            }
                                            // 我接收的中，如果是上报过程中应该是都可见
                                            if (Constants.PUSHUP.equals(entity.getUpStatusType())) {
                                                return true;
                                            }
                                            // 如果用户可以看子的数据，就判断指派单位是否在其的子地域中
                                            if (DeptUtils.canSeeChildrenData(user)) {
                                                return isStartWithCode(i.getCheckDeptCode(), spreadingAreaCode(user.getDept().getDistrictCode(), false));
                                            }
                                            return false;
                                        default:
                                            // 其他地方看到的应该都是全部
                                            return true;
                                    }
                                }
                        ).getOrElse(false)).collect(Collectors.toList())
                ).orElse(Collections.emptyList());
        Long all = Integer.valueOf(list.size()).longValue();
        Long assign = list.stream().filter(it -> {
            // 没有指派单位的(草稿箱的数据按照XMKFB-1727要求都是未指派)
            if (Objects.isNull(it.getCheckDeptId())
                    || Objects.isNull(it.getAssignTime())
                    || Objects.equals(1, entity.getDraftsFlag())
            ) {
                return false;
            }
            // 指派单位在自己这儿的
            if (Objects.equals(it.getCheckDeptId(), user.getDeptId())) {
                return false;
            }
            // 如果不能看子数据，那么就都算已指派
            if (!DeptUtils.canSeeChildrenData(user)) {
                return true;
            }
            // 可以看子的需要看地域编码是否在其子地域中
            return Try.of(() -> isStartWithCode(it.getCheckDeptCode(), spreadingAreaCode(user.getDept().getDistrictCode(), false)))
                    .getOrElse(false);
        }).count();
        Long worked = list.stream().filter(it -> Constants.CN_YIWENKONG.equals(it.getWorkStatus())).count();
        return String.format("%s/%s/%s", assign, worked, all);
    }

    @Override
    public void check(Boolean isAdd, AttributeTemplatesVo templatesVo, XianSuoSaveDTO saveDTO, List<DataAttributesDTO> attributes, XianSuoBaseInfoEntity entity) throws ServiceException {
        if (!isAdd) {
            Boolean isDrafts = Optional.ofNullable(entity.getDraftsFlag()).filter(it -> it == 1).isPresent();
            Boolean isBuCaiYongStatus = Optional.ofNullable(entity.getStatusCode())
                    .filter(it -> Objects.equals(it, Status.BUCAIYONG.getCode()))
                    .isPresent();
            checkArgument(
                    isDrafts || isBuCaiYongStatus,
                    new ParamInvalidException("草稿或不采用的数据才能进行编辑")
            );
        }
    }

    /**
     * saveOrUpdate<BR>
     *
     * @param saveDTO 参数
     * @param user    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/23 15:29
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Report<String> saveOrUpdate(XianSuoSaveDTO saveDTO, CurrentUser user) throws ServiceException {
        saveDTO.isValid();
        XianSuoBaseInfoEntity entity;
        boolean isAdd = false;
        if (saveDTO.getDataId() != null && saveDTO.getDataId() > 0L) {
            entity = findById(saveDTO.getDataId());
            // ST回流数据或者当前创建者的数据
            checkArgument(
                    StringUtils.isNotEmpty(saveDTO.getStxsbh()) || entity.userCreateEntity(Optional.of(user)),
                    new ServiceException("暂不支持处理他人发布的" + desc())
            );
            checkArgument(entity.getPushType().equals(saveDTO.getPushType()), new ServiceException("推送类型不能修改"));
        } else {
            entity = new XianSuoBaseInfoEntity();
            isAdd = true;
        }
        final DeptTypeEnum deptType = Optional.ofNullable(user.getDept().getType())
                .map(Long::intValue)
                .map(DeptTypeEnum::codeOf)
                .orElse(null);
        // 派出所不支持下发
        if (DeptTypeEnum.POLICE_STATION == deptType) {
            if (Constants.PUSHDOWN.equals(saveDTO.getPushType())) {
                throw new ServiceException(deptType.getName() + "不支持下发");
            }
        }
        // 省厅目前是最高的一级，不支持上报
        if (DeptTypeEnum.PROVINCIAL == deptType) {
            if (Constants.PUSHUP.equals(saveDTO.getPushType())) {
                throw new ServiceException(deptType.getName() + "不支持上报");
            }
        }
        List<RelatedPersonSaveDTO> persons = saveDTO.makeRelatedPerson();
        check(isAdd, null, saveDTO, null, entity);
        mergeOnSave(user, isAdd, saveDTO, persons, entity);
        entity.setVersionId(0L);
        if (entity.getDataId() != null && entity.getDataId() > 0L) {
            getMapper().updateById(entity);
        } else {
            getMapper().insert(entity);
        }
        final Long dataId = entity.getDataId();
        // 回填rootId
        if (Optional.ofNullable(entity.getRootId()).filter(it -> it > 0L).isEmpty()) {
            entity.setRootId(dataId);
            getMapper().updateById(entity);
        }
        var ccDeptIds = entity.makeCcDeptIds();
        // 构建抄送关系
        getDataRelationMappingMgr().saveRelation(
                user,
                key(),
                dataId,
                Constants.DEPT,
                ccDeptIds,
                Constants.DATA_RELATION_STATUS_TYPE_CC,
                0
        );
        rebuildRelatedPersonMapping(user, dataId, persons);
        // 保存线索池关系
        Optional.ofNullable(saveDTO.getCluePoolId())
                .filter(it -> it > 0L)
                .ifPresent(it -> getDataRelationMappingMgr().saveRelation(
                        user,
                        key(),
                        dataId,
                        SystemConstant.CLUE_POOL,
                        List.of(it)
                ));
        return new Report<>("保存线索", "成功进行保存", RESULT.SUCCESS, dataId);
    }

    /**
     * rebuildRelatedPersonMapping<BR>
     *
     * @param user    参数
     * @param clueId  参数
     * @param persons 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/23 15:30
     */
    @Transactional(rollbackFor = Throwable.class)
    public void rebuildRelatedPersonMapping(CurrentUser user, Long clueId, List<RelatedPersonSaveDTO> persons) throws ServiceException {
        // 待存储的用户ID
        final var savePersonIds = persons
                .stream()
                .map(RelatedPersonSaveDTO::getDataId)
                .filter(it -> it != null && it > 0L)
                .collect(Collectors.toSet());
        // 已存在的关联关系
        final var existsMappings = new LambdaQueryChainWrapper<>(
                getRelatedPersonMappingMapper())
                .eq(XianSuoRelatedPersonMappingEntity::getIsDel, 0)
                .eq(XianSuoRelatedPersonMappingEntity::getClueId, clueId)
                .list();
        // 存在关联关系的人员ID
        final var existsPersonIds = existsMappings
                .stream()
                .map(XianSuoRelatedPersonMappingEntity::getPersonId)
                .collect(Collectors.toSet());

        if (!existsMappings.isEmpty()) {
            var mappingIds = existsMappings
                    .stream()
                    .map(XianSuoRelatedPersonMappingEntity::getDataId)
                    .collect(Collectors.toSet());
            // 标记删除（如果待存储的用户ID中不存在，删除关联关系）
            new LambdaUpdateChainWrapper<>(getRelatedPersonMappingMapper())
                    .in(XianSuoRelatedPersonMappingEntity::getDataId, mappingIds)
                    .notIn(CollectionUtils.isNotEmpty(savePersonIds), XianSuoRelatedPersonMappingEntity::getPersonId, savePersonIds)
                    .ne(XianSuoRelatedPersonMappingEntity::getIsDel, 1)
                    .set(XianSuoRelatedPersonMappingEntity::getIsDel, 1)
                    .update();
            // 标记删除（如果待存储的用户ID中不存在，删除本身）
            new LambdaUpdateChainWrapper<>(getRelatedPersonMapper())
                    .in(XianSuoRelatedPersonEntity::getDataId, existsPersonIds)
                    .notIn(CollectionUtils.isNotEmpty(savePersonIds), XianSuoRelatedPersonEntity::getDataId, savePersonIds)
                    .ne(XianSuoRelatedPersonEntity::getIsDel, 1)
                    .set(XianSuoRelatedPersonEntity::getIsDel, 1)
                    .update();
        }
        for (RelatedPersonSaveDTO person : persons) {
            var deptOpt = Optional.ofNullable(person.getCheckDeptId())
                    .map(this::findDeptById)
                    .filter(Optional::isPresent)
                    .map(Optional::get);
            // 新增的数据
            if (person.getDataId() == null || person.getDataId() <= 0L) {
                XianSuoRelatedPersonEntity save = XianSuoRelatedPersonEntity
                        .addInfoOnCreate(
                                user,
                                EntityConvertToVo.INSTANCE.dtoToEntity(person)
                        );
                deptOpt.ifPresent(simpleDeptVO -> save.setCheckDeptCode(spreadingAreaCode(simpleDeptVO.getDistrictCode(), false)));
                getRelatedPersonMapper().insert(save);
                person.setDataId(save.getDataId());
            } else {
                new LambdaUpdateChainWrapper<>(getRelatedPersonMapper())
                        .set(XianSuoRelatedPersonEntity::getZjhm, person.getZjhm())
                        .set(XianSuoRelatedPersonEntity::getXm, person.getXm())
                        .set(XianSuoRelatedPersonEntity::getSjhwm, person.getSjhwm())
                        .set(XianSuoRelatedPersonEntity::getHjd, person.getHjd())
                        .set(XianSuoRelatedPersonEntity::getGsdy, person.getGsdy())
                        .set(XianSuoRelatedPersonEntity::getGsdymc, person.getGsdymc())
                        .set(XianSuoRelatedPersonEntity::getRemarksLabel, person.getRemarksLabel())
                        .set(XianSuoRelatedPersonEntity::getRemarksLabelName, person.getRemarksLabelName())
                        .set(XianSuoRelatedPersonEntity::getIsSearched, person.getIsSearched())
                        .set(XianSuoRelatedPersonEntity::getCheckDeptId, person.getCheckDeptId())
                        .set(
                                XianSuoRelatedPersonEntity::getCheckDeptCode,
                                deptOpt.map(it -> spreadingAreaCode(it.getDistrictCode(), false)).orElse(null)
                        ).eq(XianSuoRelatedPersonEntity::getDataId, person.getDataId())
                        .update();
            }
            // 不存在的就补充关联关系
            if (!existsPersonIds.contains(person.getDataId())) {
                XianSuoRelatedPersonMappingEntity mapping = XianSuoRelatedPersonEntity
                        .addInfoOnCreate(user, new XianSuoRelatedPersonMappingEntity());
                mapping.setPersonId(person.getDataId());
                mapping.setClueId(clueId);
                getRelatedPersonMappingMapper().insert(mapping);
            }
        }
    }

    @Override
    public RestfulResultsV2<XianSuoEntityVo> queryList(String from, ListParamsRequest request)
            throws ServiceException {
        final CurrentUser user = AuthHelper.getNotNullUser();
        List<KeyValueTypeVO> baseFilter = request.getFilterParams()
                .stream()
                .filter(it -> !it.getKey().startsWith(Constants.ATTRIBUTES_DOT))
                .peek(it -> {
                    if ("targetTime".equals(StringUtils.showEmpty(it.getKey()))) {
                        it.setKey("target_time_format");
                    }
                }).map(FieldUtils::reBuildFilter)
                .collect(Collectors.toList());
        List<Long> realAllDeptIds = new ArrayList<>(1);
        if (Constants.FROM_LIST.contains(from)) {
            realAllDeptIds.add(user.getDeptId());
            Optional.ofNullable(getPermissionService().getCurrentUserDataPermissionInfo().getDeptIds())
                    .ifPresent(realAllDeptIds::addAll);
        }
        Page<XianSuoBaseInfoEntity> page = getMapper().doPageSelect(
                from,
                user,
                realAllDeptIds,
                baseFilter,
                request.getSearchParams(),
                request.getSortParams(),
                request.getPageParams().toPage()
        );
        List<Long> deptIds = new ArrayList<>();
        List<String> userNames = new ArrayList<>();
        List<Long> dataIds = new ArrayList<>();
        page.getRecords()
                .forEach(it -> {
                    Optional.ofNullable(it.makeAcceptDeptIds())
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(deptIds::addAll);
                    Optional.ofNullable(it.makeCcDeptIds())
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(deptIds::addAll);
                    deptIds.add(it.getSourceDeptId());
                    deptIds.add(it.getCrDeptId());
                    Optional.ofNullable(it.getPubDeptId())
                            .ifPresent(deptIds::add);
                    // 回填关联人员
                    it.setRelatedPersons(getRelatedPersonMapper().relatedPersonList(it.getDataId()));
                    Optional.ofNullable(it.getRelatedPersons())
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(
                                    item -> item.stream()
                                            .map(XianSuoRelatedPersonEntity::getCheckDeptId)
                                            .filter(Objects::nonNull)
                                            .forEach(deptIds::add)
                            );
                    dataIds.add(it.getDataId());
                    userNames.add(it.getCrUser());
                });
        Map<Long, SimpleDeptVO> deptMapping = CollectionUtils.isEmpty(deptIds) ? Collections.EMPTY_MAP
                : findDeptByIds(deptIds)
                .stream()
                .collect(Collectors.toMap(SimpleDeptVO::getDeptId, it -> it));
        Map<String, UserDto> userMapping = CollectionUtils.isEmpty(userNames) ? Collections.EMPTY_MAP
                : findUserByNames(userNames)
                .stream()
                .collect(Collectors.toMap(UserDto::getUsername, it -> it));
        Map<Long, CollectionEntity> collectIds =
                CollectionUtils.isEmpty(dataIds) ? Collections.EMPTY_MAP : getCollectionMapper()
                        .findCollectionEntityObjId(
                                Constants.USER,
                                user.getId(),
                                key(),
                                dataIds
                        ).stream().collect(Collectors.toMap(CollectionEntity::getObjId, it -> it, (a, b) -> a));
        final Map<Long, BigScreenPinDataEntity> pinDataMapping;
        if (Objects.equals(Constants.BIG_SCREEN, from)) {
            pinDataMapping = findPinData(dataIds, user);
        } else {
            pinDataMapping = Collections.EMPTY_MAP;
        }
        return RestfulResultsV2.ok(
                        page.getRecords()
                                .stream()
                                .map(it -> {
                                    var vo = makeVo(it, deptMapping, userMapping);
                                    vo.setWorkInfo(makeWorkInfo(from, user, it));
                                    vo.setCollectionFlag(0);
                                    Optional.ofNullable(collectIds.get(it.getDataId()))
                                            .ifPresent(col -> {
                                                vo.setCollectionFlag(1);
                                                vo.setCollectionTitle(col.getCollectionTitle());
                                            });
                                    if (Objects.equals(Constants.BIG_SCREEN, from)) {
                                        BigScreenPinDataEntity pinData = pinDataMapping.get(it.getDataId());
                                        if (Objects.nonNull(pinData)) {
                                            vo.setShowName(StringUtils.showEmpty(pinData.getShowTitle(), it.getDataTitle()));
                                            vo.setIsAddScreen(Optional.ofNullable(pinData.getPinFlag()).orElse(0));
                                        } else {
                                            vo.setShowName(it.getDataTitle());
                                            vo.setIsAddScreen(0);
                                        }
                                    }
                                    return vo;
                                })
                                .collect(Collectors.toList())
                ).addPageNum(request.getPageParams().getPageNumber())
                .addPageSize(request.getPageParams().getPageSize())
                .addTotalCount(page.getTotal());
    }

    @Override
    public List<CountItem> categoryList(CategorySearchDto dto) throws ServiceException {
        dto.isValid();
        final CurrentUser user = AuthHelper.getNotNullUser();
        List<KeyValueTypeVO> baseFilter = dto.getRequest().getFilterParams()
                .stream()
                .filter(it -> !it.getKey().startsWith(Constants.ATTRIBUTES_DOT))
                .peek(it -> {
                    if ("targetTime".equals(StringUtils.showEmpty(it.getKey()))) {
                        it.setKey("target_time_format");
                    }
                }).map(FieldUtils::reBuildFilter)
                .collect(Collectors.toList());
        List<Long> realAllDeptIds = new ArrayList<>(1);
        if (Constants.FROM_LIST.contains(dto.getFrom())) {
            realAllDeptIds.add(user.getDeptId());
            Optional.ofNullable(getPermissionService().getCurrentUserDataPermissionInfo().getDeptIds())
                    .ifPresent(realAllDeptIds::addAll);
        }
        String groupField;
        switch (StringUtils.showEmpty(dto.getDateFormat()).trim()) {
            case "day":
                groupField = String.format("DATE_FORMAT(%s,'%%Y-%%m-%%d')", dto.getGroupField());
                break;
            case "month":
                groupField = String.format("DATE_FORMAT(%s,'%%Y-%%m')", dto.getGroupField());
                break;
            case "year":
                groupField = String.format("DATE_FORMAT(%s,'%%Y')", dto.getGroupField());
                break;
            default:
                groupField = dto.getGroupField();
        }
        return getMapper().categoryList(
                dto.getFrom(),
                groupField,
                user,
                realAllDeptIds,
                baseFilter,
                dto.getRequest().getSearchParams()
        );
    }

    private void mergeOnSave(CurrentUser user, Boolean isAdd, XianSuoSaveDTO saveDTO, List<RelatedPersonSaveDTO> persons, XianSuoBaseInfoEntity entity) throws ServiceException {
        if (isAdd) {
            XianSuoBaseInfoEntity.addInfoOnCreate(user, entity);
            entity.setPubDeptId(Optional.ofNullable(saveDTO.getCrDeptId()).orElseGet(user::getDeptId));
            entity.setSystemCrTime(new Date());
            entity.setSyncTaskId(saveDTO.getSyncTaskId());
            entity.setChatId(saveDTO.getChatId());
            entity.setCrUserTrueName(StringUtils.showEmpty(user.getRealName(), user.getUsername()));
            entity.setRootId(Optional.ofNullable(saveDTO.getRootId()).orElse(0L));
            entity.setDataType(saveDTO.getDataType());
            entity.setDataClass(saveDTO.getDataClass());
            entity.setDataNo(getMaxNo(saveDTO.getDataClass(), user) + 1);
            entity.setClueNo(getOrderNo(saveDTO.getDataClass(), user));
            entity.setDataYear(TimeUtils.getFieldOfDate(new Date(), Calendar.YEAR));
            entity.setCrDistrictCode(user.getDept().getDistrictCode());
            entity.setCrDate(TimeUtils.stringToDate(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD)));
            entity.setPushType(saveDTO.getPushType());
            entity.setDraftsFlag(Optional.ofNullable(saveDTO.getDraftsFlag()).orElse(1));
            entity.setStatusCode(Status.CAOGAOXIANG.getCode());
        }
        // 草稿箱的数据默认都标记为正常类型
        if (Objects.equals(1, entity.getDraftsFlag())) {
            entity.setXslx(Constants.XSLX_COMMON);
        }
        if (TimeUtils.isValid(saveDTO.getXssj())) {
            entity.setCrTime(TimeUtils.stringToDate(saveDTO.getXssj()));
        }
        Optional.ofNullable(saveDTO.getCrDeptId()).ifPresent(entity::setCrDeptId);
        entity.setStReportDeptNames(saveDTO.getStReportDeptNames());
        entity.setStxsbh(saveDTO.getStxsbh());
        entity.setSthbxsbh(saveDTO.getSthbxsbh());
        entity.setClueSourceType(saveDTO.getClueSourceType());
        entity.setSourceDeptId(saveDTO.getSourceDeptId());
        entity.setSourceClueNo(saveDTO.getSourceClueNo());
        entity.setDataTitle(saveDTO.getDataTitle());
        entity.setDataContent(saveDTO.getDataContent());
        entity.setRawDataContent(saveDTO.getDataContent());
        entity.setClassicalFlag(Optional.ofNullable(saveDTO.getClassicalFlag()).orElse(0));
        entity.setTargetTime(saveDTO.getTargetTime());
        if (Constants.CN_JIN_QI.equals(saveDTO.getTargetTime())) {
            entity.setTargetTimeFormat(entity.getCrDate());
        } else {
            entity.setTargetTimeFormat(TimeUtils.stringToDate(saveDTO.getTargetTime()));
        }
        entity.setGroupType(saveDTO.getGroupType());
        entity.setGroupDetail(saveDTO.getGroupDetail());
        entity.setWqfs(saveDTO.getWqfs());
        entity.setXwfs(saveDTO.getXwfs());
        entity.setZxdd(saveDTO.getZxdd());
        entity.setXxdd(saveDTO.getXxdd());
        entity.setCcDeptIds(saveDTO.getCcDeptIds());
        entity.setSigner(saveDTO.getSigner());
        entity.setContactsUser(saveDTO.getContactsUser());
        entity.setPhone(saveDTO.getPhone());
        entity.setCheckLevel(saveDTO.getCheckLevel());
        if (TimeUtils.isValid(saveDTO.getFeedbackLimitTime())) {
            entity.setFeedbackLimitTime(TimeUtils.stringToDate(saveDTO.getFeedbackLimitTime()));
        } else {
            entity.setFeedbackLimitTime(null);
        }
        entity.setAttachment(saveDTO.getAttachment());
        String personType = getPersonType(persons);
        entity.setPersonType(personType);
        entity.setRelatedPersonZjhm(persons.stream()
                .map(RelatedPersonSaveDTO::getZjhm)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.joining(StringUtils.SEPARATOR_COMMA)));
    }

    private String getPersonType(List<RelatedPersonSaveDTO> persons) throws ServiceException {
        String defaultAreaCode = getCommonService().defaultAreaCode();
        boolean isLocal = false;
        boolean noLocal = false;
        for (RelatedPersonSaveDTO person : persons) {
            if (isStartWithCode(person.getGsdy(), defaultAreaCode)) {
                isLocal = true;
            } else {
                noLocal = true;
            }
        }
        if (isLocal && noLocal) {
            return Constants.PERSON_TYPE_ALL;
        } else if (noLocal) {
            return Constants.PERSON_TYPE_NO_LOCAL;
        } else {
            return Constants.PERSON_TYPE_LOCAL;
        }
    }

    @Override
    public Tuple2<String, String> export(ExportDTO dto) throws Exception {
        PreConditionCheck.checkNotNull(dto.getDataId(), new ServiceException("请选择需要导出的线索"));
        PreConditionCheck.checkNotEmpty(dto.getTemplateType(), new ServiceException("要情导出的模板类型不能为空"));
        final XianSuoEntityVo detail = detail(dto.getDataId());
        PreConditionCheck.checkNotNull(detail, new ServiceException("不存在可导出的数据"));
        final Map<String, String> map = new HashMap<>(7);
        map.put("areaName", detail.getCrDept().getShortName());
        map.put("clueNo", detail.getClueNo());
        map.put("reportTime", TimeUtils.dateToString(detail.getCrTime(), TimeUtils.YYYYMMDD_HHMMSS));
        map.put("dataTitle", detail.getDataTitle());
        map.put("dataContent", detail.getDataContent());
        map.put("signer", StringUtils.showEmpty(detail.getSigner()));
        map.put("checkLevel", detail.getCheckLevel());
        map.put("checkLimitTime", Objects.nonNull(detail.getFeedbackLimitTime())
                ? TimeUtils.dateToString(detail.getCrTime(), TimeUtils.YYYYMMDD_HHMMSS)
                : "");
        if (CollectionUtils.isNotEmpty(detail.getCcDepts())) {
            String ccDepts = detail.getCcDepts()
                    .stream()
                    .map(it -> StringUtils.showEmpty(it.getShortName(), it.getDeptName()))
                    .distinct()
                    .collect(Collectors.joining("，"));
            if (StringUtils.isNotEmpty(ccDepts)) {
                map.put("ccUnits", ccDepts);
            }
        } else {
            map.put("ccUnits", "");
        }
        // 线索相关人员
        List<XianSuoRelatedPersonEntity> personList = getRelatedPersonMapper().relatedPersonList(dto.getDataId());
        List<Map<String, String>> persons = new ArrayList<>(0);
        for (XianSuoRelatedPersonEntity person : personList) {
            Map<String, String> personMap = new HashMap<>(7);
            personMap.put("xm", person.getXm());
            personMap.put("zjhm", person.getZjhm());
            personMap.put("sjhwm", person.getSjhwm());
            personMap.put("hjd", person.getHjd());
            personMap.put("gsdymc", person.getGsdymc());
            persons.add(personMap);
        }
        final var templateType = StringUtils.showEmpty(dto.getTemplateType(), Constants.UP);
        final String tempName;
        if (Objects.equals(Constants.DOWN, templateType)) {
            // 线索下发
            tempName = "qzx.xiansuo.template.down.docx";
        } else {
            // 上报线索
            tempName = "qzx.xiansuo.template.up.docx";
        }
        final String fileName = findExportFilePath() + "/" + UUID.randomUUID() + ".docx";
        try (
                InputStream fis = WordUtils.getTemplateInputStream(tempName);
                XWPFDocument doc = new XWPFDocument(fis);
                FileOutputStream fos = new FileOutputStream(fileName)
        ) {
            XianSuoExportUtils.processTemplate(doc, map, persons);
            //保存文档
            doc.write(fos);
        } catch (Exception e) {
            log.error("线索导出异常：{}", e.getMessage(), e);
            throw new ServiceException("线索导出失败：" + e.getMessage());
        }
        return new Tuple2<>(
                detail.getDataTitle() + ".docx",
                fileName
        );
    }

    /**
     * makeVo<BR>
     *
     * @param it          参数
     * @param deptMapping 参数
     * @param userMapping 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/23 15:30
     */
    public XianSuoEntityVo makeVo(XianSuoBaseInfoEntity it, Map<Long, SimpleDeptVO> deptMapping, Map<String, UserDto> userMapping) {
        XianSuoEntityVo vo = makeVo(it, EntityConvertToVo.INSTANCE.entityToVo(it), deptMapping, userMapping);
        // 补充涉及人数
        vo.setSjrs(Optional.ofNullable(it.getRelatedPersons()).map(List::size).orElse(0));
        // 补充被合并的目前任务ID
        Optional.ofNullable(it.getMergeMainDataId())
                .filter(t -> t > 0L)
                .map(i -> getMapper().selectById(i))
                .ifPresent(i -> vo.setMergeMainClueNo(i.getClueNo()));
        vo.setSourceDept(deptMapping.get(it.getSourceDeptId()));
        if (Objects.nonNull(it.getPubDeptId())) {
            vo.setPubDept(deptMapping.get(it.getPubDeptId()));
        } else {
            // 默认用创建单位来填充历史数据
            vo.setPubDept(deptMapping.get(it.getCrDeptId()));
        }
        vo.setCcDepts(
                it.makeCcDeptIds()
                        .stream()
                        .map(deptMapping::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );
        vo.setDealDepts(
                Optional.ofNullable(it.getRelatedPersons())
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(XianSuoRelatedPersonEntity::getCheckDeptId)
                        .filter(Objects::nonNull)
                        .distinct()
                        .map(deptMapping::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );
        vo.setCheckLevel(StringUtils.showEmpty(vo.getCheckLevel(), "待定"));
        return addCommonInfo(it, vo);
    }

    /**
     * relatedPersonList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/23 15:29
     */
    @Override
    public List<XianSuoRelatedPersonVo> relatedPersonList(RelatedPersonQueryDTO dto) throws ServiceException {
        dto.isValid();
        final CurrentUser user = AuthHelper.getNotNullUser();
        final String shortDeptAreaCode;
        final Long deptId;
        final String gsdyShortCode;
        if (dto.getOnlyMyDept() == 1) {
            if (DeptUtils.canSeeChildrenData(user)) {
                shortDeptAreaCode = getAreaShortCode(spreadingAreaCode(user.getDept().getDistrictCode(), false));
                deptId = null;
            } else {
                shortDeptAreaCode = null;
                deptId = user.getDeptId();
            }
        } else {
            shortDeptAreaCode = null;
            deptId = null;
        }
        if (Objects.equals(1, dto.getOnlyLocal())) {
            gsdyShortCode = getAreaShortCode(spreadingAreaCode(user.getDept().getDistrictCode(), false));
        } else {
            gsdyShortCode = null;
        }
        XianSuoBaseInfoEntity entity = findById(dto.getDataId());
        var list = relatedPersonMapper.relatedPersonList(
                dto.getDataId(),
                deptId,
                shortDeptAreaCode,
                gsdyShortCode
        );
        List<Long> deptIds = new ArrayList<>();
        final var zjhms = new HashSet<String>();
        list.forEach(it -> {
            deptIds.add(it.getCrDeptId());
            if (it.getCheckDeptId() != null) {
                deptIds.add(it.getCheckDeptId());
            }
            zjhms.add(it.getZjhm());
        });
        final Map<String, Long> zjhmMapping = CollectionUtils.isEmpty(zjhms) ? Collections.EMPTY_MAP : getPersonLibraryMapper().findArchivesIdsByZjhm(zjhms)
                .stream()
                .collect(Collectors.toMap(GroupVo::getGroupName, GroupVo::getGroupValue, Math::max));
        var deptMapping = findDeptByIds(deptIds)
                .stream()
                .collect(Collectors.toMap(SimpleDeptVO::getDeptId, it -> it));
        return list.stream().map(it -> {
            XianSuoRelatedPersonVo vo = EntityConvertToVo.INSTANCE.entityToVo(it).initAssignToMe(user);
            vo.setCrDept(deptMapping.get(it.getCrDeptId()));
            if (it.getCheckDeptId() != null) {
                Optional.ofNullable(deptMapping.get(it.getCheckDeptId())).ifPresent(dept -> {
                    vo.setCheckDept(dept);
                    vo.setCheckDeptShowName(dept.getShortName());
                });
            }
            String code = BeanFactoryHolder.getEnv().getProperty("intelligence.xiansuo.checkDeptShowName.notShow.gsdymc.code");
            if (StringUtils.isNotEmpty(code) && !StringUtils.showEmpty(it.getGsdy()).startsWith(code)) {
                vo.setCheckDeptShowName(StringUtils.showEmpty(it.getGsdymc(), vo.getCheckDeptShowName()));
            }
            vo.setFeedbackTimeLimit(entity.getFeedbackLimitTime());
            vo.setArchivesId(zjhmMapping.get(it.getZjhm()));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * simpleList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/23 15:29
     */
    public List<XianSuoEntityWithRepeatPersonVo> simpleList(XianSuoSimpleListDTO dto) throws ServiceException {
        dto.isValid();
        final List<String> relatedPersons = new ArrayList<>(0);
        if (dto.getRepeatSourceDataId() != null) {
            Optional.ofNullable(findById(dto.getRepeatSourceDataId()))
                    .map(XianSuoBaseInfoEntity::getRelatedPersons)
                    .ifPresent(it -> it.forEach(item -> relatedPersons.add(item.getZjhm())));
        }
        List<XianSuoBaseInfoEntity> list = getMapper().findByIds(dto.makeDataIds(), dto.makeOrder());
        final List<XianSuoEntityWithRepeatPersonVo> data = new ArrayList<>(0);
        for (XianSuoBaseInfoEntity entity : list) {
            XianSuoEntityWithRepeatPersonVo vo = EntityConvertToVo.INSTANCE.voToVo(mergeDataOnDetail(null, entity, null));
            Optional.ofNullable(vo)
                    .map(XianSuoEntityWithRepeatPersonVo::getCrDept)
                    .map(SimpleDeptVO::getDeptType)
                    .map(Long::intValue)
                    .ifPresent(vo::setDeptType);
            if (!Objects.equals(vo.getDeptType(), DeptTypeEnum.POLICE_STATION.getCode())) {
                var len = StringUtils.showEmpty(getAreaShortCode(spreadingAreaCode(vo.getCrDept().getDistrictCode(), false)))
                        .length();
                switch (len) {
                    case 0:
                    case 2:
                        vo.setDeptType(DeptTypeEnum.PROVINCIAL.getCode());
                        break;
                    case 4:
                        vo.setDeptType(DeptTypeEnum.MUNICIPAL.getCode());
                        break;
                    case 6:
                        vo.setDeptType(DeptTypeEnum.COUNTY.getCode());
                        break;
                    default:
                }
            }
            if (dto.getDeptType() != null
                    && !Objects.equals(vo.getDeptType(), dto.getDeptType())) {
                // 过滤创建单位类型
                continue;
            }
            if (!relatedPersons.isEmpty()) {
                vo.setRepeatPersons(Optional.ofNullable(entity.getRelatedPersons())
                        .map(item -> item.stream()
                                .filter(it -> relatedPersons.contains(it.getZjhm()))
                                .map(EntityConvertToVo.INSTANCE::entityToVo)
                                .collect(Collectors.toList()))
                        .orElse(Collections.emptyList()));
            } else {
                vo.setRepeatPersons(Collections.emptyList());
            }
            data.add(vo);
        }
        return data;
    }

    /**
     * personLibrary<BR>
     *
     * @param request 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/23 15:31
     */
    public RestfulResultsV2<XianSuoPersonLibraryVo> personLibrary(ListParamsRequest request) {
        List<KeyValueTypeVO> baseFilter = request.getFilterParams()
                .stream()
                .map(FieldUtils::reBuildFilter)
                .collect(Collectors.toList());
        final var page = getPersonLibraryMapper().doPageSelect(
                baseFilter,
                request.getSearchParams(),
                request.getSortParams(),
                request.getPageParams().toPage()
        );
        final List<Long> deptIds = new ArrayList<>(0);
        page.getRecords()
                .forEach(it -> {
                    Optional.ofNullable(it.getCrDeptId()).ifPresent(deptIds::add);
                    Optional.ofNullable(it.getCheckDeptId()).ifPresent(deptIds::add);
                });
        Map<Long, SimpleDeptVO> deptMapping = CollectionUtils.isEmpty(deptIds) ? Collections.EMPTY_MAP
                : findDeptByIds(deptIds)
                .stream()
                .collect(Collectors.toMap(SimpleDeptVO::getDeptId, it -> it));
        Integer year = TimeUtils.getFieldOfDate(new Date(), Calendar.YEAR);
        var list = page.getRecords();
        final var zjhms = list.stream().map(XianSuoPersonLibraryEntity::getZjhm).collect(Collectors.toSet());
        final Map<String, Long> map = CollectionUtils.isEmpty(zjhms) ? Collections.EMPTY_MAP : getPersonLibraryMapper().findArchivesIdsByZjhm(zjhms)
                .stream()
                .collect(Collectors.toMap(GroupVo::getGroupName, GroupVo::getGroupValue, Math::max));
        return RestfulResultsV2.ok(
                        list.stream().map(it -> {
                            var vo = EntityConvertToVo.INSTANCE.entityToVo(it);
                            vo.setAge(Integer.max(year - Integer.valueOf(it.getZjhm().substring(6, 10)), 0));
                            if (Integer.valueOf(it.getZjhm().substring(16, 17)) % 2 == 0) {
                                vo.setXb("女");
                            } else {
                                vo.setXb("男");
                            }
                            vo.setArchivesId(map.get(it.getZjhm()));
                            vo.setRelatedClueIds(getPersonLibraryMapper().findClueIdsByZjhm(it.getZjhm()));
                            vo.setCrDept(deptMapping.get(it.getCrDeptId()));
                            vo.setCheckDept(deptMapping.get(it.getCheckDeptId()));
                            return vo;
                        }).collect(Collectors.toList())
                )
                .addPageNum(request.getPageParams().getPageNumber())
                .addPageSize(request.getPageParams().getPageSize())
                .addTotalCount(page.getTotal());
    }

    @Override
    public Optional<Long> consumerThirdData(PullDataVo pullDataVo, CurrentUser cityUser, CurrentUser provinceUser) throws ServiceException {
        if (Objects.isNull(cityUser)) {
            return Optional.empty();
        }
        final ProvinceClueVO clueVO = pullDataVo.getClueVO();
        final List<ProvinceCluePersonVO> provinceCluePersons = pullDataVo.getPersonVOList();
        final XianSuoBaseInfoEntity entity = findEntityByStXsbh(clueVO.getXsbh());
        final Integer status = clueHelper.getClueStatusMapping(clueVO.getCyztDm());
        final String key = clueHelper.conditionKey(clueVO.getSbdwDm(), clueVO.getHbqXs(), cityUser.getDept().getDistrictCode());
        boolean isLocal = clueHelper.checkIsLocal(cityUser.getDept().getDistrictCode(), clueVO.getSbdwDm());
        final Map<String, XianSuoRelatedPersonEntity> idCardPersonMap = makeIdCardPersonMap(clueVO, entity);
        final Optional<Long> opt;
        if (Objects.nonNull(entity) && Objects.equals(0, entity.getDraftsFlag())) {
            consumerExistData(cityUser, provinceUser, provinceCluePersons, idCardPersonMap, entity, clueVO, isLocal, status);
            opt = Optional.ofNullable(entity.getDataId());
        } else {
            final XianSuoSaveDTO saveDTO;
            switch (key) {
                case "sbsj":
                case "jsxj":
                    // 接收下级 & 上报上级
                    saveDTO = buildSaveDto(clueVO, cityUser, provinceCluePersons, idCardPersonMap);
                    saveDTO.setPushType(Constants.PUSHUP);
                    break;
                case "jssj":
                    // 非本地（自贡） 接收上级
                    saveDTO = buildSaveDto(clueVO, provinceUser, provinceCluePersons, idCardPersonMap);
                    saveDTO.setPushType(Constants.PUSHDOWN);
                    break;
                default:
                    throw new ServiceException("未知类型!");
            }
            saveDTO.setSyncTaskId(pullDataVo.getTaskId());
            // 优化草稿箱的数据
            Optional.ofNullable(entity).ifPresent(i -> {
                saveDTO.setDataId(i.getDataId());
                saveDTO.setRootId(i.getRootId());
            });
            opt = Optional.ofNullable(saveOrUpdate(saveDTO).getContext()).map(Long.class::cast);
            if (opt.isPresent()) {
                pushData(cityUser, provinceUser, opt.get(), key, status);
                // 更新归档
                if (saveDTO.getNeedArchive()) {
                    new LambdaUpdateChainWrapper<>(getMapper())
                            .set(XianSuoBaseInfoEntity::getXslx, Constants.XSLX_ARCHIVE)
                            .eq(XianSuoBaseInfoEntity::getDataId, opt.get())
                            .update();
                }
            }
        }
        if (opt.isPresent() && CollectionUtils.isNotEmpty(clueVO.getHbqXs())) {
            buildMerge(opt.get(), clueVO);
        }
        return opt;
    }

    private void buildMerge(Long dataId, ProvinceClueVO clueVO) {
        if (CollectionUtils.isEmpty(clueVO.getHbqXs())) {
            return;
        }
        XianSuoBaseInfoEntity entity = getMapper().selectById(dataId);
        if (Objects.isNull(entity)) {
            return;
        }
        final Set<Long> mergeIds = new HashSet<>(StringUtils.getLongList(entity.getMergeDataIds()));
        new LambdaQueryChainWrapper<>(getMapper())
                .select(XianSuoBaseInfoEntity::getDataId)
                .eq(XianSuoBaseInfoEntity::getIsDel, 0)
                .eq(XianSuoBaseInfoEntity::getDraftsFlag, 0)
                .in(XianSuoBaseInfoEntity::getStxsbh, clueVO.getHbqXs())
                .list()
                .forEach(i -> {
                    if (!Objects.equals(dataId, i.getDataId())) {
                        mergeIds.add(i.getDataId());
                    }
                });
        if (CollectionUtils.isNotEmpty(mergeIds)) {
            // 合并的主数据更新合并的ID串
            new LambdaUpdateChainWrapper<>(getMapper())
                    .set(XianSuoBaseInfoEntity::getMergeDataIds, StringUtils.join(mergeIds.toArray(), StringUtils.SEPARATOR_COMMA))
                    .eq(XianSuoBaseInfoEntity::getDataId, entity.getDataId())
                    .update();
            // 被合并数据设置主ID
            new LambdaUpdateChainWrapper<>(getMapper())
                    .set(XianSuoBaseInfoEntity::getMergeMainDataId, entity.getDataId())
                    .set(XianSuoBaseInfoEntity::getXslx, Constants.XSLX_ARCHIVE)
                    .in(XianSuoBaseInfoEntity::getDataId, mergeIds)
                    .eq(XianSuoBaseInfoEntity::getIsDel, 0)
                    .eq(XianSuoBaseInfoEntity::getDraftsFlag, 0)
                    .update();
            var exists = getDataRelationMappingMgr().getRelationList(
                    module(),
                    entity.getDataId(),
                    Constants.DEPT,
                    Constants.PUSHDOWN
            ).stream().map(DataRelationMappingEntity::getObjId).collect(Collectors.toSet());
            // b已有关系
            getDataRelationMappingMgr().getRelationList(
                    module(),
                    mergeIds,
                    Constants.DEPT,
                    Constants.PUSHDOWN
            ).forEach(it -> {
                if (!exists.contains(it.getObjId())) {
                    exists.add(it.getObjId());
                    DataRelationMappingEntity relationMapping = EntityConvertToVo.INSTANCE.clone(it);
                    relationMapping.setDataId(null);
                    relationMapping.setRelationId(entity.getDataId());
                    getDataRelationMappingMgr().insertEntity(relationMapping);
                }
            });
        }
    }

    private void consumerExistData(CurrentUser cityUser, CurrentUser provinceUser, List<ProvinceCluePersonVO> provinceCluePersons, Map<String, XianSuoRelatedPersonEntity> idCardPersonMap, XianSuoBaseInfoEntity entity, ProvinceClueVO clueVO, boolean isLocal, Integer status) throws ServiceException {
        List<RelatedPersonSaveDTO> persons = clueHelper.buildRelatedPerson(cityUser, provinceCluePersons, idCardPersonMap);
        final String personType = getPersonType(persons);
        entity.setPersonType(personType);
        entity.setRelatedPersonZjhm(persons.stream()
                .map(RelatedPersonSaveDTO::getZjhm)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.joining(StringUtils.SEPARATOR_COMMA)));
        if (clueHelper.isShengTingPushDown(clueVO.getXsbh())
                && CollectionUtils.isNotEmpty(clueVO.getHbqXs())) {
            entity.setDataContent(StringUtils.showEmpty(clueVO.getNr(), entity.getDataContent()));
            if (TimeUtils.isValid(clueVO.getXssj())) {
                entity.setCrTime(TimeUtils.stringToDate(clueVO.getXssj()));
            }
            entity.setSthbxsbh(String.join(StringUtils.SEPARATOR_COMMA, clueVO.getHbqXs()));
        }
        if (Objects.equals(Constants.XSLX_ARCHIVE, entity.getXslx())
                && !clueHelper.needArchive(clueVO.getXsbh(), provinceCluePersons)) {
            // 取消归档状态
            entity.setXslx(Constants.XSLX_COMMON);
            log.info("线索[{}]被取消归档", clueVO.getXsbh());
        }
        Optional.ofNullable(clueHelper.buildStReportDeptNames(clueVO))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(entity::setStReportDeptNames);
        // 找到单位就回填
        findDeptIdByCode(clueVO.getSbdwDm()).ifPresent(entity::setCrDeptId);
        final var checkLevel = StringUtils.showEmpty(clueVO.getHcdjMc(), "关注");
        if (StringUtils.isEmpty(entity.getCheckLevel())) {
            entity.setCheckLevel(checkLevel);
        }
        // 更新反馈时限
        clueHelper.buildFeedbackLimitTime(clueVO.getXssj(), checkLevel)
                .map(TimeUtils::stringToDate)
                .ifPresent(entity::setFeedbackLimitTime);
        getMapper().updateById(entity);
        // 重新构建人员关系信息
        if (isLocal) {
            this.rebuildRelatedPersonMapping(cityUser, entity.getDataId(), persons);
        } else {
            this.rebuildRelatedPersonMapping(provinceUser, entity.getDataId(), persons);
        }
        // 线索状态变更日志
        if (Objects.nonNull(status)) {
            if (!Objects.equals(status, Status.DAICHULI.getCode())
                    && Objects.equals(Status.DAICHULI.getCode(), entity.getStatusCode())) {
                doClueActionLog(status, entity, provinceUser);
                entity.setStatusCode(status);
                // ST采用之后，对应发布单位改为ST
                if (Objects.equals(provinceUser.getDeptId().toString(), entity.getPushUpDeptIds())) {
                    entity.setPubDeptId(provinceUser.getDeptId());
                }
                getMapper().updateById(entity);
            }
        }
    }

    private Map<String, XianSuoRelatedPersonEntity> makeIdCardPersonMap(ProvinceClueVO clueVO, XianSuoBaseInfoEntity entity) {
        final List<XianSuoRelatedPersonEntity> personList = new ArrayList<>(0);
        if (Objects.nonNull(entity)) {
            personList.addAll(getRelatedPersonMapper().relatedPersonList(entity.getDataId()));
        }
        if (Objects.nonNull(clueVO)) {
            final Set<String> stxzbh = new HashSet<>(1);
            if (StringUtils.isNotEmpty(clueVO.getXsbh())) {
                stxzbh.add(clueVO.getXsbh());
            }
            if (CollectionUtils.isNotEmpty(clueVO.getHbqXs())) {
                stxzbh.addAll(clueVO.getHbqXs());
            }
            if (CollectionUtils.isNotEmpty(stxzbh)) {
                personList.addAll(getRelatedPersonMapper().findPersonListByStxsbhs(stxzbh));
            }
        }
        return personList.stream().collect(Collectors.toMap(
                XianSuoRelatedPersonEntity::getZjhm,
                Function.identity(),
                (a, b) -> a.getDataId() > b.getDataId() ? b : a)
        );
    }

    private void pushData(CurrentUser cityUser, CurrentUser provinceUser, Long dataId, String key, Integer status) throws ServiceException {
        switch (key) {
            case "sbsj":
            case "jsxj":
                XianSuoReportDataDTO reportDataDTO = new XianSuoReportDataDTO();
                // 接收下级  推送给本市局即可
                reportDataDTO.setDataId(dataId);
                reportDataDTO.setUser(cityUser);
                // 跳过推送ST的行为
                reportDataDTO.setSkipPushSt(true);
                reportDataDTO.setDeptId(Objects.equals("sbsj", key) ? provinceUser.getDeptId() : cityUser.getDeptId());
                reportDataDTO.setSystemCall(true);
                BaseAction.findAction(reportDataDTO.getActionEnum()).doAction(reportDataDTO);
                break;
            case "jssj":
                XianSuoPushDataDTO xianSuoPushDataDTO = new XianSuoPushDataDTO();
                xianSuoPushDataDTO.setUser(provinceUser);
                xianSuoPushDataDTO.setTargetUser(cityUser);
                xianSuoPushDataDTO.setDataId(dataId);
                xianSuoPushDataDTO.setSystemCall(true);
                BaseAction.findAction(xianSuoPushDataDTO.getActionEnum()).doAction(xianSuoPushDataDTO);
                break;
            default:
                throw new ServiceException("未知类型!");
        }
        // 记录状态变更日志
        XianSuoBaseInfoEntity byId = findById(dataId);
        // 线索状态变更日志
        if (Objects.nonNull(status)) {
            if (!Objects.equals(status, Status.DAICHULI.getCode())
                    && Objects.equals(Status.DAICHULI.getCode(), byId.getStatusCode())) {
                doClueActionLog(status, byId, provinceUser);
                byId.setStatusCode(status);
                // ST采用之后，对应发布单位改为ST
                if (Objects.equals(provinceUser.getDeptId().toString(), byId.getPushUpDeptIds())) {
                    byId.setPubDeptId(provinceUser.getDeptId());
                }
                getMapper().updateById(byId);
            }
        }
    }

    private XianSuoBaseInfoEntity findEntityByStXsbh(String xsbh) {
        return new LambdaQueryChainWrapper<>(getMapper())
                .eq(XianSuoBaseInfoEntity::getStxsbh, xsbh)
                .eq(XianSuoBaseInfoEntity::getIsDel, 0)
                .eq(XianSuoBaseInfoEntity::getDraftsFlag, 0)
                .orderByDesc(XianSuoBaseInfoEntity::getDataId)
                .page(new Page<>(1, 1))
                .getRecords()
                .stream()
                .findFirst()
                .orElseGet(
                        () -> {
                            // 查询是否存在草稿箱数据
                            var ids = new LambdaQueryChainWrapper<>(getMapper())
                                    .select(XianSuoBaseInfoEntity::getDataId)
                                    .eq(XianSuoBaseInfoEntity::getStxsbh, xsbh)
                                    .eq(XianSuoBaseInfoEntity::getIsDel, 0)
                                    .eq(XianSuoBaseInfoEntity::getDraftsFlag, 1)
                                    .orderByDesc(XianSuoBaseInfoEntity::getDataId)
                                    .list()
                                    .stream()
                                    .map(XianSuoBaseInfoEntity::getDataId)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(ids)) {
                                return null;
                            }
                            final Long dataId = ids.get(0);
                            // 标记其他数据为删除
                            new LambdaUpdateChainWrapper<>(getMapper())
                                    .set(XianSuoBaseInfoEntity::getIsDel, 1)
                                    .in(XianSuoBaseInfoEntity::getDataId, ids)
                                    .ne(XianSuoBaseInfoEntity::getDataId, dataId)
                                    .eq(XianSuoBaseInfoEntity::getIsDel, 0)
                                    .eq(XianSuoBaseInfoEntity::getDraftsFlag, 1)
                                    .update();
                            return getMapper().selectById(dataId);
                        }
                );
    }

    private void doClueActionLog(Integer statusCode, XianSuoBaseInfoEntity entity, CurrentUser user) {
        entity.setStatusCode(statusCode);
        getMapper().updateById(entity);
        final var relationList = getDataRelationMappingMgr().getRelationList(
                module(),
                entity.getDataId(),
                Constants.DEPT,
                Constants.PUSHUP
        );
        for (DataRelationMappingEntity mappingEntity : relationList) {
            mappingEntity.setStatusCode(statusCode);
            getDataRelationMappingMgr().updateEntity(mappingEntity);
        }
        String content;
        String logType;
        if (Status.YICAIYONG.getCode().equals(statusCode)) {
            logType = ActionEnum.XIANSUO_DINGBAN_DATA.getLogType();
            content = "根据省厅接口回流，变更状态为已采用";
        } else if (Status.BUCAIYONG.getCode().equals(statusCode)) {
            logType = ActionEnum.XIANSUO_NOT_DEAL_DATE.getLogType();
            content = "根据省厅接口回流，变更状态为不采用";
        } else {
            return;
        }
        ActionLogEntity log = ActionLogEntity.addInfoOnCreate(user, new ActionLogEntity());
        log.setContent(content);
        log.setLogType(logType);
        log.setObjType(module());
        log.setObjId(entity.getDataId());
        log.setObjRootId(entity.getRootId());
        getActionLogMapper().insert(log);
    }

    private XianSuoSaveDTO buildSaveDto(ProvinceClueVO clueVO, CurrentUser user, List<ProvinceCluePersonVO> personList, Map<String, XianSuoRelatedPersonEntity> idCardPersonMap) {
        XianSuoSaveDTO dto = new XianSuoSaveDTO();
        // 省厅回流数据就简单校验
        dto.setSimpleCheck(true);
        dto.setUser(user);
        // 找到单位就回填
        findDeptIdByCode(clueVO.getSbdwDm()).ifPresent(dto::setCrDeptId);
        dto.setStxsbh(clueVO.getXsbh());
        if (CollectionUtils.isNotEmpty(clueVO.getHbqXs())) {
            dto.setSthbxsbh(String.join(",", clueVO.getHbqXs()));
        }
        dto.setStReportDeptNames(clueHelper.buildStReportDeptNames(clueVO));
        dto.setXssj(clueVO.getXssj());
        dto.setAttachment("[]");
        dto.setDataId(0L);
        dto.setDraftsFlag(1);
        dto.setDataTitle(clueVO.getXsbt());
        dto.setDataContent(clueVO.getNr());
        if (!TimeUtils.isValid(clueVO.getZxsj())) {
            dto.setTargetTime(Constants.CN_JIN_QI);
        } else {
            dto.setTargetTime(TimeUtils.stringToString(TimeUtils.YYYYMMDD_HHMMSS, clueVO.getZxsj(), TimeUtils.YYYYMMDD));
        }
        dto.setGroupType(clueVO.getQtlxMc());
        dto.setGroupDetail(clueVO.getQtxlMc());
        dto.setWqfs(clueVO.getWqfsMc());
        dto.setXwfs(clueVO.getXwfsMc());
        dto.setZxdd(clueVO.getZxddMc());
        dto.setXxdd(clueVO.getZxdXxddMc());
        var clueSourceType = StringUtils.showEmpty(clueVO.getXslylxMc(), Constants.SGAT);
        if (Constants.ST_LY.contains(clueSourceType)) {
            dto.setClueSourceType(clueSourceType);
        } else {
            dto.setClueSourceType(Constants.SGAT);
        }
        if (Constants.SJDW.equals(clueVO.getLydwMc())) {
            dto.setSourceDeptId(user.getDeptId());
        }
        dto.setSigner(StringUtils.showEmpty(clueVO.getQfr(), user.getRealName()));
        dto.setContactsUser(StringUtils.showEmpty(clueVO.getNgr(), user.getRealName()));
        dto.setPhone(StringUtils.showEmpty(clueVO.getLxdh(), user.getMobile()));
        dto.setRelatedPerson(JsonUtil.toJsonString(clueHelper.buildRelatedPerson(user, personList, idCardPersonMap)));
        dto.setNeedArchive(clueHelper.needArchive(clueVO.getXsbh(), personList));
        var checkLevel = StringUtils.showEmpty(clueVO.getHcdjMc(), "关注");
        dto.setCheckLevel(checkLevel);
        clueHelper.buildFeedbackLimitTime(clueVO.getXssj(), checkLevel).ifPresent(dto::setFeedbackLimitTime);
        return dto;
    }

    @Override
    public String key() {
        return Constants.XIANSUO;
    }

    @Override
    public String desc() {
        return "线索";
    }
}
