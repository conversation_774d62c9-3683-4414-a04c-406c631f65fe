package com.trs.police.intelligence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.common.utils.StringUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/4 16:22
 * @since 1.0
 */
@Data
@TableName("tb_intelligence_province_clue_fail_reason")
public class ProvinceClueFailReasonEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long dataId;

    @TableField
    private Date crTime = new Date();

    @TableField
    private Date updateTime = new Date();

    @TableField
    private String taskId;

    @TableField
    private String stxsbh;

    @TableField
    private String reasonContent;

    /**
     * of<BR>
     *
     * @param taskId 参数
     * @param stxsbh 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/4 16:33
     */
    public static ProvinceClueFailReasonEntity of(String taskId, String stxsbh) {
        ProvinceClueFailReasonEntity entity = new ProvinceClueFailReasonEntity();
        entity.setTaskId(StringUtils.showEmpty(taskId, "未知"));
        entity.setStxsbh(stxsbh);
        return entity;
    }
}
