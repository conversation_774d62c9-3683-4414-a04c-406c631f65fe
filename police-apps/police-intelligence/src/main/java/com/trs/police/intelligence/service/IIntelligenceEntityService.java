package com.trs.police.intelligence.service;

import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseVO;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.intelligence.dto.*;
import com.trs.police.intelligence.dto.third.PullDataDTO;
import com.trs.police.intelligence.vo.BaseEntityVo;
import com.trs.police.intelligence.vo.GroupVo;
import com.trs.police.intelligence.vo.third.PullDataVo;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.web.builder.base.IKey;
import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.Tuple2;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * IIIntelligenceEntityService
 *
 * @param <DetailVo> 参数
 * @param <ListVo>   参数
 * @param <Save>     参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 22:35
 * @since 1.0
 */
public interface IIntelligenceEntityService<Save extends BaseSaveDTO, ListVo extends BaseVO, DetailVo extends BaseVO>
        extends IKey {

    /**
     * 获取最大的编号<BR>
     *
     * @param dataClass 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 09:22
     */
    default Integer getMaxNo(String dataClass) throws ServiceException {
        return getMaxNo(dataClass, null);
    }

    /**
     * 获取最大的编号<BR>
     *
     * @param dataClass 参数
     * @param user      指定用户
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 09:22
     */
    Integer getMaxNo(String dataClass, CurrentUser user) throws ServiceException;

    /**
     * getOrderNo<BR>
     *
     * @param dataClass 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 11:52
     */
    default String getOrderNo(String dataClass) throws ServiceException {
        return getOrderNo(dataClass, null);
    }

    /**
     * getOrderNo<BR>
     *
     * @param dataClass 参数
     * @param user      当前用户
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 11:52
     */
    String getOrderNo(String dataClass, CurrentUser user) throws ServiceException;

    /**
     * saveOrUpdate<BR>
     *
     * @param saveDTO 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 22:39
     */
    Report<String> saveOrUpdate(Save saveDTO) throws ServiceException;

    /**
     * detail<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 22:53
     */
    DetailVo detail(EntityDetailDTO dto) throws ServiceException;

    /**
     * detail<BR>
     *
     * @param dataId 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/22 12:16
     */
    default DetailVo detail(Long dataId) throws ServiceException {
        EntityDetailDTO dto = new EntityDetailDTO();
        dto.setDataId(dataId);
        dto.setVersionId(0L);
        return detail(dto);
    }

    /**
     * queryList<BR>
     *
     * @param from    参数
     * @param request 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 23:02
     */
    RestfulResultsV2<ListVo> queryList(String from, ListParamsRequest request) throws ServiceException;

    /**
     * categoryList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/16 19:42
     */
    List<CountItem> categoryList(CategorySearchDto dto) throws ServiceException;

    /**
     * todoList<BR>
     *
     * @param pageParams 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/22 19:57
     */
    PageResult<TodoTaskVO> todoList(PageParams pageParams) throws ServiceException;

    /**
     * getUnReadNum<BR>
     *
     * @param from 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/18 23:37
     */
    List<GroupVo> getUnReadNum(String from) throws ServiceException;

    /**
     * export<BR>
     *
     * @param dto 参数
     * @return 结果(下载文件名, 生成的临时文件名)
     * @throws Exception 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/22 10:58
     */
    Tuple2<String, String> export(ExportDTO dto) throws Exception;

    /**
     * consumerThirdData<BR>
     *
     * @param pullDataVo   参数
     * @param cityUser     本地接收的用户（市局用户）
     * @param provinceUser 省厅用户
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/25 10:09
     */
    Optional<Long> consumerThirdData(PullDataVo pullDataVo, CurrentUser cityUser, CurrentUser provinceUser) throws ServiceException;

    /**
     * finishedThirdData<BR>
     *
     * @param dto          参数
     * @param cityUser     本地接收的用户（市局用户）
     * @param provinceUser 省厅用户
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/21 10:30
     */
    void finishedConsumerThirdData(PullDataDTO dto, CurrentUser cityUser, CurrentUser provinceUser) throws ServiceException;

    /**
     * relatedPersonList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/5 18:08
     */
    List<? extends BaseEntityVo> relatedPersonList(RelatedPersonQueryDTO dto) throws ServiceException;
}
