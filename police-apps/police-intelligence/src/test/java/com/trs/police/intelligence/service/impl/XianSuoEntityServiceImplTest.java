package com.trs.police.intelligence.service.impl;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.intelligence.PoliceIntelligenceApplication;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.dto.RelatedPersonQueryDTO;
import com.trs.police.intelligence.dto.RelatedPersonSaveDTO;
import com.trs.police.intelligence.dto.XianSuoSaveDTO;
import com.trs.police.intelligence.dto.XianSuoSimpleListDTO;
import com.trs.police.intelligence.mapper.XianSuoBaseInfoMapper;
import com.trs.police.intelligence.vo.third.ProvinceCluePersonVO;
import com.trs.police.intelligence.vo.third.ProvinceClueVO;
import com.trs.police.intelligence.vo.third.PullDataVo;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = PoliceIntelligenceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class XianSuoEntityServiceImplTest extends BaseTestCase {

    @Resource
    private XianSuoEntityServiceImpl service;

    @Resource
    private XianSuoBaseInfoMapper mapper;

    @BeforeEach
    @Override
    public void before() {
        super.before();
        service.setPermissionService(permissionService);
    }

    @Test
    void getMaxNo() throws Exception {
        print(service.getMaxNo(null));
    }

    @Test
    void getOrderNo() throws Exception {
        print(service.getOrderNo(null));
    }

    //    @Test
    void saveOrUpdate() throws Exception {
        XianSuoSaveDTO dto = new XianSuoSaveDTO();
        dto.setDataId(0L);
        dto.setRootId(0L);
        dto.setPushType(Constants.PUSHUP);
        dto.setDataTitle("测试标题");
        dto.setDataContent("测试内容");
        dto.setDraftsFlag(1);
        dto.setSourceDeptId(2L);
        dto.setTargetTime(Constants.CN_JIN_QI);
        dto.setGroupType("投资受损群体");
        dto.setGroupDetail("四川信托");
        dto.setWqfs("涉仿（限5人以上）");
        dto.setXwfs("网上煽动");

        RelatedPersonSaveDTO person = new RelatedPersonSaveDTO();
        person.setZjhm("510501194410101254");
        person.setXm("TRS");
        person.setSjhwm("13412341234/trs");
        person.setHjd("四川省广元市利州区");
        person.setGsdy("510802000000");
        person.setGsdymc("利州区");
        person.setIsSearched(0);

        RelatedPersonSaveDTO person2 = new RelatedPersonSaveDTO();
        person2.setZjhm("510802199410101244");
        person2.setXm("TRS2");
        person2.setSjhwm("13412341234/trs");
        person2.setHjd("四川省广元市利州区");
        person2.setGsdy("510802000000");
        person2.setGsdymc("利州区");
        person2.setIsSearched(0);
        dto.setRelatedPerson(JSON.toJSONString(List.of(person, person2)));

        dto.setZxdd("赴省");
        dto.setXxdd("省政府");
        dto.setSigner("TRS");
        dto.setContactsUser("TRS");
        dto.setPhone("13412341234");
        dto.setCheckLevel("一级");
        dto.setFeedbackLimitTime(TimeUtils.getCurrentDate());
        print(service.saveOrUpdate(dto));
    }

    @Test
    void queryList() throws Exception {
        ListParamsRequest request = new ListParamsRequest(PageParams.getDefaultPageParams());
        print(service.queryList(Constants.DRAFTS, request));
        request = new ListParamsRequest(new PageParams(3, 1));
        print(service.queryList(Constants.DRAFTS, request));
//        print(service.queryList(Constants.UP, request));
    }

    @Test
    void relatedPersonList() throws Exception {
        RelatedPersonQueryDTO dto = new RelatedPersonQueryDTO();
        dto.setDataId(6L);
        USER.getDept().setDistrictCode("510802");
        var v1 = service.relatedPersonList(dto);
        print(v1);
        dto.setOnlyLocal(1);
        v1 = service.relatedPersonList(dto);
        print(v1);
        var d = mapper.findByIds(List.of(6L));
        print(d);
    }

    @Test
    void simpleList() throws Exception {
        XianSuoSimpleListDTO dto = new XianSuoSimpleListDTO();
        dto.setDataIds("4,6");
        print(service.simpleList(dto));
        dto.setRepeatSourceDataId(4L);
        print(service.simpleList(dto));
    }

    @Test
    void testJsonDeserialize() throws Exception {
        var s = "[\n" +
                "    {\n" +
                "        \"gsdy\": \"510502000000\",\n" +
                "        \"gsdymc\": \"泸州市江阳区\",\n" +
                "        \"hjd\": \"泸州市江阳区\",\n" +
                "        \"isSearched\": \"1\",\n" +
                "        \"sjhwm\": \"13012345678\",\n" +
                "        \"xm\": \"张三六\",\n" +
                "        \"zjhm\": \"510104195203260552\",\n" +
                "        \"checkArr\": [\n" +
                "            2,\n" +
                "            503\n" +
                "        ],\n" +
                "        \"checkDeptId\": 503,\n" +
                "        \"checkDept\": {\n" +
                "            \"deptCode\": \"5105\",\n" +
                "            \"deptId\": 503,\n" +
                "            \"deptName\": \"实战服务中心\",\n" +
                "            \"deptType\": 6,\n" +
                "            \"shortName\": \"实战服务中心\"\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        \"gsdy\": \"510502000000\",\n" +
                "        \"gsdymc\": \"泸州市江阳区\",\n" +
                "        \"hjd\": \"泸州市江阳区\",\n" +
                "        \"isSearched\": \"0\",\n" +
                "        \"sjhwm\": \"13012345678\",\n" +
                "        \"xm\": \"41-4\",\n" +
                "        \"zjhm\": \"51132320030407717X\",\n" +
                "        \"checkArr\": [\n" +
                "            2,\n" +
                "            632\n" +
                "        ],\n" +
                "        \"checkDeptId\": 632,\n" +
                "        \"checkDept\": {\n" +
                "            \"deptCode\": \"45454\",\n" +
                "            \"deptId\": 632,\n" +
                "            \"deptName\": \"真正的实战服务中心\",\n" +
                "            \"deptType\": 4,\n" +
                "            \"shortName\": \"真正的实战服务中心\"\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        \"gsdy\": \"510303000000\",\n" +
                "        \"gsdymc\": \"自贡市贡井区\",\n" +
                "        \"hjd\": \"自贡市贡井区\",\n" +
                "        \"isSearched\": \"\",\n" +
                "        \"sjhwm\": \"13012345678\",\n" +
                "        \"xm\": \"马俊\",\n" +
                "        \"zjhm\": \"341225199012156837\",\n" +
                "        \"checkArr\": [\n" +
                "            2,\n" +
                "            632\n" +
                "        ],\n" +
                "        \"checkDeptId\": 632,\n" +
                "        \"checkDept\": {\n" +
                "            \"deptCode\": \"45454\",\n" +
                "            \"deptId\": 632,\n" +
                "            \"deptName\": \"真正的实战服务中心\",\n" +
                "            \"deptType\": 4,\n" +
                "            \"shortName\": \"真正的实战服务中心\"\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        \"gsdy\": \"51032208000000\",\n" +
                "        \"gsdymc\": \"福善镇\",\n" +
                "        \"hjd\": \"福善镇\",\n" +
                "        \"isSearched\": null,\n" +
                "        \"sjhwm\": \"13012345678\",\n" +
                "        \"xm\": \"郑斯英\",\n" +
                "        \"zjhm\": \"45282219640910154X\",\n" +
                "        \"checkArr\": [\n" +
                "            2,\n" +
                "            632\n" +
                "        ],\n" +
                "        \"checkDeptId\": 632,\n" +
                "        \"checkDept\": {\n" +
                "            \"deptCode\": \"45454\",\n" +
                "            \"deptId\": 632,\n" +
                "            \"deptName\": \"真正的实战服务中心\",\n" +
                "            \"deptType\": 4,\n" +
                "            \"shortName\": \"真正的实战服务中心\"\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        \"gsdy\": \"510104000000\",\n" +
                "        \"gsdymc\": \"成都市锦江区\",\n" +
                "        \"hjd\": \"成都市锦江区\",\n" +
                "        \"isSearched\": \"\",\n" +
                "        \"sjhwm\": \"13012345678\",\n" +
                "        \"xm\": \"41-2\",\n" +
                "        \"zjhm\": \"51132320010611745X\",\n" +
                "        \"checkArr\": [\n" +
                "            2,\n" +
                "            632\n" +
                "        ],\n" +
                "        \"checkDeptId\": 632,\n" +
                "        \"checkDept\": {\n" +
                "            \"deptCode\": \"45454\",\n" +
                "            \"deptId\": 632,\n" +
                "            \"deptName\": \"真正的实战服务中心\",\n" +
                "            \"deptType\": 4,\n" +
                "            \"shortName\": \"真正的实战服务中心\"\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        \"gsdy\": \"510502000000\",\n" +
                "        \"gsdymc\": \"泸州市江阳区\",\n" +
                "        \"hjd\": \"泸州市江阳区\",\n" +
                "        \"isSearched\": \"\",\n" +
                "        \"sjhwm\": \"13012345678\",\n" +
                "        \"xm\": \"导入测试1\",\n" +
                "        \"zjhm\": \"510106198707116155\",\n" +
                "        \"checkArr\": [\n" +
                "            2,\n" +
                "            632\n" +
                "        ],\n" +
                "        \"checkDeptId\": 632,\n" +
                "        \"checkDept\": {\n" +
                "            \"deptCode\": \"45454\",\n" +
                "            \"deptId\": 632,\n" +
                "            \"deptName\": \"真正的实战服务中心\",\n" +
                "            \"deptType\": 4,\n" +
                "            \"shortName\": \"真正的实战服务中心\"\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        \"gsdy\": \"510322000000\",\n" +
                "        \"gsdymc\": \"自贡市富顺县\",\n" +
                "        \"hjd\": \"自贡市富顺县\",\n" +
                "        \"isSearched\": \"\",\n" +
                "        \"sjhwm\": \"15023201234\",\n" +
                "        \"xm\": \"张董青\",\n" +
                "        \"zjhm\": \"14272919731002301X\",\n" +
                "        \"checkArr\": [\n" +
                "            2,\n" +
                "            632\n" +
                "        ],\n" +
                "        \"checkDeptId\": 632,\n" +
                "        \"checkDept\": {\n" +
                "            \"deptCode\": \"45454\",\n" +
                "            \"deptId\": 632,\n" +
                "            \"deptName\": \"真正的实战服务中心\",\n" +
                "            \"deptType\": 4,\n" +
                "            \"shortName\": \"真正的实战服务中心\"\n" +
                "        }\n" +
                "    }\n" +
                "]";
        var list = JsonUtil.parseArray(s, RelatedPersonSaveDTO.class);
        print(list);
    }

    @Test
    void consumerThirdData() throws Exception {
        final String xsbh = "XS5100002025041401740";
        final ProvinceCluePersonVO person = new ProvinceCluePersonVO();
        person.setXsbh(xsbh);
        person.setXm("张三");
        person.setRySfz("511722200109080192");
        person.setHjdDm("510500");
        person.setHjdMc("泸州市");
        person.setSjh("13412345641");
        person.setHcztDm("01");
        person.setHcztMc("工作中");

        final ProvinceCluePersonVO person2 = new ProvinceCluePersonVO();
        person2.setXsbh(xsbh);
        person2.setXm("金日成");
        person2.setRySfz("43407820171108316X");
        person2.setHjdDm("510500");
        person2.setHjdMc("泸州市");
        person2.setSjh("13412341234");
        person2.setHcztDm("01");
        person2.setHcztMc("工作中");

        final ProvinceClueVO clueVO = new ProvinceClueVO();
        clueVO.setXslylxDm("30021");
        clueVO.setXslylxMc("厅部门");
        clueVO.setLydwDm("510500");
        clueVO.setLydwMc("市局单位");
        clueVO.setLybh("LYBH20240925");
        clueVO.setLyrq("2024-09-25 10:00:00");
        clueVO.setHcdjDm("4");
        clueVO.setHcdjMc("四级");
        clueVO.setXsFx("方向A");
        clueVO.setXsbh(xsbh);
        clueVO.setXsbt("线索标题");
        clueVO.setQtlxDm("01");
        clueVO.setQtlxMc("“明天系”群体");
        clueVO.setQtxlDm("010001");
        clueVO.setQtxlMc("明天系群体");
        clueVO.setWqfsDm("1");
        clueVO.setWqfsMc("暴力极端方式维权(人员不限制)");
        clueVO.setXwfsDm("01");
        clueVO.setXwfsMc("网上煽动");
        clueVO.setZxddDm("01");
        clueVO.setZxddMc("到省");
        clueVO.setZxdXxddDm("0199");
        clueVO.setZxdXxddMc("其他省级部门");
        clueVO.setZxdJtdd("具体地点");
        clueVO.setMgjdDm("0605");
        clueVO.setMgjdMc("春节");
        clueVO.setZxsj("2025-01-01 00:00:00");
        clueVO.setNr("这是线索内容描述");
        clueVO.setSjrs(1);
        clueVO.setSjdxDm("0");
        clueVO.setSjdxMc("省内");
        clueVO.setNgr("拟稿人");
        clueVO.setQfr("签发人");
        clueVO.setLxdh("13812345678");
        clueVO.setCyztDm("1");
        clueVO.setCyztMc("已采用");
        clueVO.setXszt(1);
        clueVO.setXssj("2025-01-01 09:00:00");
        clueVO.setSbdwDm("510500");
        clueVO.setSbdwMc("泸州市公安局");
        clueVO.setHbqXs(List.of("xs015"));

        PullDataVo pullDataVo = new PullDataVo();
        pullDataVo.setTaskId(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS2));
        pullDataVo.setClueVO(clueVO);
        pullDataVo.setPersonVOList(List.of(person, person2));
        service.consumerThirdData(pullDataVo, USER, USER);
    }
}