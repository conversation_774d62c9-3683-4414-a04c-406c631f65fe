1.5

## 更新日志

- XMKFB-1088[XMKFB-885] 后 - 要情模板列表功能开发
- XMKFB-1089[XMKFB-885] 后 - 新增要情功能开发
- XMKFB-1118[XMKFB-885] 后 - 要情列表功能开发
- XMKFB-1172[XMKFB-885] 后 - 要情导出功能开发
- XMKFB-1173[XMKFB-977] 后 - 指令相关功能开发
- XMKFB-1174[XMKFB-977] 后 - 通知通报功能开发
- XMKFB-1246 要情导出文件中显示的签发人名称应作调整
- XMKFB-1244 要情，草稿箱列表的数据为空
- XMKFB-1282 要情，同一单位下新增要情编辑页面会出现编号相同的情况
- XMKFB-1279 要情列表中显示的填报字段数据错误
- XMKFB-1314 编辑要情标题失败
- XMKFB-1352 设置/取消设置为经典案例不需要记录和发送对话
- XMKFB-1342[XMKFB-1307] 要情-警情协查/情报预警
- XMKFB-1361 要情记录中未记录显示导出要情操作
- XMKFB-1332 被退回后再推送要情至上级单位，对应要情的信息和状态应发生变化
- XMKFB-1340 退回后再重新推送要情的要情状态应变为待签收
- XMKFB-1372 要情，自动发送的退回消息未显示退回原因
- XMKFB-1398[XMKFB-1236] 后 - 群体类型跟群体细类等填写信息接口开发
- XMKFB-1399[XMKFB-1236] 后 - 文字内容解析接口
- XMKFB-1416[XMKFB-1236] 后 - 地域列表接口开发
- XMKFB-1393 指令、通知通报列表按照编号检索无效
- XMKFB-1421[XMKFB-1236] 后 - 创建线索接口开发
- XMKFB-1422[XMKFB-1236] 后 - 线索列表接口开发
- XMKFB-1423[XMKFB-1236] 后 - 线索详情接口开发
- XMKFB-1460[XMKFB-1236] 后 - 上报线索，转报线索
- XMKFB-1461[XMKFB-1236] 后 - 下发线索
- XMKFB-1452[XMKFB-1236] 后 - 根据线索ID串查询线索标题跟编号接口
- XMKFB-1453[XMKFB-1236] 后 - 抄送线索，签收线索
- XMKFB-1473[XMKFB-1236] 后 - 人员反馈接口
- XMKFB-1419[XMKFB-1236] 后 - 标签相关接口开发
- XMKFB-1385 指令填写页面中我的收藏列表应按照指令收藏名称进行搜索
- XMKFB-1383 指令编辑页面中我发布的指令列表数据不应包含草稿箱中的数据
- XMKFB-1488[XMKFB-1236] 后 - 线索人员库列表
- XMKFB-1489 指令编号的生成应按照不同地区的单位独立计数排序
- XMKFB-1318 要情，交通事故填写模板中添加车辆的车辆类型选项值应作调整
- XMKFB-1397 指令、通知通报未显示反馈超时标签
- XMKFB-1459 通报填写页面选择要情通报后自动生成的通报文号为空值
- XMKFB-1472 预览、导出通知通报的模板应作调整修改
- XMKFB-1458 通知填写页面自动生成的通知文号显示有误
- XMKFB-1451 原指令的指令记录中未记录显示追加指令操作
- XMKFB-1464 导出要情的标题和正文格式应作调整优化
- XMKFB-1446 要情对话中缺少填充要情字段提示消息
- XMKFB-1554 省厅单位无法下发线索
- XMKFB-1444 指令、通知详情页的对话中缺少反馈时限倒计时提醒消息
- XMKFB-1570 线索人员标签配置，全部分类下无法新建标签
- XMKFB-1582 线索人员标签配置，对标签分类和名称做判重校验
- XMKFB-1600 线索接收上级列表，签收线索报错
- XMKFB-1752 线索-接收下级列表，线索转报后的状态未变化
-

## 前置说明

```text
# 因为地域数据量较大，为了避免影响flyway性能，所以抽成了单独的数据文件
# 第一次部署的时候需要手动导入地域信息到数据库中，可以选择sql导入，或通过csv导入
```

## 新增配置

```properties
# 指令中指令号前缀的跟地域的映射关系，key为地域，value为指令号前缀，需要根据实际情况修改
# 当地域都不匹配时，会取用default对应的内容，以下为示例
# {
#   "510300":"自贡联指",
#   "510302":"自井联指",
#   "510303":"贡井联指",
#   "510304":"大安联指",
#   "510311":"沿滩联指",
#   "510321":"荣县联指",
#   "510322":"富顺联指",
#   "default":"公厅联指"
# }
intelligence.zhiling.orderNo.prefix={"510300":"自贡联指","510302":"自井联指","510303":"贡井联指","510304":"大安联指","510311":"沿滩联指","510321":"荣县联指","510322":"富顺联指","default":"公厅联指"}
# 要情是否可以发送短信
intelligence.yaoqing.canSendSMS=true
# 要情短信模块ID，需要启动后自行调整
intelligence.yaoqing.sms.moduleId=0
# 20240507 - 褚川宝 - 线索的映射数据
# 群体
intelligence.xiansuo.attribute.groupType=[{"children":[{"children":[],"name":"四川信托"},{"children":[],"name":"中植系"},{"children":[],"name":"河南村镇银行"},{"children":[],"name":"翰林金业（黄金）"},{"children":[],"name":"成都“智汇堂”"},{"children":[],"name":"宜宾“中民集团”"},{"children":[],"name":"成都“孺子牛”"},{"children":[],"name":"湖南“盛大金禧”"},{"children":[],"name":"成都“置信金融”"},{"children":[],"name":"遂宁“乾宏”"},{"children":[],"name":"绵阳“恒鼎金融”"},{"children":[],"name":"成都“崇孝养老”"},{"children":[],"name":"“明天系”群体"},{"children":[],"name":"玖富"},{"children":[],"name":"爱钱进"},{"children":[],"name":"E租宝"},{"children":[],"name":"有利网"},{"children":[],"name":"普信"},{"children":[],"name":"凤凰金融"},{"children":[],"name":"中港系"},{"children":[],"name":"京广和"},{"children":[],"name":"泛亚"},{"children":[],"name":"兴麟系"},{"children":[],"name":"善林金融"},{"children":[],"name":"妙资金融"},{"children":[],"name":"钱妈妈"},{"children":[],"name":"民创群体"},{"children":[],"name":"捷越"},{"children":[],"name":"普惠"},{"children":[],"name":"蛋壳洗车"},{"children":[],"name":"水松原"},{"children":[],"name":"原油宝"},{"children":[],"name":"鑫茂"},{"children":[],"name":"未来网"},{"children":[],"name":"鑫龙源"},{"children":[],"name":"小牛金融"},{"children":[],"name":"汉富诺远"},{"children":[],"name":"信和财富"},{"children":[],"name":"众合天下"},{"children":[],"name":"中科汇通"},{"children":[],"name":"蚂蚁金服"},{"children":[],"name":"荷包金融"},{"children":[],"name":"滨海大宗邮币卡"},{"children":[],"name":"钱宝系"},{"children":[],"name":"唐小僧"},{"children":[],"name":"投之家"},{"children":[],"name":"晋商贷"},{"children":[],"name":"中佳易购"},{"children":[],"name":"冠群驰骋"},{"children":[],"name":"达飞云贷"},{"children":[],"name":"康得新"},{"children":[],"name":"轻易贷"},{"children":[],"name":"人人贷"},{"children":[],"name":"和信贷"},{"children":[],"name":"Ppmoney网贷"},{"children":[],"name":"盛京百善"},{"children":[],"name":"翰墨"},{"children":[],"name":"盛世农业"},{"children":[],"name":"科创"},{"children":[],"name":"鑫圆"},{"children":[],"name":"其他"}],"name":"投资受损群体"},{"children":[{"children":[],"name":"山里红"},{"children":[],"name":"善心汇"},{"children":[],"name":"云联惠"},{"children":[],"name":"麒麟矿机"},{"children":[],"name":"江苏HUB虚拟币"},{"children":[],"name":"中天兴盛"},{"children":[],"name":"其他"}],"name":"传销群体"},{"children":[{"children":[],"name":"下岗专业志愿兵"},{"children":[],"name":"两参人员"},{"children":[],"name":"复转军人"},{"children":[],"name":"企业军转干部"},{"children":[],"name":"伤残军人"},{"children":[],"name":"农村义务兵"},{"children":[],"name":"城镇义务兵"},{"children":[],"name":"进藏兵"},{"children":[],"name":"抗美援朝"},{"children":[],"name":"对越自卫反击战退役人员"},{"children":[],"name":"退伍士兵"},{"children":[],"name":"自主择业军转干部"},{"children":[],"name":"军烈属"},{"children":[],"name":"退役人员涉访群体"},{"children":[],"name":"其他"}],"name":"军队退役人员涉访群体"},{"children":[{"children":[],"name":"商铺业主"},{"children":[],"name":"小区业主"},{"children":[],"name":"投资受损"},{"children":[],"name":"商票债权人"},{"children":[],"name":"讨薪"},{"children":[],"name":"内部员工"},{"children":[],"name":"其他"}],"name":"恒大集团"},{"children":[{"children":[],"name":"外省"},{"children":[],"name":"成都市"},{"children":[],"name":"自贡市"},{"children":[],"name":"攀枝花市"},{"children":[],"name":"泸州市"},{"children":[],"name":"德阳市"},{"children":[],"name":"绵阳市"},{"children":[],"name":"广元市"},{"children":[],"name":"遂宁市"},{"children":[],"name":"内江市"},{"children":[],"name":"乐山市"},{"children":[],"name":"南充市"},{"children":[],"name":"眉山市"},{"children":[],"name":"宜宾市"},{"children":[],"name":"广安市"},{"children":[],"name":"达州市"},{"children":[],"name":"雅安市"},{"children":[],"name":"巴中市"},{"children":[],"name":"资阳市"},{"children":[],"name":"阿坝州"},{"children":[],"name":"甘孜州"},{"children":[],"name":"凉山州"}],"name":"小区业主群体"},{"children":[{"children":[],"name":"外省"},{"children":[],"name":"成都市"},{"children":[],"name":"自贡市"},{"children":[],"name":"攀枝花市"},{"children":[],"name":"泸州市"},{"children":[],"name":"德阳市"},{"children":[],"name":"绵阳市"},{"children":[],"name":"广元市"},{"children":[],"name":"遂宁市"},{"children":[],"name":"内江市"},{"children":[],"name":"乐山市"},{"children":[],"name":"南充市"},{"children":[],"name":"眉山市"},{"children":[],"name":"宜宾市"},{"children":[],"name":"广安市"},{"children":[],"name":"达州市"},{"children":[],"name":"雅安市"},{"children":[],"name":"巴中市"},{"children":[],"name":"资阳市"},{"children":[],"name":"阿坝州"},{"children":[],"name":"甘孜州"},{"children":[],"name":"凉山州"}],"name":"商铺业主群体"},{"children":[{"children":[],"name":"讨薪群体"}],"name":"讨薪群体"},{"children":[{"children":[],"name":"涉日维稳"}],"name":"涉日维稳"},{"children":[{"children":[],"name":"“双减”政策"}],"name":"“双减”政策"},{"children":[{"children":[],"name":"涉法涉诉"}],"name":"涉法涉诉"},{"children":[{"children":[],"name":"涉医保改革"}],"name":"涉医保改革"},{"children":[{"children":[],"name":"非访群体"}],"name":"非访群体"},{"children":[{"children":[],"name":"征地拆迁群体"}],"name":"征地拆迁群体"},{"children":[{"children":[],"name":"电信诈骗群体"}],"name":"电信诈骗群体"},{"children":[{"children":[],"name":"失独群体"}],"name":"失独群体"},{"children":[{"children":[],"name":"民师（民代幼）"}],"name":"民师（民代幼）"},{"children":[{"children":[],"name":"长租公寓"},{"children":[],"name":"野生动物养殖户"},{"children":[],"name":"患者家属"},{"children":[],"name":"其他"}],"name":"新冠疫情群体"},{"children":[{"children":[],"name":"大货车司机"},{"children":[],"name":"出租车司机"},{"children":[],"name":"网约车司机"},{"children":[],"name":"其他"}],"name":"涉车群体"},{"children":[{"children":[],"name":"民代幼教师"},{"children":[],"name":"未分配大中专毕业生"},{"children":[],"name":"乡村医生群体"},{"children":[],"name":"协解职工"},{"children":[],"name":"疫苗受害者家属"},{"children":[],"name":"其他"}],"name":"历史遗留问题"},{"children":[{"children":[],"name":"优胜教育"},{"children":[],"name":"学霸君"},{"children":[],"name":"其他"}],"name":"教育培训群体"},{"children":[{"children":[],"name":"大病致贫"},{"children":[],"name":"阴滋病群体"},{"children":[],"name":"渐冻症"},{"children":[],"name":"尘肺病"},{"children":[],"name":"其他"}],"name":"医疗群体"},{"children":[{"children":[],"name":"高考学生家长"}],"name":"学生家长"},{"children":[{"children":[],"name":"“双语”群体"}],"name":"“双语”群体"},{"children":[{"children":[],"name":"“学习”群体"}],"name":"“学习”群体"},{"children":[{"children":[],"name":"脑控群体"}],"name":"脑控群体"},{"children":[{"children":[],"name":"物流群体"}],"name":"物流群体"},{"children":[{"children":[],"name":"网约车司机"},{"children":[],"name":"快递员"},{"children":[],"name":"外卖人员"},{"children":[],"name":"其他"}],"name":"新业态"},{"children":[{"children":[],"name":"自然灾害"}],"name":"自然灾害"},{"children":[{"children":[],"name":"老放映员"}],"name":"老放映员"},{"children":[{"children":[],"name":"退休“中人”"}],"name":"退休“中人”"},{"children":[{"children":[],"name":"水电移民"}],"name":"水电移民"},{"children":[{"children":[],"name":"涉黑犯罪前科"},{"children":[],"name":"严重暴力犯罪前科"},{"children":[],"name":"涉爆炸物犯罪前科"},{"children":[],"name":"涉枪支弹药案犯罪前科"},{"children":[],"name":"其他前科"}],"name":"前科人员群体"},{"children":[{"children":[],"name":"颠覆破坏"},{"children":[],"name":"高校维稳"},{"children":[],"name":"网上不当言论"},{"children":[],"name":"涉美"},{"children":[],"name":"涉港"},{"children":[],"name":"其他涉政重点人"}],"name":"涉政重点人员群体"},{"children":[{"children":[],"name":"涉藏群体"}],"name":"涉藏群体"},{"children":[{"children":[],"name":"邪教群体"}],"name":"邪教群体"},{"children":[{"children":[],"name":"涉恐群体"}],"name":"涉恐群体"},{"children":[{"children":[],"name":"轻微滋事精神病人"},{"children":[],"name":"有潜在暴力倾向精神病人"},{"children":[],"name":"肇事肇祸精神病人"},{"children":[],"name":"其他精神病人"}],"name":"精神障碍患者"},{"children":[{"children":[],"name":"其他"}],"name":"其他"}]
# 维权方式
intelligence.xiansuo.attribute.wqfs=[{"name":"涉访（限5人以上）","children":[]},{"name":"暴力极端方式维权（不限制人数）","children":[]}]
# 行为方式
intelligence.xiansuo.attribute.xwfs=[{"name":"网上煽动","children":[]},{"name":"电话串联","children":[]},{"name":"线下串联","children":[]}]
# 指向地点
intelligence.xiansuo.attribute.zxdd=[{"name":"赴省","children":[{"name":"省政府","children":[]},{"name":"公安厅","children":[]},{"name":"省信访局","children":[]},{"name":"省人大","children":[]},{"name":"省纪委","children":[]},{"name":"住建厅","children":[]},{"name":"教育厅","children":[]},{"name":"会场及住地","children":[]},{"name":"人社厅","children":[]},{"name":"川信大厦","children":[]},{"name":"使领馆","children":[]},{"name":"天府广场","children":[]},{"name":"春熙路","children":[]},{"name":"其他省级重点区域","children":[]}]},{"name":"进京","children":[{"name":"国家信访局","children":[]},{"name":"天安门","children":[]},{"name":"中南海","children":[]},{"name":"中纪委","children":[]},{"name":"公安部","children":[]},{"name":"退役军人事务部","children":[]},{"name":"中央军委信访处","children":[]},{"name":"北京市政府","children":[]},{"name":"其它明确地点","children":[]},{"name":"地点不明确","children":[]}]},{"name":"本地集访","children":[{"name":"无数据","children":[]}]},{"name":"外省集访","children":[{"name":"无数据","children":[]}]}]
# 2024-05-07 褚川宝 默认省地域代码
intelligence.common.defaultAreaCode=510000000000
# 2024-05-09 褚川宝 线索是否强制要求逐级下发
intelligence.xiansuo.need.gradually.push=true
# 2024-05-09 褚川宝 线索历史重复时间范围（天）
# 30天内不允许
intelligence.xiansuo.person.history.min.day=-30
# 90天内历史重复
intelligence.xiansuo.person.history.max.day=-90
# 2024-05-11 褚川宝 指派时是否校验已经指派到了当前单位，默认不校验
intelligence.xiansuo.need.check.push.same.dept=false
# 工作状态
intelligence.xiansuo.attribute.gzzt=[{"name":"工作中","children":[]},{"name":"已稳控","children":[]},{"name":"非本市","children":[{"name":"成都","children":[]},{"name":"自贡","children":[]},{"name":"攀枝花","children":[]},{"name":"泸州","children":[]},{"name":"德阳","children":[]},{"name":"绵阳","children":[]},{"name":"广元","children":[]},{"name":"遂宁","children":[]},{"name":"内江","children":[]},{"name":"乐山","children":[]},{"name":"南充","children":[]},{"name":"眉山","children":[]},{"name":"宜宾","children":[]},{"name":"广安","children":[]},{"name":"达州","children":[]},{"name":"雅安","children":[]},{"name":"巴中","children":[]},{"name":"资阳","children":[]},{"name":"阿坝","children":[]},{"name":"甘孜","children":[]},{"name":"凉山","children":[]}]},{"name":"外省人员","children":[]},{"name":"查无此人","children":[]},{"name":"无法核查","children":[]}]
# 目前所在
intelligence.xiansuo.attribute.mqsz=[{"name":"在本地","children":[]},{"name":"不在本地","children":[{"name":"成都","children":[]},{"name":"自贡","children":[]},{"name":"攀枝花","children":[]},{"name":"泸州","children":[]},{"name":"德阳","children":[]},{"name":"绵阳","children":[]},{"name":"广元","children":[]},{"name":"遂宁","children":[]},{"name":"内江","children":[]},{"name":"乐山","children":[]},{"name":"南充","children":[]},{"name":"眉山","children":[]},{"name":"宜宾","children":[]},{"name":"广安","children":[]},{"name":"达州","children":[]},{"name":"雅安","children":[]},{"name":"巴中","children":[]},{"name":"资阳","children":[]},{"name":"阿坝","children":[]},{"name":"甘孜","children":[]},{"name":"凉山","children":[]}]},{"name":"未知","children":[]}]
# 2024-05-13 褚川宝 定时标记超时
intelligence.task.markTimeOut.enable=true
# 10分钟进行一次标记
intelligence.task.markTimeOut.cron=0 */10 * * * ?
# 2024-05-14 褚川宝 要情定时检查字段填写情况
intelligence.task.markFieldsFinished.enable=true
# 每小时进行一次标记
intelligence.task.markFieldsFinished.cron=0 10 */1 * * ?
# 超过多长时间进行提醒
intelligence.task.markFieldsFinished.hour=-12
# 定时任务中用到登录用户(目前消息提醒也用到这个账户）
intelligence.schedule.username=admin
```

# 5.3

## 更新日志

- XMKFB-1585 下发线索时将涉及人员的核查单位按照所属地域自动匹配
- XMKFB-1607 被取消指派后单位仍能查看被取消指派的线索
- XMKFB-1592 推送下发线索完成后线索详情页存在的问题
- XMKFB-1597 线索接收下级列表中显示的总人数有误
- XMKFB-1593 区县推送下发线索后列表中显示的已指派人数有误
- XMKFB-1666 新增线索包含重复人员时保存线索接口报错
- XMKFB-1683 对人员相关线索和人员库中的数据排序做调整
- XMKFB-1544 在后台编辑、删除线索人员标签后，前台线索中的数据应同步更新
- XMKFB-1388 指令单预览和导出模板有误
- XMKFB-1385 指令填写页面中我的收藏列表应按照指令收藏名称进行搜索（通知通报填写页面）
- XMKFB-1688 人员库和相关线索中应去除市局下发、区县下发、派出所上报的数据
- XMKFB-1685 线索-人员库列表人员数据的调整与优化
- XMKFB-1710 后 - 工作台增加线索跟指令的待办模块跟列表
- XMKFB-1596 线索接收上级列表中显示的已指派人数和总人数有误
- XMKFB-1731 关于线索详情页显示或编辑显示的人员标签存在的问题
-

# 5.4

## 更新日志

- XMKFB-1624 线索-人员库检索项的优化与调整
- XMKFB-1727 线索草稿箱列表中显示的下发线索已指派人数有误
- XMKFB-1738 合并线索详情页显示信息存在的问题
- XMKFB-1768[XMKFB-1610] 后 - 优化线索状态码的查询
- XMKFB-1767 线索详情页显示的抄送单位为空
- XMKFB-1748[XMKFB-1439] 指令修改后原来已签收或已反馈的单位不需要再签收、反馈，不会重复显示在待签收、待反馈里。
- XMKFB-1773 自贡GA-接收到上级推送指令的列表中，指令状态完善
- APP-3 创建指令推送给下级，下级单位用户进入APP，待办任务指令数显示为0
- APP-65 转发指令到单位A后，再转发指令到其他单位，提示该部门已转发过了，不能重复转发
- APP-71 指令列表显示的已作废的指令状态不对
- XMKFB-1832 盯办线索后核查单位在接收上级列表中会重复显示多条数据
- XMKFB-1833[XMKFB-1799] 增加归总部门的返回
- APP-54 工作台待办指令详情中没有签收、反馈入口
- APP-100 工作台-我的待办，待签收、待反馈指令详情页显示的是未结案状态
- APP-111 工作台待办指令详情页显示的状态标签都是”待反馈“
- XMKFB-1866 合 - 指令增加追加指令跟转发指令的标识
- XMKFB-1924[XMKFB-1430] 后-转发指令、通知通报相关问题处理
- XMKFB-1990[XMKFB-1559] 增加检索项
- XMKFB-2022[XMKFB-1559] 后-线索配置列表排序调整
-

# 6.1

## 更新日志

- XMKFB-2016[XMKFB-1799] 指令编号生成错误

# 6.2

## 更新日志

- XMKFB-2042[XMKFB-1725] 后 - 要情，线索提供对应的大屏列表
- XMKFB-2053[XMKFB-2036] 线索列表检索，检索类型添加“线索内容”
- XMKFB-2040 追加指令显示的发布时间为空
- XMKFB-2077 线索详情人员接口添加档案ID的返回
- XMKFB-2079[XMKFB-1848] 后 - 新增任务功能开发
- XMKFB-2080[XMKFB-1848] 后 - 任务列表跟任务详情接口开发
- XMKFB-2089[XMKFB-1848] 后 - 增加群体档案跟人员档案相关查询接口
- XMKFB-2095[XMKFB-1848] 后 - 完善任务动作逻辑
- # 20240614发版
- 修复按照部门汇总查询日志异常
- # 20240618 RC发版

## 新增配置

```properties

# 指向地点
intelligence.renwu.attribute.zxdd=[{"name":"赴省","children":[{"name":"省政府","children":[]},{"name":"公安厅","children":[]},{"name":"省信访局","children":[]},{"name":"省人大","children":[]},{"name":"省纪委","children":[]},{"name":"住建厅","children":[]},{"name":"教育厅","children":[]},{"name":"会场及住地","children":[]},{"name":"人社厅","children":[]},{"name":"川信大厦","children":[]},{"name":"使领馆","children":[]},{"name":"天府广场","children":[]},{"name":"春熙路","children":[]},{"name":"其他省级重点区域","children":[]}]},{"name":"进京","children":[{"name":"国家信访局","children":[]},{"name":"天安门","children":[]},{"name":"中南海","children":[]},{"name":"中纪委","children":[]},{"name":"公安部","children":[]},{"name":"退役军人事务部","children":[]},{"name":"中央军委信访处","children":[]},{"name":"北京市政府","children":[]},{"name":"其它明确地点","children":[]},{"name":"地点不明确","children":[]}]},{"name":"本地集访","children":[{"name":"无数据","children":[]}]},{"name":"外省集访","children":[{"name":"无数据","children":[]}]}]
# 工作状态
intelligence.renwu.attribute.gzzt=[{"name":"工作中","children":[]},{"name":"见面稳控","children":[]},{"name":"敲打教育","children":[]},{"name":"技术管控","children":[]},{"name":"非本市","children":[{"name":"成都","children":[]},{"name":"自贡","children":[]},{"name":"攀枝花","children":[]},{"name":"泸州","children":[]},{"name":"德阳","children":[]},{"name":"绵阳","children":[]},{"name":"广元","children":[]},{"name":"遂宁","children":[]},{"name":"内江","children":[]},{"name":"乐山","children":[]},{"name":"南充","children":[]},{"name":"眉山","children":[]},{"name":"宜宾","children":[]},{"name":"广安","children":[]},{"name":"达州","children":[]},{"name":"雅安","children":[]},{"name":"巴中","children":[]},{"name":"资阳","children":[]},{"name":"阿坝","children":[]},{"name":"甘孜","children":[]},{"name":"凉山","children":[]}]},{"name":"外省人员","children":[]},{"name":"查无此人","children":[]},{"name":"无法核查","children":[]}]
# 目前所在
intelligence.renwu.attribute.mqsz=[{"name":"在本地","children":[]},{"name":"不在本地","children":[{"name":"成都","children":[]},{"name":"自贡","children":[]},{"name":"攀枝花","children":[]},{"name":"泸州","children":[]},{"name":"德阳","children":[]},{"name":"绵阳","children":[]},{"name":"广元","children":[]},{"name":"遂宁","children":[]},{"name":"内江","children":[]},{"name":"乐山","children":[]},{"name":"南充","children":[]},{"name":"眉山","children":[]},{"name":"宜宾","children":[]},{"name":"广安","children":[]},{"name":"达州","children":[]},{"name":"雅安","children":[]},{"name":"巴中","children":[]},{"name":"资阳","children":[]},{"name":"阿坝","children":[]},{"name":"甘孜","children":[]},{"name":"凉山","children":[]}]},{"name":"未知","children":[]}]
# 20240614发版
```

# 6.3

## 更新日志

- XMKFB-2137[XMKFB-1961] 线索相关接口添加“来源类别”字段支持
- XMKFB-2043[XMKFB-1799] 修改通知通报后未显示修改提示信息
- XMKFB-2041[XMKFB-1799] 查看原始内容预览信息有误
- XMKFB-2121[XMKFB-1961] 后 - 要情通报功能开发
- XMKFB-2208 后 - 任务细览增加群体信息的返回
- XMKFB-2236[XMKFB-1848] 接收任务详情页中的稳控清单人员应仅显示被指派到本单位的人员数据
- XMKFB-2232 德阳GA-任务功能列表支持展示子任务标签，以及任务编号。
- fix(人员库): 修复人员库相关线索点击报错
- # 20240621发版

# 6.4

## 更新日志

- XMKFB-2204[XMKFB-2198] 后 - 补充相关字段
- XMKFB-2272[XMKFB-1848] 【后】工作记录接口需要返回电话号码
- XMKFB-2282[XMKFB-2179] 后 - 视侦跳转地址获取接口开发
- XMKFB-2273[XMKFB-2198] 后 - 完善导出行为
- XMKFB-2275[XMKFB-1848] 【后】提供根据工作状态获取不同填报模板的接口
- XMKFB-2250[XMKFB-1848] 新建任务时相关人员的工作要求字段添加
- # 20240627发版
- XMKFB-2217[XMKFB-1848] 全部指派逻辑错误
-

## 新增配置项

```properties
# 20240624 褚川宝 任务视侦的用户名
intelligence.renwu.shizhen.username=tuoersi01
# 20240624 褚川宝 任务视侦的密码
intelligence.renwu.shizhen.password=a123456
# 20240624 褚川宝 任务视侦的type（调用方名称）
intelligence.renwu.shizhen.type=拓尔思
# 20240624 褚川宝 任务视侦的tgt跟tickets获取URL
intelligence.renwu.shizhen.tickets.url=http://***********:11125/sso/v1/tickets
# 20240624 褚川宝 任务视侦的跳转地址(集成登录)
intelligence.renwu.shizhen.validate.url=http://***********:11127/cas/validate
# 20240624 褚川宝 任务视侦的跳转地址(目标地址)
intelligence.renwu.shizhen.last.url=http://***********:11127/sceneViewJudge/mapSearch
# 20240624 褚川宝 OSS的host地址
intelligence.common.oss.host=
# 20240625 褚川宝 导出文件在容器中存放的默认路径
intelligence.common.exportFilePath=/tmp
# 20240626 褚川宝 工作状态内容模板 XMKFB-2275
intelligence.renwu.attribute.gzttnrmb=[{"name":"工作中","children":[]},{"name":"见面稳控","children":[{"name":"___年___月___日___时___分，______派出所民警与______（身份证:__________）进行_____工作，向其宣传相关法律法规，其本人称不会去上访，会理性维权。","children":[]}]},{"name":"敲打教育","children":[{"name":"___年___月___日___时___分，______派出所民警与______（身份证:__________）进行_____工作，向其宣传相关法律法规，其本人称不会去上访，会理性维权。","children":[]}]},{"name":"技术管控","children":[{"name":"___年___月___日___时___分，______派出所民警与______（身份证:__________）进行_____工作，向其宣传相关法律法规，其本人称不会去上访，会理性维权。","children":[]}]},{"name":"非本市","children":[]},{"name":"外省人员","children":[]},{"name":"查无此人","children":[]},{"name":"无法核查","children":[]}]
# 20240627发版
```

# 7.1

- XMKFB-2429 反馈的工作记录中应支持显示上传的附件信息
- XMKFB-2419[XMKFB-1961] 后-情指行消息改造
- XMKFB-2446[XMKFB-1848] 后-添加反馈时限字段用于在拆分任务时回显
- XMKFB-2469 自贡GA-指令导出模板不标准
- # 20240705发版
- XMKFB-2504 后 - 进行过转发指令的操作之后再新建指令就再也无法收到指令细览消息了
- XMKFB-2577[XMKFB-2557] 后 - 优化新建时指令编号的获取
- # 20240712发版

# 7.2

- XMKFB-2620[XMKFB-2579] 后 - 单位补充归属地域名称的返回
- XMKFB-2613[XMKFB-2579] 后 - 优化操作权限验证
- # 20240716 RC发版
-

## 新增配置项

```properties
# 2024-07-16 - 褚川宝 - 情指行对象创建权限判断类型
# user: 按照创建人；dept：按照创建单位
intelligence.entity.create.right.owner.type=dept
# 20240716 RC发版
```

# 7.3

- XMKFB-2618[XMKFB-1961] 后-要请只允许转一次指令
- # 20240719发版

# 8.2

- XMKFB-2655[XMKFB-2579] 后 - 指令增加追加单位的行为
- XMKFB-2614[XMKFB-2556] 后 - 要情导出文件核对
- XMKFB-2686[XMKFB-2579] 追加单位存在的问题
- XMKFB-3063[XMKFB-2579] 后 - 优化消息列表查询
-

# 8.3

- XMKFB-3090[XMKFB-3080] 后 - 优化通知文号的生产
- XMKFB-3089[XMKFB-3080] 后 -优化导出内容
- XMKFB-3168[XMKFB-2919] 后 - 新增/编辑功能开发
- XMKFB-3075[XBKFB-2719] 后 - 导出内容优化
- XMKFB-3169[XMKFB-2919] 后 - 细览跟列表接口开发
- XMKFB-3190[XMKFB-2919] 后 - 修改/续报内容接口开发
- XMKFB-3193[XMKFB-2919] 后 - 舆情内容版本列表开发
- XMKFB-3200[XMKFB-2919] 后 - 抄送，完结舆情接口开发
- XMKFB-3201[XMKFB-2919] 后 - 舆情反馈工作记录开发
- XMKFB-3202[XMKFB-2919] 后 - 研判舆情开发
-

## 新增配置项

```properties
# 20240812 - 褚川宝 - 通知通报中通知的文号模板
# 占位符为：{{year}}跟{{dataNo}}，对应年份跟期号
intelligence.tongzhitongbao.notice.orderNo.format=川公情指[{{year}}]{{dataNo}}号
# 20240813 - 蒋俊杰 - 指令/通知通报导出地域信息配置(配置为当地的地名)
intelligence.export.excel.areaName=自贡市
# 20240814 - 褚川宝 - 舆情相关属性
intelligence.yuqing.attribute.yqly=[{"name":"巡查发现","children":[]},{"name":"网络举报","children":[]},{"name":"其他单位移交","children":[]}]
intelligence.yuqing.attribute.yqdj=[{"name":"一般","children":[]},{"name":"较大","children":[]},{"name":"重大","children":[]},{"name":"特别重大","children":[]}]
intelligence.yuqing.attribute.gzyq=[{"name":"转要情","children":[]},{"name":"转指令","children":[]},{"name":"转线索","children":[]},{"name":"核查处置","children":[]}]
```

# 8.4

- XMKFB-3208[XMKFB-2919] 后 - 标记已读跟获取已读情况的接口
- XMKFB-3365[XMKFB-2919] 后 - 完善舆情导出功能

# 8.5

- XMKFB-1738 合并线索详情页显示信息存在的问题
- XMKFB-3794 合-【广安】-指令导出预览优化

# 9.1

- XMKFB-3631[XMKFB-2556] 要情、指令导出优化
- XMKFB-3617[XMKFB-2556] 要情-信息快报-结构化导出信息模板
- XMKFB-3663[XMKFB-3569] 后 - 指令反馈增加相关反馈记录的存储跟导出
- XMKFB-3665[XMKFB-2737] 后 - 增加相关导出行为
- XMKFB-2737 指令新增事件处置流程按钮
- XMKFB-3827[XMKFB-3826] 后-【广安】-草稿箱不能占用指令号
- XMKFB-3791 前 - 线索AI提取优化
-

# 9.2

- XMKFB-3810[XMKFB-2919] 导出舆情内容里显示的时间有误
- XMKFB-3809[XMKFB-2919] 舆情导出文件内容格式存在的问题
- XMKFB-3858[XMKFB-2919] 舆情列表的工作要求筛选项补充新增“未研判”相关接口支持
- XMKFB-3862[XMKFB-3823] 后 - 指令增加置顶功能
- XMKFB-3863[XMKFB-3823] 后 - 创建指令时支持不生成指令号
- XMKFB-3895 【自贡】指令导出的事件处置流程内容存在的问题
- XMKFB-3796 后-【广安】-指令/要请/线索 全部列表检索逻辑优化
- XMKFB-3823 合-【广安】-指令系统优化
- XMKFB-3874 【自贡】追加指令的消息显示判断逻辑应作调整
- XMKFB-3888 自贡GA-指令列表页面搜索逻辑新增按内容搜索（其它项目同步改动）
- XMKFB-3978 【自贡】转发通知通报后的内容数据有误
-

## 部署前置

```properties
# 修改配置项（intelligence.properties）
intelligence.yuqing.attribute.gzyq=[{"name":"未研判","children":[]},{"name":"转要情","children":[]},{"name":"转指令","children":[]},{"name":"转线索","children":[]},{"name":"核查处置","children":[]}]
```

# 9.3

- XMKFB-3988 指令-事件处置流程功能修改
- XMKFB-4067[XMKFB-3953] 后端返回是发送方还是接收方标识位
- XMKFB-3836 自贡GA-指令下发时可以指定到具体人
-

# 9.4

- XMKFB-4086[XMKFB-3922] 后 - 日志内容中增加相关信息
- XMKFB-3922 德阳GA-任务系统，研判内容记录没有展示
- XMKFB-4088 后-【自贡】-风险人员-标签打标相关服务提供
- XMKFB-4190[XMKFB-3836] 指令下发给人员后，对应人员无法签收、反馈该指令
- XMKFB-3901 【自贡】指令作废、结案后显示的单位接收状态有误
- XMKFB-4208[XMKFB-3836] 催办指令接收人员失败
- XMKFB-4178[XMKFB-3507] 后 - 完善第三方接口相关业务逻辑
- XMKFB-4180[XMKFB-3507] 后 - 完善线索数据消费行为
- XMKFB-4221 【广安】未选择生成指令编号的草稿数据发布后会强制带有指令编号
- XMKFB-4230 情指行模操作消息增加crDept的返回
- XMKFB-4203 后-【广安】-指令反馈/回复相应慢问题排查
-

## 新增配置

```properties
# 20240926 - 肖豪 - 省厅相关数据获取配置项，仅自贡、德阳环境增加
intelligence.province.clue.enable=false
intelligence.province.clue.cron=
intelligence.province.clue.userId=
intelligence.province.clue.deptId=
intelligence.province.clue.idCard=
intelligence.province.clue.authUrl=
intelligence.province.clue.reportUrl=
intelligence.province.clue.pageUrl=
intelligence.province.clue.personPageUrl=
intelligence.province.clue.dateBeforeDay=-7
# 20240925省厅线索数据同步到我们目标模块（xiansuo 或 renwu），德阳，广安，自贡按照实际情况填写，其他项目目前应该为空
intelligence.st.data.pull.to=
```

# 10.2

- XMKFB-4183[XMKFB-4071] 后 - 完善任务签收,反馈逻辑
- XMKFB-4182[XMKFB-3507] 后 - 完善线索签收，反馈逻辑
- XMKFB-4306[XMKFB-3869] 后-调整追加指令和转发指令接口返回结果
- feat(线索下发): 优化上下级判断
- XMKFB-4412 指令bug处理
- XMKFB-4419[XMKFB-4415] 后 - 下发指令时接收方无法收到提醒消息
- XMKFB-4415 合 - 情指行模块消息提醒优化
- XMKFB-4411 【云哨】-线索/要情/指令/通知通报优化
-

# 10.3

- XMKFB-4594[XMKFB-4286] 后-要情详情所属辖区返回优化

# 10.4

- XMKFB-4746[XMKFB-3898] 接口增加返回已转指令相关字段信息
-

## 新增配置

```properties
# 20241025 - 张阳 - 省厅任务关联人员补录开关（默认关闭）
intelligence.st.renwu.related.person.add=false
# 20241025 - 张阳 - 省厅线索消费过滤指定地域（DY：过滤德阳本地上报线索）
intelligence.st.renwu.filter.codes=5106
```

# 10.5

- XMKFB-4852[XMKFB-4675] 后 - 后端调整适配
- XMKFB-4828[XMKFB-4826] 后 - 优化任务接入逻辑
- XMKFB-4811 指令，转发指令内容筛选结果为空
- XMKFB-4836[XMKFB-4826] 后 - 优化线索消费入库
- XMKFB-4812[XMKFB-4755] 后-【德阳】-大数据核查功能开发
- XMKFB-4919[XMKFB-4914] 后-线索是否反馈返回相关标识字段
- XMKFB-4921 后 - 优化指令消息列表加载
-

## 新增配置

```properties
# 20241029 - 张阳 - 省厅线索消费市局情指人员默认ID
intelligence.default.city.user.id=2214
# 20241030 - 肖豪 - 指定省厅用户信息 - 根据不通项目自行配置
intelligence.province.clue.provinceUserId=
intelligence.province.clue.provinceDeptId=
# 20241030 - 褚川宝 - 任务大数据核查的有效期（分钟），为0时不走缓存
intelligence.renwu.dsjhc.expired=120
intelligence.renwu.dsjhc.policeNo=080483
intelligence.renwu.dsjhc.userId=51000013503
intelligence.renwu.dsjhc.appId=A-3325378248572928
intelligence.renwu.dsjhc.subId=A-3325378248572928
intelligence.renwu.dsjhc.senderId=5100002800
intelligence.renwu.dsjhc.groupId=thunder
intelligence.renwu.dsjhc.areaCode=510000000000
intelligence.renwu.dsjhc.host=http://api.dataservice.sc:9090
intelligence.renwu.dsjhc.token.url=${intelligence.renwu.dsjhc.host}/restcloud/api/v1/token/create
intelligence.renwu.dsjhc.szqtryxxcx.url=${intelligence.renwu.dsjhc.host}/restcloud/fhopenapiplatform/S-510000000000-0100-00063
intelligence.renwu.dsjhc.szqtryxxcx.serviceId=9790545752752128
intelligence.renwu.dsjhc.scsryztkxxcx.url=${intelligence.renwu.dsjhc.host}/restcloud/fhopenapiplatform/S-510000000000-0100-00053
intelligence.renwu.dsjhc.scsryztkxxcx.serviceId=9790458041466880
intelligence.renwu.dsjhc.scsryjcxxcx.url=${intelligence.renwu.dsjhc.host}/restcloud/fhopenapiplatform/S-510000000000-0100-00026
intelligence.renwu.dsjhc.scsryjcxxcx.serviceId=6628124917760000
intelligence.renwu.dsjhc.scsxdryjbxxcx.url=${intelligence.renwu.dsjhc.host}/restcloud/fhopenapiplatform/S-510000000000-0100-00066
intelligence.renwu.dsjhc.scsxdryjbxxcx.serviceId=9790546549669888
intelligence.renwu.dsjhc.scsjtwfjlcx.url=${intelligence.renwu.dsjhc.host}/restcloud/fhopenapiplatform/S-510000000000-0100-00045
intelligence.renwu.dsjhc.scsjtwfjlcx.serviceId=9790273664057344
intelligence.renwu.dsjhc.scsgmsfyhjgxcx.url=${intelligence.renwu.dsjhc.host}/restcloud/fhopenapiplatform/S-510000000000-0100-00052
intelligence.renwu.dsjhc.scsgmsfyhjgxcx.serviceId=9790457701728256
intelligence.renwu.dsjhc.scsgmsfyajgxcx.url=${intelligence.renwu.dsjhc.host}/restcloud/fhopenapiplatform/S-510000000000-0100-00051
intelligence.renwu.dsjhc.scsgmsfyajgxcx.serviceId=9790371823353856
intelligence.renwu.dsjhc.scsajjbxxcx.url=${intelligence.renwu.dsjhc.host}/restcloud/fhopenapiplatform/S-510000000000-0100-00044
intelligence.renwu.dsjhc.scsajjbxxcx.serviceId=9790273391427584
intelligence.renwu.dsjhc.scsjdcjcxxcx.url=${intelligence.renwu.dsjhc.host}/restcloud/fhopenapiplatform/S-510000000000-0100-00039
intelligence.renwu.dsjhc.scsjdcjcxxcx.serviceId=9790245784518656
```

# 11.1

- XMKFB-4959[XMKFB-4950] 回复消息展示附件提示信息
- XMKFB-4901[XMKFB-4825] 后 - 按照原型优化不同操作日志
- XMKFB-4992[XMKFB-4865] 后 - 优化是否发起协同/线索的过滤
- XMKFB-4975[XMKFB-4886] 后 - 增加获取技侦数据的行为

# 11.2

- XMKFB-5117[XMKFB-4926] 后 - 完善相关接口逻辑
- XMKFB-5131 合 - 任务模块细览中关联人员过滤优化
-

## 新增配置

```properties
# 20241113 - 褚川宝 - 任务模块中可以看看子部门数据的警种
intelligence.renwu.canSee.children.policeKinds=1,4,7
```

# 11.3

- XMKFB-5227[XMKFB-4949] 情指模块增加查看回复消息内容接口
- XMKFB-5221 【德阳】任务，从省厅同步的线索未显示出线索编号数据
- XMKFB-5222 【德阳】任务，省厅同步线索在系统内编入的任务编号重复
- XMKFB-5320 后 - 德阳数据对比
- XMKFB-5455[XMKFB-5456] 德阳-任务模块不能签收
-

# 11.4

- XMKFB-5426[XMKFB-5179] 后-调整适配指令内容富文本格式

# 12.2

- XMKFB-5626 合 - 自贡ST线索同步调整
- XMKFB-5650[XMKFB-5648] 后 - 增加根据人力情报反查线索列表的行为
- XMKFB-5687[XMKFB-4442] 后 - 完善上报ST的逻辑

# 12.3

- XMKFB-5768[XMKFB-5620] 后-【广安】- 情指行记录发起盯办日志
- XMKFB-5798 后-【自贡】- 通知通报模块BUG处理

# 12.4

- XMKFB-5691 优化身份证信息提取
- XMKFB-5891 【德阳】任务，人员户籍地数据应显示为简称
- XMKFB-5131[XMKFB-5191] 合 - 任务模块细览中关联人员过滤优化
- XMKFB-6152 【德阳】任务，任务反馈提交失败

# 13.1

- XMKFB-6154 【德阳】任务，市局、分局情指、治安、网安警种单位无法反馈任务

## 新增配置

```properties

```

# 13.2

- XMKFB-6209[XMKFB-6176] 后 - 增加相关列表查询
- XMKFB-6176 德阳GA-任务功能列表新增“我处理的”tab
-

# 13.3

- XMKFB-6369 要情、指令、线索、通知通报-记录导出模板调整

# 14.3

- XMKFB-6905 后 - 查看自贡上报ST接口是否激活了
- XMKFB-6922 后-【广安】- 线索抽取内容关键信息优化

## 新增配置项

```properties
# 20250220 - 蒋俊杰 - 上报ST是否激活(自贡环境配置,已添加生产环境)
intelligence.province.clue.canReport=true
```

# 14.4

- XMKFB-6960[XMKFB-6924] 后-【广安】- 线索模块相关更改
- XMKFB-7164 后-【广安】- 核查单位匹配逻辑优化
- XMKFB-7178 【自贡】线索功能AI提取效果优化
-

# 15.1

- XMKFB-7240 【自贡】线索增加涉及人数、涉及对象

# 15.2

- XMKFB-7267 后 - 优化AI提取人员信息行为
- XMKFB-7352[XMKFB-7303] 参照父任务，完成消息回到顶部的改造

# 15.3

- XMKFB-7364 【自贡】线索模块优化

# 16.1

- XMKFB-7619[XMKFB-7549] 后 - 增加流转功能
- XMKFB-7661[XMKFB-7549] 后 - 完善线索列表过滤跟详情页返回数据
- XMKFB-7604[XMKFB-7549] 后 - 线索增加导出行为

# 16.3

- XMKFB-7798[XMKFB-7698] 后-【德阳】- 完成文档中任务相关的调整
- XMKFB-7841 【自贡】【线索】线索bug处理
- XMKFB-7949 广安-要情-新建 重大案事件-涉稳上访聚集调整
-

# 更新历史数据  执行一次即可（发版后执行）

```sql
update tb_intelligence_renwu_related_person_mapping  m 
left join tb_intelligence_renwu_related_person p on p.data_id = m.person_id
set m.zjhm = p.zjhm
WHERE m.is_del = 0 and (m.zjhm is null or m.zjhm = '');
```

# 16.4

- XMKFB-7984 后 - 自贡线索回流优化
- XMKFB-8005 【自贡】【线索】线索bug处理0421
- XMKFB-7834 【德阳】与省厅线索状态同步的优化
- XMKFB-8053 【自贡】【线索】线索列表优化
- XMKFB-7818 【自贡】线索管理，线索单缺少导出和打印功能
- XMKFB-8113 【自贡】【线索】线索还存在的问题
- XMKFB-8051 【自贡】【线索】【验收】线索归档
- XMKFB-8124 【自贡】【线索】接收上级、推送上级核查时限
- XMKFB-8129 【自贡】【线索】线索列表数据更新
- XMKFB-8170 【德阳】任务列表为空，请排查原因
-

# 17.1

- XMKFB-8285 【德阳】任务，线索人员的户籍地代码和简称数据为空

# 17.2

- XMKFB-8290[XMKFB-8277] 后 - 优化ST数据回流
- XMKFB-8289[XMKFB-8277] 后 -任务支持手动关联群体的行为
- XMKFB-8406[XMKFB-8398] 后 - 线索人员列表增加“仅看本地区”过滤支持
- XMKFB-8455[XMKFB-8398] 后 - 增加指派状态返回
- 

## 新增配置

```properties
# 20250512 - 张阳 - 省厅线索回流是否自动补缺群体 默认为否
intelligence.province.clue.auto.add.group=false
```

# 17.3

- XMKFB-8457[XMKFB-8434] 后 - 完善群体信息
- XMKFB-8471 【德阳】任务，省厅下发线索里首次涉及群体无需匹配
- XMKFB-8526[XMKFB-8520] 后 - 优化德阳数据回流

## 新增配置项

```properties
# 群体(在nacos中替换)
intelligence.xiansuo.attribute.groupType=[{"children":[{"children":[],"name":"四川信托"},{"children":[],"name":"中植系"},{"children":[],"name":"河南村镇银行"},{"children":[],"name":"翰林金业（黄金）"},{"children":[],"name":"成都“智汇堂”"},{"children":[],"name":"宜宾“中民集团”"},{"children":[],"name":"成都“孺子牛”"},{"children":[],"name":"湖南“盛大金禧”"},{"children":[],"name":"成都“置信金融”"},{"children":[],"name":"遂宁“乾宏”"},{"children":[],"name":"绵阳“恒鼎金融”"},{"children":[],"name":"成都“崇孝养老”"},{"children":[],"name":"“明天系”群体"},{"children":[],"name":"玖富"},{"children":[],"name":"爱钱进"},{"children":[],"name":"E租宝"},{"children":[],"name":"有利网"},{"children":[],"name":"普信"},{"children":[],"name":"凤凰金融"},{"children":[],"name":"中港系"},{"children":[],"name":"京广和"},{"children":[],"name":"泛亚"},{"children":[],"name":"兴麟系"},{"children":[],"name":"善林金融"},{"children":[],"name":"妙资金融"},{"children":[],"name":"钱妈妈"},{"children":[],"name":"民创群体"},{"children":[],"name":"捷越"},{"children":[],"name":"普惠"},{"children":[],"name":"蛋壳洗车"},{"children":[],"name":"水松原"},{"children":[],"name":"原油宝"},{"children":[],"name":"鑫茂"},{"children":[],"name":"未来网"},{"children":[],"name":"鑫龙源"},{"children":[],"name":"小牛金融"},{"children":[],"name":"汉富诺远"},{"children":[],"name":"信和财富"},{"children":[],"name":"众合天下"},{"children":[],"name":"中科汇通"},{"children":[],"name":"蚂蚁金服"},{"children":[],"name":"荷包金融"},{"children":[],"name":"滨海大宗邮币卡"},{"children":[],"name":"钱宝系"},{"children":[],"name":"唐小僧"},{"children":[],"name":"投之家"},{"children":[],"name":"晋商贷"},{"children":[],"name":"中佳易购"},{"children":[],"name":"冠群驰骋"},{"children":[],"name":"达飞云贷"},{"children":[],"name":"康得新"},{"children":[],"name":"轻易贷"},{"children":[],"name":"人人贷"},{"children":[],"name":"和信贷"},{"children":[],"name":"Ppmoney网贷"},{"children":[],"name":"盛京百善"},{"children":[],"name":"翰墨"},{"children":[],"name":"盛世农业"},{"children":[],"name":"科创"},{"children":[],"name":"鑫圆"},{"children":[],"name":"其他"}],"name":"投资受损群体"},{"children":[{"children":[],"name":"山里红"},{"children":[],"name":"善心汇"},{"children":[],"name":"云联惠"},{"children":[],"name":"麒麟矿机"},{"children":[],"name":"江苏HUB虚拟币"},{"children":[],"name":"中天兴盛"},{"children":[],"name":"其他"}],"name":"传销群体"},{"children":[{"children":[],"name":"下岗转业志愿兵"},{"children":[],"name":"两参人员"},{"children":[],"name":"复转军人"},{"children":[],"name":"企业军转干部"},{"children":[],"name":"伤残军人"},{"children":[],"name":"农村义务兵"},{"children":[],"name":"城镇义务兵"},{"children":[],"name":"进藏兵"},{"children":[],"name":"抗美援朝"},{"children":[],"name":"对越自卫反击战退役人员"},{"children":[],"name":"退伍士兵"},{"children":[],"name":"自主择业军转干部"},{"children":[],"name":"军烈属"},{"children":[],"name":"退役人员涉访群体"},{"children":[],"name":"其他"}],"name":"军队退役人员涉访群体"},{"children":[{"children":[],"name":"商铺业主"},{"children":[],"name":"小区业主"},{"children":[],"name":"投资受损"},{"children":[],"name":"商票债权人"},{"children":[],"name":"讨薪"},{"children":[],"name":"内部员工"},{"children":[],"name":"其他"}],"name":"恒大集团"},{"children":[{"children":[],"name":"外省"},{"children":[],"name":"成都市"},{"children":[],"name":"自贡市"},{"children":[],"name":"攀枝花市"},{"children":[],"name":"泸州市"},{"children":[],"name":"德阳市"},{"children":[],"name":"绵阳市"},{"children":[],"name":"广元市"},{"children":[],"name":"遂宁市"},{"children":[],"name":"内江市"},{"children":[],"name":"乐山市"},{"children":[],"name":"南充市"},{"children":[],"name":"眉山市"},{"children":[],"name":"宜宾市"},{"children":[],"name":"广安市"},{"children":[],"name":"达州市"},{"children":[],"name":"雅安市"},{"children":[],"name":"巴中市"},{"children":[],"name":"资阳市"},{"children":[],"name":"阿坝州"},{"children":[],"name":"甘孜州"},{"children":[],"name":"凉山州"}],"name":"小区业主群体"},{"children":[{"children":[],"name":"外省"},{"children":[],"name":"成都市"},{"children":[],"name":"自贡市"},{"children":[],"name":"攀枝花市"},{"children":[],"name":"泸州市"},{"children":[],"name":"德阳市"},{"children":[],"name":"绵阳市"},{"children":[],"name":"广元市"},{"children":[],"name":"遂宁市"},{"children":[],"name":"内江市"},{"children":[],"name":"乐山市"},{"children":[],"name":"南充市"},{"children":[],"name":"眉山市"},{"children":[],"name":"宜宾市"},{"children":[],"name":"广安市"},{"children":[],"name":"达州市"},{"children":[],"name":"雅安市"},{"children":[],"name":"巴中市"},{"children":[],"name":"资阳市"},{"children":[],"name":"阿坝州"},{"children":[],"name":"甘孜州"},{"children":[],"name":"凉山州"}],"name":"商铺业主群体"},{"children":[{"children":[],"name":"讨薪群体"}],"name":"讨薪群体"},{"children":[{"children":[],"name":"涉日维稳"}],"name":"涉日维稳"},{"children":[{"children":[],"name":"“双减”政策"}],"name":"“双减”政策"},{"children":[{"children":[],"name":"涉法涉诉"}],"name":"涉法涉诉"},{"children":[{"children":[],"name":"涉医保改革"}],"name":"涉医保改革"},{"children":[{"children":[],"name":"非访群体"},{"children":[],"name":"非访"}],"name":"非访群体"},{"children":[{"children":[],"name":"征地拆迁群体"}],"name":"征地拆迁群体"},{"children":[{"children":[],"name":"电信诈骗群体"}],"name":"电信诈骗群体"},{"children":[{"children":[],"name":"失独群体"}],"name":"失独群体"},{"children":[{"children":[],"name":"民师（民代幼）"}],"name":"民师（民代幼）"},{"children":[{"children":[],"name":"长租公寓"},{"children":[],"name":"野生动物养殖户"},{"children":[],"name":"患者家属"},{"children":[],"name":"其他"}],"name":"新冠疫情群体"},{"children":[{"children":[],"name":"大货车司机"},{"children":[],"name":"出租车司机"},{"children":[],"name":"网约车司机"},{"children":[],"name":"其他"}],"name":"涉车群体"},{"children":[{"children":[],"name":"民代幼教师"},{"children":[],"name":"未分配大中专毕业生"},{"children":[],"name":"乡村医生群体"},{"children":[],"name":"协解职工"},{"children":[],"name":"疫苗受害者家属"},{"children":[],"name":"其他"}],"name":"历史遗留问题"},{"children":[{"children":[],"name":"优胜教育"},{"children":[],"name":"学霸君"},{"children":[],"name":"其他"}],"name":"教育培训群体"},{"children":[{"children":[],"name":"大病致贫"},{"children":[],"name":"阴滋病群体"},{"children":[],"name":"渐冻症"},{"children":[],"name":"尘肺病"},{"children":[],"name":"其他"}],"name":"医疗群体"},{"children":[{"children":[],"name":"高考学生家长"}],"name":"学生家长"},{"children":[{"children":[],"name":"“双语”群体"}],"name":"“双语”群体"},{"children":[{"children":[],"name":"“学习”群体"}],"name":"“学习”群体"},{"children":[{"children":[],"name":"脑控群体"}],"name":"脑控群体"},{"children":[{"children":[],"name":"物流群体"}],"name":"物流群体"},{"children":[{"children":[],"name":"网约车司机"},{"children":[],"name":"快递员"},{"children":[],"name":"外卖人员"},{"children":[],"name":"其他"}],"name":"新业态"},{"children":[{"children":[],"name":"自然灾害"}],"name":"自然灾害"},{"children":[{"children":[],"name":"老放映员"}],"name":"老放映员"},{"children":[{"children":[],"name":"退休“中人”"}],"name":"退休“中人”"},{"children":[{"children":[],"name":"水电移民"}],"name":"水电移民"},{"children":[{"children":[],"name":"涉黑犯罪前科"},{"children":[],"name":"严重暴力犯罪前科"},{"children":[],"name":"涉爆炸物犯罪前科"},{"children":[],"name":"涉枪支弹药案犯罪前科"},{"children":[],"name":"其他前科"}],"name":"前科人员群体"},{"children":[{"children":[],"name":"颠覆破坏"},{"children":[],"name":"高校维稳"},{"children":[],"name":"网上不当言论"},{"children":[],"name":"涉美"},{"children":[],"name":"涉港"},{"children":[],"name":"其他涉政重点人"}],"name":"涉政重点人员群体"},{"children":[{"children":[],"name":"涉藏"}],"name":"涉藏群体"},{"children":[{"children":[],"name":"邪教群体"}],"name":"邪教群体"},{"children":[{"children":[],"name":"涉恐"}],"name":"涉恐群体"},{"children":[{"children":[],"name":"轻微滋事精神病人"},{"children":[],"name":"有潜在暴力倾向精神病人"},{"children":[],"name":"肇事肇祸精神病人"},{"children":[],"name":"其他精神病人"}],"name":"精神障碍患者"},{"name":"涉个人极端","children":[{"children":[],"name":"涉效仿珠海"},{"children":[],"name":"扬言个人极端"},{"children":[],"name":"其他"}]},{"name":"涉延迟退休","children":[{"children":[],"name":"涉延迟退休"}]},{"children":[{"children":[],"name":"其他"}],"name":"其他"}]
```

# 17.4

- XMKFB-8542[XMKFB-8516] 后 - 优化上报ST逻辑
- XMKFB-8549[XMKFB-8532] 后 - 专项工作增加对应信息存储
- XMKFB-8532 【自贡】【要情】专项工作增加类别选项
- XMKFB-8574 后-【自贡】- 线索-人员库 检索慢问题处理
- XMKFB-8543[XMKFB-8516] 后 - 优化回流数据中人员信息
- XMKFB-8516 【自贡】【线索】线索优化0523
- XMKFB-8595[XMKFB-8586] 后 - 排查大量草稿箱重复数据出现原因
- XMKFB-8594[XMKFB-8586] 后 - 对于等级为空的数据默认塞入待定
- XMKFB-8600 【德阳】任务，人员自动建档以后在人员档案列表里未作显示
- XMKFB-8629 【自贡】线索数据有漏数据的现象

## 新增配置项

```properties
# 20250527 - 褚川宝 - 要情专项工作中的类别
intelligence.yaoqing.attribute.zxgzlb=[{"name":"打击长江流域非法捕捞","children":[]},{"name":"打击跨境赌博情报调研","children":[]},{"name":"打击整治文娱情报研判","children":[]},{"name":"涉非专项情报","children":[]},{"name":"长江大保护","children":[]}]
```
