# 1.0

## 新增配置项

```properties
# search 模块
## 人像识别是否开启Debug模式（成都开发环境置为true,生产环境置为false）
search.face.recognition.debug.enable=false

```

新增“commons-yi-to-14yi.yaml”文件用于配置1比14亿的配置信息

# 1.1

## 新增配置项

```properties
# search 模块 20240119
# 20240119 消息发送的Kafka服务器地址
search.face.recognition.sendMessage.bootServer=
# 20240119 消息发送的Topic
search.face.recognition.sendMessage.topic=archive_data_consume_add_topic
# OSS的内网地址，一般是对应模块的svc
warning.subscribe.ossHost=http://police-gateway-svc:8888
# OSS的互联网地址，浏览器可访问的地址
warning.subscribe.ossInternetHost=https://ys.dev.trs
```

1.2

- XMKFB-478[XMKFB-460] 【泸州】后 - 交警专题导出文件生成逻辑调整
- XMKFB-460 【泸州】交警专题导出优化
- XMKFB-477[XMKFB-462] 【泸州】后 - 提供统计结果获取接口
- XMKFB-462 【泸州】交警专题统计功能
- XMKFB-435 【高新】检索逻辑增加档案补录
- # 高新： 2024-02-04 发版
- XMKFB-532 后 - 档案补录对于新能源车牌识别错误
- # 高新： 2024-02-05 RC 发版
- 优化构建脚本，支持人工选择触发
-

## 新增配置项

```yaml
search:
  producer:
    kafka:
      archive:
        topic: archive_data_consume_add_topic
        bootServer: Kafka的地址
        targetVo:
          enable: true
        archivesVo:
          enable: true
  consumer:
    kafka:
      archive:
        enable: true
        pollDuration: 5
        maxPollRecords: 500
        bootStrapServers: Kafka的地址
        groupId: 'search-consumer-kafka-archive_data_consume_add_topic-group'
        topic: archive_data_consume_add_topic
        autoOffsetReset: 'earliest'
  api:
    # 高新能力平台中“车牌号查询接口（成都市）”
    chengduchepai:
      enable: false
      clientKey: '在能力平台中查询后填写'
      clientName: '在能力平台中查询后填写'
      publicKey: '在能力平台中查询后填写'
      baseUri: '在能力平台中查询后填写'
      path: '在能力平台中查询后填写'
    # 高新能力平台中“根据身份证号查询成都市常驻人口信息”
    shenfenzheng:
      enable: false
      clientKey: '在能力平台中查询后填写'
      clientName: '在能力平台中查询后填写'
      publicKey: '在能力平台中查询后填写'
      baseUri: '在能力平台中查询后填写'
      path: '在能力平台中查询后填写'
    # 高新能力平台中“车牌号查询接口（四川省）”
    sichuanchepai:
      enable: false
      clientKey: '在能力平台中查询后填写'
      clientName: '在能力平台中查询后填写'
      publicKey: '在能力平台中查询后填写'
      baseUri: '在能力平台中查询后填写'
      path: '在能力平台中查询后填写'
# 高新： 2024-02-04 发版
```

1.3

## 更新日志

- XMKFB-538[XMKFB-537] 后 - 根据特征值获取落脚点信息接口
- XMKFB-536[XMKFB-351] 后 - 智能分析列表查询
- XMKFB-435 【高新】检索逻辑增加档案补录
- XMKFB-544[XMKFB-538] 后 - 根据感知源获取轨迹数据
- XMKFB-545[XMKFB-538] 后 - 根据特征值获取轨迹数据
- XMKFB-543[XMKFB-537] 后 - 获取当前警情信息
- XMKFB-562[XMKFB-351] 后 - 提供轨迹相关查询接口
- XMKFB-576[XMKFB-569] 后 - 增加定时判断ES索引是否删除
- XMKFB-569 检索配置改进
- XMKFB-607 后 - 档案检索出现重复数据
- XMKFB-610 后 - 优化人像识别流程
- XMKFB-596 后 - 优化轨迹列表查询
- # 高新： 2024-02-28 发版
- # 泸州： 2024-02-28 发版
-

## 新增配置项

```yaml
search:
  gjxx:
    tzlx:
      searchMapping: '{"person":"身份证","car":"车牌","phone":"手机号","IMSI":"imsi","IMEI":"imei","MAC":"mac"}'
    gzylx:
      searchMapping: '{"1":"电子围栏","4":"wifi","5":"人像","7":"卡口"}'
  ai:
    jq:
      tableName: dwd_jwzh_asj_jqxx
    gj:
      tableName: theme_gjxxb
com:
  trs:
    synchronized:
      tableEsIndexStatus:
        task:
          enable: true
          cron: 0 0 3 * * ?
```

1.4

## 更新日志

- XMKFB-649[XMKFB-626] 后 - 优化单位列表查询接口
- XMKFB-637[XMKFB-626] 后 - 优化通知书的导出
- XMKFB-642[XMKFB-626] 后 - 市局账号导出的统计表，导出的excel无内容
- XMKFB-640[XMKFB-626] 后 - 4. 预警详情未显示人员户籍地址
- XMKFB-641[XMKFB-626] 后 - 数据的处置单位没有对应的派出所
- XMKFB-677[XMKFB-615] 后 - 修改事件是否发生状态接口开发
- XMKFB-763 后 - Excel导入基础检索库报错
- XMKFB-847[XMKFB-789] 后 - 增加“来源系统”筛选条件，用于查询预警信息的系统来源。
- XMKFB-851[XMKFB-789] 后 - 预警推送人员设置年龄区间14-70岁。
- XMKFB-829[XMKFB-789] 后 - 部分用户赋权后无法查看交警列表的问题
- XMKFB-833[XMKFB-789] 后 - 历史违法记录无法查看违法照片（显示场景大图）。
- XMKFB-811 后 - 车档，手机档智能分析页接口开发
- XMKFB-889 后 - 交警专题页面查询全部时卡顿
- # 泸州： 2024-03-22 发版
- XMKFB-849[XMKFB-789] 后 - 5. 派出所导出的统计表，要统计乡镇街道。
- XMKFB-882[XMKFB-789] 后 - 导出违法告知书优化
- XMKFB-883[XMKFB-789] 后 - 批量导出优化
- XMKFB-884[XMKFB-789] 后 - 违法处理通知书、违法行为确认书
- XMKFB-815[XMKFB-814] 检索接口提供
- XMKFB-924 高新-【智能检索】提供一个根据手机号查询人员档案详情的接口
- XMKFB-931 导出通知书和确认书后对应记录的处置时间应更新显示
- XMKFB-942 导出的道路交通安全违法处理通知书表格应作调整优化
- XMKFB-948 告知书文件表格中的数据内容有误
- # 泸州： 2024-03-27 发版
- XMKFB-950 后 - 轨迹查询接口增加图片字段返回
- XMKFB-956 批量导出的文件包含三书，但其文件名称显示有误
- XMKFB-957 后 - 人像跟车辆最近抓拍图接口开发
- XMKFB-958 【优化】导出告知书表格中身份证号码列宽应作优化处理
- XMKFB-959 后 - 优化智能分析标签数据的返回
- XMKFB-1017[XMKFB-1012] 后 - 优化Kafka数据的接收处理处理
- XMKFB-1016[XMKFB-1012] 后 - 优化导出统计
- XMKFB-1018[XMKFB-1012] 后 - 优化导出的三书
- XMKFB-1089[XMKFB-885] 后 - 新增要情功能开发
- XMKFB-1118[XMKFB-885] 后 - 要情列表功能开发
- # 泸州： 2024-04-15 发版
- XMKFB-1136[XMKFB-1116] 后 - 确认书内容错误
- XMKFB-1134[XMKFB-1116] 后 - 导出的劝导量处罚量都是空的
- XMKFB-1135[XMKFB-1116] 后 - 导出违法处理告知书数据内容有问题
- XMKFB-1130[XMKFB-1116] 后 - 排查相关小数展示问题
- XMKFB-1138[XMKFB-1116] 后 - 优化数据消费入库
- # 泸州已RC
- XMKFB-1059 高新-【1比14亿】正式环境已部署后端代码需要调整
- XMKFB-1204[XMKFB-1200] 后 - 批量导出支持过滤已导出数据
- XMKFB-1203[XMKFB-1200] 后 - 告知书按照村拆分成多个数据
- XMKFB-1206[XMKFB-1200] 后 - 增加所属乡镇街道跟所属行政村字段的返回
- 修复空指针异常
- 优化查询详情的逻辑
- XMKFB-1270[XMKFB-1268] 后 - 优化导出数据中违法时间字段
- XMKFB-1271[XMKFB-1268] 后 - 优化反馈结果时处罚编号的检测
- XMKFB-1278[XMKFB-1187] 后-检索支持根据多个身份号检索人员轨迹数据
-

## 新增配置

```yaml
search:
  content:
    parse:
      mapping: '{"car":"[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z][A-Z]{1}[A-Z0-9]{4,5}[A-Z0-9挂学警港澳]","phone":"(?<!\\d)((1\\d{10})|(\\d{3,4}-?\\d{6,8})|(\\+?((00)[1-9]\\d{1,3}|86|\\+86)?\\d{10,15}))(?![\\dxX])","person":"\\d{17}[\\dXx]","case":"A\\d{22}"}'
# 泸州： 2024-03-22 发版
```

## 部署前置

```yaml
# 修改统计数据量的周期
com:
  trs:
    synchronized:
      search:
        task:
          count: 0 10 2/3 * * ?
# 泸州： 2024-03-27 发版
```

5.1

## 更新日志

- XMKFB-1376[XMKFB-1374]  合成作战的dwd写入工作

## 新增配置

```yaml
business:
  archives:
    config:
      properties: '[{"serviceCode":"dwd_hecheng_chat_message","dbType":"ES","dbName":"","tableName":"dwd_hecheng_chat_message"},{"serviceCode":"dwd_hecheng_collaboration","dbType":"ES","dbName":"","tableName":"dwd_hecheng_collaboration"},{"serviceCode":"dwd_hecheng_results","dbType":"ES","dbName":"","tableName":"dwd_hecheng_results"},{"serviceCode":"dwd_hecheng_info","dbType":"ES","dbName":"","tableName":"dwd_hecheng_info"},{"serviceCode":"dwd_hecheng_collaboration","dbType":"Hive","dbName":"trs_test","tableName":"dwd_hecheng_collaboration"},{"serviceCode":"dwd_hecheng_results","dbType":"Hive","dbName":"trs_test","tableName":"dwd_hecheng_results"},{"serviceCode":"dwd_hecheng_info","dbType":"Hive","dbName":"trs_test","tableName":"dwd_hecheng_info"},{"serviceCode":"dwd_hecheng_chat_message","dbType":"Hive","dbName":"trs_test","tableName":"dwd_hecheng_chat_message"}]'
      persist:
        way: 'DB'
      enable:
        kerberos: true

dwd:
  repository:
    config:
      esRepositoryType: ES
      esRepositoryHost:
      esRepositoryPort:
      esRepositoryUserName:
      esRepositoryPassword:
      hiveRepositoryType: hive
      hiveRepositoryHost:
      hiveRepositoryPort:
      hiveRepositoryUserName:
      hiveRepositoryPassword:
kafka:
  search:
    business:
      archives:
        topic: 'business-archives'
        group: 'business-archives-group'
spring:
  kafka:
    consumer:
      bootstrap-servers: kafka地址
      group-id: 'business-archives-group'
      auto-startup: true
      auto-offset-reset: earliest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
```

# 5.2

- XMKFB-1512[XMKFB-1374] 后 - 业务库检索功能开发
- XMKFB-1572[XMKFB-1374] 后 - 导入新增的主题库到检索配置中

# 5.3

## 更新日志

- XMKFB-1627 后 - 交警专题导出文件的横版竖版问题
- XMKFB-1621 检索数据导入
- 

# 5.4

## 更新日志

- 交警专题统计提供mock数据
- XMKFB-1792[XMKFB-1786] 后 - 优化交警专题数据消费
- XMKFB-1791[XMKFB-1786] 后 - 治理相关数据到感知源库中
- XMKFB-1857[XMKFB-1668] 检索-业务-案件异常
- 

## 部署前置

导入version下sql文件夹的tb_rod_body.sql文件

## 新增配置

```yaml
search:
  traffic:
    statistics:
      mockData: '{"sj":[{"dept":"合江县","illegalCaptureNum":24966,"persuasionNum":22195,"timelyPersuasionRate":"88.90%","onTimePunishNum":19698,"onTimePunishRate":"78.90%","shouldPunishNum":347,"punishNum":248,"timelyPunishRate":"71.54%","onTimeConfirmNum":189,"onTimeConfirmRate":"76.16%","unConfirmNum":2,"unConfirmRate":"0.58%"},{"dept":"古蔺县","illegalCaptureNum":12310,"persuasionNum":10434,"timelyPersuasionRate":"84.76%","onTimePunishNum":9942,"onTimePunishRate":"80.76%","shouldPunishNum":137,"punishNum":96,"timelyPunishRate":"74.25%","onTimeConfirmNum":72,"onTimeConfirmRate":"74.39%","unConfirmNum":3,"unConfirmRate":"2.30%"},{"dept":"江阳区","illegalCaptureNum":13288,"persuasionNum":12018,"timelyPersuasionRate":"90.44%","onTimePunishNum":11220,"onTimePunishRate":"84.44%","shouldPunishNum":167,"punishNum":124,"timelyPunishRate":"74.12%","onTimeConfirmNum":96,"onTimeConfirmRate":"77.40%","unConfirmNum":8,"unConfirmRate":"4.78%"},{"dept":"纳溪区","illegalCaptureNum":9942,"persuasionNum":8585,"timelyPersuasionRate":"89.04%","onTimePunishNum":8296,"onTimePunishRate":"86.04%","shouldPunishNum":130,"punishNum":97,"timelyPunishRate":"74.25%","onTimeConfirmNum":72,"onTimeConfirmRate":"74.39%","unConfirmNum":3,"unConfirmRate":"2.30%"},{"dept":"叙永县","illegalCaptureNum":77710,"persuasionNum":66528,"timelyPersuasionRate":"85.61%","onTimePunishNum":66528,"onTimePunishRate":"85.61%","shouldPunishNum":2133,"punishNum":1526,"timelyPunishRate":"71.56%","onTimeConfirmNum":1025,"onTimeConfirmRate":"67.17%","unConfirmNum":56,"unConfirmRate":"2.63%"},{"dept":"龙马潭区","illegalCaptureNum":6488,"persuasionNum":5790,"timelyPersuasionRate":"89.24%","onTimePunishNum":5660,"onTimePunishRate":"87.24%","shouldPunishNum":68,"punishNum":52,"timelyPunishRate":"76.64%","onTimeConfirmNum":43,"onTimeConfirmRate":"82.31%","unConfirmNum":1,"unConfirmRate":"1.47%"},{"dept":"泸县","illegalCaptureNum":33121,"persuasionNum":29576,"timelyPersuasionRate":"89.27%","onTimePunishNum":24268,"onTimePunishRate":"73.27%","shouldPunishNum":530,"punishNum":388,"timelyPunishRate":"73.27%","onTimeConfirmNum":314,"onTimeConfirmRate":"80.84%","unConfirmNum":12,"unConfirmRate":"2.26%"}],"qx":[{"dept":"天仙派出所","illegalCaptureNum":2180,"persuasionNum":2161,"timelyPersuasionRate":"99.13%","onTimePunishNum":2067,"onTimePunishRate":"94.82%","shouldPunishNum":29,"punishNum":22,"timelyPunishRate":"75.86%","onTimeConfirmNum":12,"onTimeConfirmRate":"54.55%","unConfirmNum":0,"unConfirmRate":"0.00%"},{"dept":"白节派出所","illegalCaptureNum":793,"persuasionNum":545,"timelyPersuasionRate":"68.73%","onTimePunishNum":545,"onTimePunishRate":"68.73%","shouldPunishNum":11,"punishNum":7,"timelyPunishRate":"63.64%","onTimeConfirmNum":1,"onTimeConfirmRate":"14.29%","unConfirmNum":2,"unConfirmRate":"0.25%"},{"dept":"棉花坡派出所","illegalCaptureNum":3,"persuasionNum":3,"timelyPersuasionRate":"100.00%","onTimePunishNum":1,"onTimePunishRate":"33.33%","shouldPunishNum":0,"punishNum":0,"timelyPunishRate":"00.00%","onTimeConfirmNum":0,"onTimeConfirmRate":"00.00%","unConfirmNum":0,"unConfirmRate":"00.00%"},{"dept":"渠坝派出所","illegalCaptureNum":7,"persuasionNum":7,"timelyPersuasionRate":"100.00%","onTimePunishNum":1,"onTimePunishRate":"14.29%","shouldPunishNum":0,"punishNum":0,"timelyPunishRate":"0.00%","onTimeConfirmNum":0,"onTimeConfirmRate":"0.00%","unConfirmNum":0,"unConfirmRate":"0.00%"},{"dept":"上马派出所","illegalCaptureNum":673,"persuasionNum":76,"timelyPersuasionRate":"11.29%","onTimePunishNum":76,"onTimePunishRate":"11.29%","shouldPunishNum":9,"punishNum":6,"timelyPunishRate":"66.67%","onTimeConfirmNum":2,"onTimeConfirmRate":"33.33%","unConfirmNum":1,"unConfirmRate":"0.15%"},{"dept":"安富派出所","illegalCaptureNum":383,"persuasionNum":213,"timelyPersuasionRate":"55.61%","onTimePunishNum":213,"onTimePunishRate":"55.61%","shouldPunishNum":5,"punishNum":4,"timelyPunishRate":"80.00%","onTimeConfirmNum":1,"onTimeConfirmRate":"25.00%","unConfirmNum":0,"unConfirmRate":"0.00%"},{"dept":"永宁派出所","illegalCaptureNum":219,"persuasionNum":216,"timelyPersuasionRate":"98.63%","onTimePunishNum":216,"onTimePunishRate":"98.63%","shouldPunishNum":3,"punishNum":2,"timelyPunishRate":"66.67%","onTimeConfirmNum":2,"onTimeConfirmRate":"100.00%","unConfirmNum":0,"unConfirmRate":"0.00%"},{"dept":"丰乐派出所","illegalCaptureNum":475,"persuasionNum":450,"timelyPersuasionRate":"94.74%","onTimePunishNum":450,"onTimePunishRate":"94.74%","shouldPunishNum":6,"punishNum":5,"timelyPunishRate":"83.33%","onTimeConfirmNum":4,"onTimeConfirmRate":"80.00%","unConfirmNum":25,"unConfirmRate":"5.26%"},{"dept":"合面派出所","illegalCaptureNum":1377,"persuasionNum":899,"timelyPersuasionRate":"65.29%","onTimePunishNum":899,"onTimePunishRate":"65.29%","shouldPunishNum":19,"punishNum":14,"timelyPunishRate":"73.68%","onTimeConfirmNum":9,"onTimeConfirmRate":"64.29%","unConfirmNum":12,"unConfirmRate":"0.87%"},{"dept":"护国派出所","illegalCaptureNum":767,"persuasionNum":439,"timelyPersuasionRate":"57.24%","onTimePunishNum":439,"onTimePunishRate":"57.24%","shouldPunishNum":10,"punishNum":8,"timelyPunishRate":"80.00%","onTimeConfirmNum":6,"onTimeConfirmRate":"75.00%","unConfirmNum":2,"unConfirmRate":"0.26%"},{"dept":"东升派出所","illegalCaptureNum":388,"persuasionNum":266,"timelyPersuasionRate":"68.56%","onTimePunishNum":266,"onTimePunishRate":"68.56%","shouldPunishNum":5,"punishNum":4,"timelyPunishRate":"80.00%","onTimeConfirmNum":2,"onTimeConfirmRate":"50.00%","unConfirmNum":8,"unConfirmRate":"2.06%"},{"dept":"大渡派出所","illegalCaptureNum":694,"persuasionNum":667,"timelyPersuasionRate":"96.11%","onTimePunishNum":667,"onTimePunishRate":"96.11%","shouldPunishNum":9,"punishNum":7,"timelyPunishRate":"77.78%","onTimeConfirmNum":4,"onTimeConfirmRate":"57.14%","unConfirmNum":0,"unConfirmRate":"0.00%"},{"dept":"打古派出所","illegalCaptureNum":1152,"persuasionNum":292,"timelyPersuasionRate":"25.35%","onTimePunishNum":292,"onTimePunishRate":"25.35%","shouldPunishNum":16,"punishNum":12,"timelyPunishRate":"75.00%","onTimeConfirmNum":4,"onTimeConfirmRate":"57.14%","unConfirmNum":0,"unConfirmRate":"0.00%"},{"dept":"新乐派出所","illegalCaptureNum":331,"persuasionNum":322,"timelyPersuasionRate":"97.28%","onTimePunishNum":322,"onTimePunishRate":"97.28%","shouldPunishNum":4,"punishNum":3,"timelyPunishRate":"75.00%","onTimeConfirmNum":2,"onTimeConfirmRate":"66.67%","unConfirmNum":0,"unConfirmRate":"0.00%"},{"dept":"龙车派出所","illegalCaptureNum":200,"persuasionNum":188,"timelyPersuasionRate":"94.00%","onTimePunishNum":188,"onTimePunishRate":"94.00%","shouldPunishNum":3,"punishNum":2,"timelyPunishRate":"66.67%","onTimeConfirmNum":1,"onTimeConfirmRate":"50.00%","unConfirmNum":0,"unConfirmRate":"0.00%"}],"pcs":[{"dept":"丰乐镇","illegalCaptureNum":475,"persuasionNum":450,"timelyPersuasionRate":"94.74%","onTimePunishNum":450,"onTimePunishRate":"94.74%","shouldPunishNum":6,"punishNum":5,"timelyPunishRate":"83.33%","onTimeConfirmNum":4,"onTimeConfirmRate":"80.00%","unConfirmNum":25,"unConfirmRate":"5.26%"}]}'
```

# 6.1

## 更新日志

- XMKFB-1883[XMKFB-1854] 后 - 退回逻辑调整
- XMKFB-1882[XMKFB-1854] 后 - 导出逻辑调整
- XMKFB-1918[XMKFB-1854] 后 - 导出表格内容调整
- XMKFB-1935[XMKFB-1668] 车辆搜索标签无效
- XMKFB-1955[XMKFB-1668] 手机标签筛选无效
- XMKFB-1932[XMKFB-1668] 车辆档案中的轨迹信息地图中未显示轨迹
- XMKFB-1968 对车辆档案知识图谱的调整与优化
- XMKFB-1972[XMKFB-1668] 档案详情页中整体情况数量统计有误
- XMKFB-2012[XMKFB-1668] 精确搜索，高级搜索报错
- XMKFB-2010[XMKFB-1668] 业务配置-搜索配置-基础库，时间戳型的字段无法被选中可用排序
- XMKFB-2026 【泸州】检索档案细揽-档案信息优化
- XMKFB-2052 后 - 兼容人像识别返回的内容
- XMKFB-2068[XMKFB-1668] 补充档案详情页中的车辆信息
- 

## 新增配置

```yaml
search:
  traffic:
    rollbackTypes: '所有违法地点非本辖区,其他'
    unDealTypes: '违法行为识别错误,违法行为人身份信息识别错误,其他'
```

# 6.2

## 更新日志

- XMKFB-1949[XMKFB-1668] 手机档案关联imsi异常
- XMKFB-2059[XMKFB-1668] 人员档案，省外发证地和省内发征地标签显示的统计人员数量有误
- XMKFB-2064[XMKFB-1668] 后 - 优化业务库关联数据查询
- # 20240614发版
- XMKFB-2128[XMKFB-2035] 后 - 后端接口功能开发
- XMKFB-2035 【泸州】涉艾人员核查工具
- XMKFB-2093[XMKFB-1668] 人员档案，布控号码的扩展信息为空
- XMKFB-2148[XMKFB-1668] 人员档案，智能分析图中的布控情况未显示布控时间
- # 20240618 RC发版

## 新增配置项

```yaml
search: 
  aids: 
    tableName: shzy_ylbj_azb
    zjhmFieldName: yxzjhm
    dataTimeFieldName: bgklrsj
    fieldMapping: '[{"enName":"hzxm","zhName":"姓名","dataType":"string","order":1},{"enName":"yxzjhm","zhName":"身份证号","dataType":"string","order":2},{"enName":"hjdz","zhName":"户籍地址","dataType":"string","order":3},{"enName":"xzxxdz","zhName":"疾控统计的现住详细地址","dataType":"string","order":4},{"enName":"zdsj","zhName":"诊断日期","dataType":"string","displayFormat":"yyyy-MM-dd HH:mm:ss","order":5}]'
# 20240618 RC发版

```

# 6.3

## 更新日志

- XMKFB-2083[XMKFB-1668] 对导出线索档案数据的调整与修改
- XMKFB-2081[XMKFB-1668] 导出的全部业务档案数据为空
- XMKFB-2085[XMKFB-1668] 对导出的案件档案数据进行调整与修改
- XMKFB-2149[XMKFB-1668] 车辆档案按照车辆归属地标签筛选无效
- XMKFB-2059[XMKFB-1668] 人员档案，省外发证地和省内发征地标签显示的统计人员数量有误
- XMKFB-2065[XMKFB-1668] 档案外部图片访问异常
- # 20240621发版
- 

# 6.4

## 更新日志

- XMKFB-2269[XMKFB-2251] 【泸州】根据父任务第四点，要求后端默认的所有字段为降序排序
- XMKFB-2270[XMKFB-2251] 【泸州】根据父任务的第二点，需要提供批量勾选/取消勾选概览字段
- # 20240627发版
- 

# 7.2

- feat(智能标签): 优化解析
- # 20240802发版

# 8.1

## 更新日志

- XMKFB-2899[XMFKB-2836] 后 - 导出与处罚阈值调整
- # 20240802发版
- XMKFB-2965[XMKFB-2770] 数据表接入状态趋势图中显示的数据表个数与统计的个数不一致
- XMKFB-2967[XMKFB-2770] 数据表详情列表中所有数据表的昨日新增数据都为0
- XMKFB-2943[XMKFB-2770] 数据表详情列表中的针对分类统计的数字有误
- XMKFB-2909[XMKFB-2836] 后 - 相关筛选项调整与问题排查
- XMKFB-2895[XMKFB-2836] 后 - 数据列表，历史违法记录增加“违法地点”
- XMKFB-2917 融合检索的人档融合手机、汽车
- XMKFB-2841 情指行关于辖区的处理
- XMKFB-2061[XMKFB-1668] 警情档案详情页中的智能分析和档案信息数据为空
- # 20240809发版

## 部署前置

```yaml
# 20240802 - 肖豪 - 泸州 - 调整交警专题相关配置项 - search.yaml
search:
  traffic:
    rollbackTypes: 违法行为人非本辖区人员,其他原因
    unDealTypes: 违法行为人确无法联系,违法行为识别错误,其他原因
# 20240809发版
```

# 8.2

## 更新日志

- XMKFB-2977 高新-【检索】在详情页面的搜索框也需要触发同步接口
- refactor(标签): 优化检索的多标签过滤条件
- XMKFB-2987[XMKFB-2836] 后 - 数据列表区县统计调整
- XMKFB-2061[XMKFB-1668] 警情档案详情页中的智能分析和档案信息数据为空
- # 20240809发版

# 8.3

## 更新日志

- XMKFB-3062 高新GA-超级检索的人员详情页，人员轨迹新增查看抓拍照片的功能

# 8.4

## 更新日志

- XMKFB-3243[XMKFB-3240] 后 - 优化删除逻辑
- XMKFB-3232[XMKFB-3230] 后 - 轨迹图片获取优化
- XMKFB-3287[XMKFB-3147] 后 - 优化数据表的保存跟查询
- XMKFB-3241[XMKFB-3240] 后 - 违法告知书的内容和格式调整
- XMKFB-3250[XMKFB-3199] 接口返回值需要增加一个一级分类的名称
- XMKFB-3234[XMKFB-2440] 后 - 设计相关接口方案与开发
- XMKFB-3377[XMKFB-3147] 后 - 群成员列表接口开发
- XMKFB-3294[XMKFB-3224] 后 - 红名单列表功能开发
- XMKFB-3378[XMKFB-3147] 后 - 群消息列表开发
- feat(检索): 优化细览逻辑跟其他档案反查人档逻辑
- XMKFB-3295[XMKFB-3224] 后 - 兜底定时任务功能开发
- XMKFB-3263 省厅日志接入
- 优化数据表字段同步接口
- XMKFB-3147 【智慧云脑】人员档案-消息记录
- XMKFB-3423[XMKFB-3224] 后 - 高新项目会调度第三方接口，红名单也要对其生效
- XMKFB-3439 后 - 数据打上红名单标签之后依然能被搜索到
- XMKFB-3438 后 - 红名单添加车牌无效
- XMKFB-3487 【高新】- 编辑手机号码，更新额外字段，导致报错

## 新增配置

```yaml
# 其中search.field.specialNatureList应该原来就存在，所以需要进行比对合并
search: 
    specialNatureList: '[{"key":"GROUP_VIEW","desc":"查看上下文"}]'
    field: 
      specialNatureList: '[{"key":"ID","desc":"主键"},{"key":"URL","desc":"链接"},{"key":"GROUP_ID","desc":"群组ID"},{"key":"GROUP_UID","desc":"UID"}]'
# 20240822 - 肖豪 - 增加红名单定时任务同步配置
com:
  trs:
    synchronized:
      redList:
        task:
          enable: true
          cron: 0 0 3 * * ?
```

## 部署后置

```markdown
# 高新环境部署之后配置聊天记录表

1. 部署之后，进入系统设置->业务配置->搜索配置->基础库，点击新增，输入对应信息保存(之前已经配置过的就不用新增了)
2. 在浏览器上访问http(s)://ip:port/search/public/table/reImport?tableNames=配置的表名&moduleName=基础库，进行首次字段同步
3. 同步完成之后，进入字段配置，修改概览可见，调整字段顺序，给主键ID，群号，UID等字段配置对应的特殊属性，修改证件号跟手机号的所属主题，时间类型字段勾选支持排序
4. 返回上一级，修改数据表配置，给数据表配置对应的特殊属性，排序字段，所属主题，主键ID，所属分类，入库时间字段等配置，并保存
5. 切换到用户权限的数据权限中，给对应角色的全景通查/精确搜索中配置对应数据表的搜索权限

```

# 8.5

## 更新日志

- XMKFB-3404[XMKFB-3240] 后 - 3. 调整统计表格式和内容
- XMKFB-3457[XMKFB-3240] 后 - 2. 调整“违法行为”及名称
- XMKFB-3536[XMKFB-3224] 超级检索，可直接通过筛选红名单标签查看显示红名单数据
- XMKFB-3515[XMKFB-3396] 后 - 标签筛选逻辑调整
- XMKFB-3544[XMKFB-3462] 后- 检索中调用第三方接口刷新时增加频次控制
- XMKFB-3405[XMKFB-3240] 后 - 增加预统计功能
- XMKFB-3578 高新-【检索】智能分析扩展一种人物关系
- XMKFB-3581 【高新】超级检索，人员档案-只能分析中显示出常驾驶车辆的时间数据
- XMKFB-3532 【高新】超级检索，设置了红名单后仍可通过全景通查和精确搜索查看到对应的档案信息
- XMKFB-3445 数据汇聚，对停更状态的数据表的定义进行调整
- XMKFB-3488[XMKFB-3224] 设置红名单后仍能通过档案详情页去搜索到该人员相关档案数据
- XMKFB-3406[XMKFB-3240] 后 - 处罚阈值设定
- XMKFB-3644[XMKFB-3224] 编辑红名单失败
- XMKFB-3658 后-【智能检索】人档、车档检索优化
- 

## 新增配置

```yaml
# 20240829 - 肖豪 - 增加检索同步第三方数据频次限制 - 仅高新
search:
  sendMessage:
    redis:
      expireTime: 86400
# 20240829 - 蒋俊杰 - 统计结果预统计
com:
  trs:
    synchronized:
      traffic:
        preStatistics:
          task:
            enable: true
            cron: 0 0 3 * * ?
```

# 9.1

## 更新日志

- XMKFB-3534[XMKFB-3224] 【高新】新增、编辑红名单时作判重校验
- XMKFB-3576 【高新】超级检索，当没有标签的搜索权限后标签列表中仍显示了该标签数据
- XMKFB-3671[XMKFB-3224] 对编辑红名单里的判重校验进行优化调整
- XMKFB-3760[XMKFB-3689] 后 - 接口设计并开发
- XMKFB-3689 【智慧云脑】标签可解释
- XMKFB-3739[3735] 案件档案异常


# 9.2

## 更新日志

- XMKFB-3820[XMKFB-3735] 标签检索统计逻辑改进
- XMKFB-3839 【高新】超级检索，屏蔽“安数-SDK-手机轨迹”类型轨迹的查看照片按钮
- XMKFB-3947[XMKFB-2440] 在多轨分析详情中将多轨名称编辑为空后的toast提示文字应作调整
- XMKFB-3948[XMKFB-2440] 多轨分析数据应按照创建时间倒序排列
- XMKFB-3958[XMKFB-3930] 搜索日志接口添加类型


# 9.3

## 更新日志

- XMKFB-3955[XMKFB-2440] 多轨分析数据的人员未作判重校验
- XMKFB-4002 后 - 部署Kerberos认证版本的检索应用
- XMKFB-3770[XMKFB-3735] 人员档案知识图谱异常-轨迹概况
- XMKFB-4034[XMKFB-3960] 自贡-多轨分析-添加结果修改

### 新增配置

```yaml
# 自贡增加h3c的Kerberos认证
warning:
  subscribe:
    searchRepositoryKerberos: true
    searchRepositoryCloudType: h3c
    searchRepositoryPrincipal: 对应信息
    searchRepositoryKrb5Path: 对应信息
    searchRepositoryUserKeytabPath: 对应信息
```

# 9.4 

## 更新日志

- XMKFB-4108 【高新】门户登录后接口报错401，提示“未授权，请重新登录”
- XMKFB-4135[XMKFB-3880] 后 - 检索系统增加追加跟移除标签的行为
- XMKFB-4087[XMKFB-3905] 后 - 白天跟夜晚最好是可以后端配置
- XMKFB-3736[XMKFB-3735] 手机档案可输入imsi
- XMKFB-4073[XMKFB-3095] 后 - 轨迹时间支持分组统计
- XMKFB-4088 后-【自贡】-风险人员-标签打标相关服务提供
- XMKFB-4164[XMKFB-3925]  风险分值明细和自动扣减记录列表数据的排序方式应作调整
- XMKFB-4212[XMKFB-2440] 多轨分析可一次重复添加同一人员数据
- XMKFB-4089[XMKFB-3880] 后-【自贡】-风险人员数据接入  
- XMKFB-4129【高新】数据汇聚，数据表统计的数据贡献量有误

## 新增配置

```yaml
# 20240924 - 肖豪 - 新增白天、夜晚的小时段配置，轨迹类型配置
search:
  aiAnalysis:
    dayAndNightHours: '[{"key":"day","value":"8,9,10,11,12,13,14,15,16,17,18,19"},{"key":"night","value":"20,21,22,23,0,1,2,3,4,5,6,7"}]'
    gzyList: '[{"name":"人像","children":[]},{"name":"卡口","children":[]},{"name":"管委会-停车场数据","children":[]},{"name":"以泊科技-停车场数据","children":[]}]'
# 20240927 - 左开元 - 风险人员推送消息配置
spring:
  cloud:
    stream:
      bindings:
        riskPersonOut:
          destination: risk-person
          group: risk-person-message-producer
          content-type: application/json
          binder: kafka
          producer:
            partition-count: 1
```

# 10.2

- XMKFB-4137 高新-【门户】检索系统日志需推送到门户展示
- XMKFB-4300[XMKFB-4240] 后-【高新】线索池消息上下文增加群聊信息列表接口
- XMKFB-4279 【泸州】人员档案，智能分析中显示的轨迹概况样式有误
- 

## 部署前置

> nacos中增加新的配置文件“search-new.properties”

## 新增配置

```properties
# 20241008 - 褚川宝 - 推送日志到门户的URL
# https://trsyapi.trscd.com.cn/project/546/interface/api/38002
search.log.push.menhu.url=
# 20241008 - 褚川宝 - 推送日志到门户的thirdPartyName
search.log.push.menhu.thirdPartyName=云基

```

# 10.3

- XMKFB-4438 【高新】超级检索，红名单标签检索存在的问题
- XMKFB-4533 【高新】门户大屏-线索池，线索详情里群聊的展示顺序应作调整
- XMKFB-4404 【泸州】融合检索，人员轨迹信息里统计显示的频率总数与实际数量不一致
- XMKFB-4330 高新-落脚点接商汤数据
- XMKFB-4687 后 - 红名单判断导致全景通查异常


## 新增配置

```properties
# 高新的search-new.properties中增加
# 20241017 - 褚川宝 - 高新kafka消费
search.foothold.consumer.kafka.gx.enable=true
search.foothold.consumer.kafka.gx.pollDuration=5
search.foothold.consumer.kafka.gx.bootStrapServers=修改为对应地址
search.foothold.consumer.kafka.gx.groupId=st_foothold_groupId
search.foothold.consumer.kafka.gx.topic=st_foothold
search.foothold.consumer.kafka.gx.autoOffsetReset=earliest
```

# 10.4

- XMKFB-4644 超级搜索人员档案详情页面，标签的显示问题-新增“其他标签”
- XMKFB-4607 【高新】超级检索，人员档案信息中将群聊信息表作为通用配置
- XMKFB-4599 超级检索，车辆档案和手机档案的轨迹信息数据为空
- XMKFB-4723 后-【自贡】-检索BUG处理
- 

## 部署前置

```yaml
# search.yaml中修改配置项
search:
  table:
    field:
      specialNatureList: '[{"key":"ID","desc":"主键"},{"key":"URL","desc":"链接"},{"key":"GROUP_ID","desc":"群组ID"},{"key":"GROUP_UID","desc":"UID"},{"key":"GROUP_NAME","desc":"群组名称"},{"key":"GROUP_CREATE_UID","desc":"群组创建人UID"},{"key":"GROUP_CREATE_TIME","desc":"群组创建时间"},{"key":"GROUP_USERNAME","desc":"消息发送人姓名"},{"key":"GROUP_MESSAGE","desc":"聊天消息内容"},{"key":"GROUP_SEND_TIME","desc":"消息发送时间"},{"key":"GROUP_USER_IP","desc":"消息发送人IP"},{"key":"GROUP_CHAT_IP","desc":"群组会话IP"},{"key":"TRS_IN_TIME","desc":"TRS_IN_TIME"},{"key":"TRS_SOURCE_TIME","desc":"TRS_SOURCE_TIME"}]'
```

# 10.5

- XMKFB-4802[XMKFB-4695] 接口调整 - 无数据时需要有一个空字符串占位
- XMKFB-4840[XMKFB-4837] 后 - 业务档增加基础库数据查询
- XMKFB-4868 【高新】超级检索，轨迹信息里的停车场感知源类型不做车辆种类区分
- XMKFB-4857 经纬度过滤工具
- XMKFB-4843 超级检索—轨迹信息，路径规划的轨迹地点包含了经纬度为(-1,-1)的点位
- [XMKFB-4883] 【自贡】风险人员列表中未自动添加线索里涉及的人员数据


# 11.1

- XMKFB-4861[XMKFB-4839] 搜索配置字段改进
- XMKFB-4862[XMKFB-4839] 后 - 优化检索配置保存
- XMKFB-4923[XMKFB-4886] 后 - 接入技侦数据
- XMKFB-4948[XMKFB-4966] 后 - 德阳轨迹数据治理到gjxxb中
- XMKFB-5015 【高新】超级检索，车辆档案数据唯一取值进行调整
- 


## 部署前置

```properties
# 20241107 - 张阳 - 新增技侦信息表
search.theme.jz.tableName=theme_jzxxb
# 20241107 - 张阳 - 技侦数据kafka消费(仅德阳)
search.consumer.kafka.jz.enable=true
search.consumer.kafka.jz.pollDuration=5
search.consumer.kafka.jz.maxPollRecords=500
search.consumer.kafka.jz.bootStrapServers=http://10.18.20.131:9092
search.consumer.kafka.jz.groupId=RTW_DWFN_QZ_DY_XPQK_groupId
search.consumer.kafka.jz.topic=RTW_DWFN_QZ_DY_XPQK
search.consumer.kafka.jz.autoOffsetReset=earliest
# 20241108 - 蒋俊杰 - 轨迹kafka消费(德阳的search-new.properties中增加)
search.consumer.kafka.trajectory.enable=true
search.consumer.kafka.trajectory.pollDuration=5
search.consumer.kafka.trajectory.bootStrapServers=修改为对应地址
search.consumer.kafka.trajectory.groupId=trs_subscribe_track_groupId
search.consumer.kafka.trajectory.topic=trs_subscribe_track
search.consumer.kafka.trajectory.autoOffsetReset=earliest
# 20241108 - 张阳 - 技侦赋能预警类型字典(仅德阳)
search.jz.warn.type.dict={"YJ_QT_MBJJ":"人员聚集预警"}
```

## 新增索引(theme_jzxxb)

```json
{
  "settings": {
    "number_of_shards": 10,
    "number_of_replicas": 1
  },
  "mappings": {
    "properties": {
      "yjsj": {
        "format": "yyyy-MM-dd HH:mm:ss",
        "type": "date",
        "meta": {
          "comment": "预警时间"
        }
      },
      "tzzlx": {
        "type": "keyword",
        "meta": {
          "comment": "特征值类型"
        }
      },
      "tzzhm": {
        "type": "keyword",
        "meta": {
          "comment": "特征值号码"
        }
      },
      "fssj": {
        "format": "yyyy-MM-dd HH:mm:ss",
        "type": "date",
        "meta": {
          "comment": "发送时间"
        }
      },
      "content": {
        "type": "text",
        "meta": {
          "comment": "预警内容"
        }
      },
      "address": {
        "type": "keyword",
        "meta": {
          "comment": "地址"
        }
      },
      "groupName": {
        "type": "keyword",
        "meta": {
          "comment": "群体名称"
        }
      },
      "mtype": {
        "type": "keyword",
        "meta": {
          "comment": "类型"
        }
      },
      "rawContent": {
        "type": "text",
        "meta": {
          "comment": "技侦数据原始内容"
        }
      }
    }
  }
}
```

# 11.2

## 发版日志

- XMKFB-5011 【高新】超级检索，相同车牌号不同车牌种类显示的车辆档案轨迹信息一致
- XMKFB-5169 【高新】超级检索，针对相同车辆重复显示的车辆档案数据需进行合并
- 


## 索引字段变更（archive_relation）

```text
PUT archive_relation/_mapping
{
  "properties": {
    "obj_ext":{
      "type":"keyword",
      "meta": {
          "comment": "对象扩展信息，因对象种类不同而不同"
        }
    },
    "relation_obj_ext":{
      "type":"keyword",
      "meta": {
          "comment": "对象扩展信息，因对象种类不同而不同"
        }     
    }
  }
}
```

# 11.3

- XMKFB-5263[XMKFB-4918] 后 - 增加相关企业档案定义
- XMKFB-4918 高新-【检索】新增一个公司主题库
- XMKFB-5311 【德阳】任务，人员轨迹接口报错
- 

## 新增配置

```properties
# 20241119 - 褚川宝 - 全部搜索时可以搜索的索引，多个逗号分割
search.all.tableName=
# 20241121 - 肖豪 - 轨迹信息数据是否关联查询 - 德阳为false，其他项目为true
search.ai.gj.needRelevant=true
```

# 11.4

- XMKFB-5443[XMKFB-5330] 后-【高新】-档案更新切换交互中台方式
- XMKFB-5454[XMKFB-5330] 后-【高新】-线索池详情的人员信息需要展示手机号
- XMKFB-5509[XMKFB-5504] 后 - 优化轨迹信息查询跟图片反查行为
- XMKFB-5504 泸州-轨迹分析无法展示照片
- 

## 部署前置

```text
需要去kafka中创建Topic：archive_standard_data，如果不需要请修改search.producer.kafka.archive.zhongtai.enable=false
```

## 新增配置

```properties
# 20241128 - 褚川宝 - 中台消息发送配置
search.producer.kafka.archive.zhongtai.enable=true
search.producer.kafka.archive.zhongtai.topic=archive_standard_data
search.producer.kafka.archive.zhongtai.bootServer=${search.producer.kafka.archive.bootServer}
```

# 12.1

- XMKFB-5502 【高新】-【智能检索】车辆信息已注销标签逻辑优化
- XMKFB-5547[XMKFB-5526] 后 - 【高新】超级检索，支持通过群号和表名获取trsId
- XMKFB-5534 【高新】超级检索，使用标签或搜索框进行检索时应重置对应的条件
- 

## 新增配置

```properties
# 20241205 - 褚川宝 - 高新聊天数据表映射
search.chatGroup.table.mapping={"dwd_bf_group_member_info":"dwd_bf_info"}
```

# 12.2

- XMKFB-5617[XMKFB-5546] 后 - 协助前端调整
- XMKFB-5699[XMKFB-5579] 后-【高新】-线索池接口findOtherArchivesByPerson需要支持feign调用
- XMKFB-5697[XMKFB-5664] 后 - 优化案件跟身份证号识别
- 

## 新增配置

```properties
# 20241212 - 肖豪 - 检索数据全部设为待办限制数量
search.todo.add.maxSize=200
```

# 12.3

- XMKFB-5771[XMKFB-5546] 后 - 完善根据档案信息查询对应数据在待办列表中的情况
- XMKFB-5723 【高新】融合检索，部分轨迹照片没有URL地址但仍做了展示
- XMKFB-5730[XMKFB-5573] 后-【高新】-人档更新消息重复


# 12.4

- XMKFB-5887 【高新】多轨分析里人员轨迹较多后地图页面加载显示极为卡顿
- XMKFB-5906 后 - 优化检索监控中昨日数据的数据统计
- XMKFB-5916[XMKFB-5261] 后 - AI总结接口改为调度拓天大模型应用
- XMKFB-6105[XMKFB-6021] 云哨多轨分析接口需要保存日志

## 新增配置

```properties
# 20241223 - 肖豪 - 多轨分析限制获取轨迹数据量 - 高新
search.multiTrack.gjList.maxPageSize=200
# 20241226 - 褚川宝 - AI相关配置
# AI总结
search.ai.config.summary.host=
search.ai.config.summary.appId=
search.ai.config.summary.userName=
search.ai.config.summary.token=
search.ai.config.summary.defaultTenantId=
search.ai.config.summary.needPrintLog=true
# AI分析
search.ai.config.analysis.host=
search.ai.config.analysis.appId=
search.ai.config.analysis.userName=
search.ai.config.analysis.token=
search.ai.config.analysis.defaultTenantId=
search.ai.config.analysis.needPrintLog=true
```

# 13.1

- XMKFB-6115[XMKFB-6017] 后 - “全部”tab项中支持搜索业务档内容
- XMKFB-6164[XMKFB-6017] 后 - 完善风险档案查询
- XMKFB-6153[XMKFB-6125] 后 - 增加点位汇总接口
- XMKFB-6260 后 - 索引字段描述全部变成了英文

## 部署前置

```properties
# 调整配置项（search-new.properties）
# 以下高新跟泸州已调整
search.all.tableName=archive_person,archive_vehicle,archive_case,archive_phone,archive_case,archive_clue,archive_jq,archive_xt,archive_fengxian
# 监控配置
com.trs.synchronized.monitor.task.cron=15 */10 * * * ?
com.trs.synchronized.monitor.task.enable=true
```

## 索引调整

```text
1. 增加风险档案索引
2. 线索档案表结构已新增 zjhm 和 sjhm 字段,字段类型都是keyword
3. 警情档案表的xgr字段中增加sjh数组，属性类型是keyword
```

# 13.2

- XMKFB-6125 多轨分析增加点位模式-优化

## 新增配置

```properties
# 20250108 - 褚川宝 - 点位聚集优化
search.multiTrack.gjList.topN=100
search.multiTrack.gjList.minGroupNum=3
search.aiAnalysis.geoHash.precise=6
```

# 13.4

- XMKFB-6467 泸州-检索监控异常
- XMKFB-6650[XMKFB-6481] 后 - 优化排序字段
- XMKFB-6714 后 - 增加刷新IMSI到手机档案的行为
- XMKFB-6734[XMKFB-5261] 后 - 优化总结内容的响应

## 索引字段变更（archive_phone）

```text
PUT archive_phone/_mapping
{
  "properties": {
    "macs": {
      "type": "nested",
      "properties": {
        "mac": {
          "type": "keyword",
          "meta": {
            "comment": "mac"
          },
          "copy_to": "content"
        },
        "reason": {
          "type": "keyword",
          "meta": {
            "comment": "说明"
          }
        }
      }
    },
    "imsi": {
      "type": "keyword",
      "meta": {
        "comment": "imsi"
      }
    },
    "imei": {
      "type": "keyword",
      "meta": {
        "comment": "imei"
      }
    },
    "mac": {
      "type": "keyword",
      "meta": {
        "comment": "mac"
      }
    }
  }
}
```

## 14.2

- XMKFB-6743[XMKFB-6491] 后 - 调整相关接口适配新的档案
- XMKFB-6783[XMKFB-6685] 后 - 提供手机转码时间查询接口
- XMKFB-6745[XMKFB-6483] 后 - 扩展车档新增关联关系解析

## 14.3

- XMKFB-6827[XMKFB-6826] 【后】—— 提供配置获取接口
- XMKFB-6766[XMKFB-6685] 后 - 增加对应智能分析标签解析行为
- XMKFB-6847 后 - 优化检索逻辑
- XMKFB-6930[XMKFB-6921] 后 - 优化AI响应内容的返回
- XMKFB-6950[XMKFB-6941] 后 - 优化智能分析脑图数据查询
- XMKFB-6951[XMKFB-6941] 后 - 优化时序图谱数据查询
- XMKFB-6994 全文检索，人员档案里的轨迹分析数据会显示社会关系、家庭成员数据
- 

## 14.4

- XMKFB-7030 高新-【检索】人员的AI检索接口封装一个给拓天一体化平台调用
- XMKFB-7055 【泸州】融合检索，手机和车辆档案未显示警情类型
- XMKFB-6956 检索-AI搜索能够对标签进行识别检索
- XMKFB-7106 【泸州】融合检索，人员档案的相关车辆增加号牌类型
- XMKFB-7213 【泸州】全文检索，同一车牌号不同车辆类型的时序图谱统计数字显示一样

## 15.2

- XMKFB-7260[XMKFB-6889] 后 - 优化全文检索字段构造
- XMKFB-7155 AI检索-回答优化
- XMKFB-7253[XMKFB-7251] 后 - 显示权限功能开发
- XMKFB-7188[XMKFB-7294] 【泸州】融合检索，警情档案未显示反馈信息
- 

## 15.3

- XMKFB-7346[XMKFB-7331] 后 - 完善AI信息转换跟回吐查询接口
- XMKFB-7333[XMKFB-7096] 【泸州】手机档案轨迹概况未展示地址的值
- XMKFB-7428 【自贡】【数据权限】搜索权限配置报错
- XMKFB-7470 全文检索，没有标签搜索权限时能够检索出人员数据
- 

## 15.4

- XMKFB-7390 高新GA-超级检索的人员档案详情页，人员关系图谱中新增预览图片
- XMKFB-7610[XMKFB-7593] 后 - 返回列表中标签的颜色
- XMKFB-7597 【高新】全文检索，搜索出来的人员姓名显示为了”未知“
- 

## 16.1

- XMKFB-7639 【高新】全文检索，人物画像里社会关系图片详情列表中抓拍时间显示为了时间戳
- XMKFB-7638 【高新】全文检索，社会关系和相关车辆里的图片详情数据需倒序排列显示
- XMKFB-7668[XMKFB-7637] 后 - 按照测试要求将时间放到证件号码下面
- XMKFB-7679[XMKFB-7675] 后 - 增加对应实体属性的返回

## 16.2

- XMKFB-7664 后 - 优化基础库导入解析接口
- XMKFB-7641 【高新】全文检索，人物画像里社会关系人员卡片排序方式的优化与调整
- XMKFB-7752 【广安】全文检索，案件档案画像里涉及警情重复显示了相同手机号

### 新增配置

```properties
# 拥有一体化大模型的环境，同时支持检索的都可以添加，根据不同的环境来进行配置
search.ai.config.tableImport.enable=true
search.ai.config.tableImport.host=https://llmys.trscd.com.cn
search.ai.config.tableImport.appId=864
search.ai.config.tableImport.userName=ga-demo
search.ai.config.tableImport.token=430f3a53171a4696
search.ai.config.tableImport.defaultTenantId=37
search.ai.config.tableImport.needPrintLog=true
search.ai.config.tableImport.callTimeout=300
search.ai.config.tableImport.connectTimeout=300
```

## 16.3

- XMKFB-7756 全文检索，精确搜索页面使用模型无效

## 16.4

- XMKFB-8058 【自贡】【系统管理】【验收】布控白名单

## 17.1

- XMKFB-8249[XMKFB-8130] 后 - 标注结果推送kafka
- 

## 新增配置(高新common-third-api.properties)

```properties
# 20250508 - 张阳 - 警情AI分析消息发送配置
search.producer.kafka.sentiment.ai.answer.enable=true
search.producer.kafka.sentiment.ai.answer.topic=nlp_chat_data_import_topic
# 项目环境需要根据实际使用情况调整此地址
search.producer.kafka.sentiment.ai.answer.bootServer=**************:9092
# 20250508 - 张阳 - 警情ai分析相关配置
search.sentiment.ai.analysis.host=http://*************:6556
search.sentiment.ai.analysis.appId=902
search.sentiment.ai.analysis.userId=1703
# 大模型响应超时时间（秒）
search.sentiment.ai.analysis.timeout=120
search.sentiment.ai.analysis.needPrintLog=true
```

## 17.1

- XMKFB-8363 【高新】全文检索，后台配置的红名单数据没有生效

## 17.2

- XMKFB-8502 高新-【检索】商汤人像比对token过期时间调整

## 17.3

```SQL
-- 后 - 通过SQL清除一些不必要的标签(这个标签属于高新，所以高新不用处理)
DELETE FROM tb_search_label WHERE `nature` = '户籍' AND `archives_type` = 'person' AND `name` IN ('疑似居住在高新区','紫云籍人员');
DELETE FROM tb_search_label WHERE `nature` = '行为特征' AND `archives_type` = 'person' AND `name` IN ('常去muchroom酒吧','去过muchroom酒吧','火车南站抵达蓉','高新区活跃','去过恒大君临阁','中和辖区活跃','桂溪辖区活跃','芳草辖区活跃','合作辖区活跃','和平辖区活跃','西园辖区活跃','石羊辖区活跃','肖家河辖区活跃','新川辖区活跃','新会展辖区活跃','新益州辖区活跃','常去雅嘉御公寓楼','去过去雅嘉御公寓楼','常去喾悦','去过喾悦','常去万骊红庭','去过万骊红庭','常去明珠春天','去过明珠春天','常去神仙树','去过神仙树');
```

## 18.1

- XMKFB-8621[XMKFB-8613] 后 - 优化日志
- XMKFB-8613 广安-检索日志-调整
