package com.trs.police.search.message.websocket;

import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.message.Channel;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.search.config.WebSocketHttpSessionConfigurator;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.service.impl.SearchCountSocketService;
import com.trs.police.search.panorama.vo.NewSearchMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;

/**
 * @ClassName WebSocketHandler
 * @Description 检索方面的socket通讯
 * <AUTHOR>
 * @Date 2023/9/28 11:03
 **/
@Slf4j
@Component
@ServerEndpoint(
        value = "/websocket/{system}/{module}/{channelName}/{userId}/{deptId}",
        configurator = WebSocketHttpSessionConfigurator.class
)
public class SearchWebSocketHandler {

    @Resource
    private SearchCountSocketService countSocketService;

    /**
     * 建立连接回调
     *
     * @param system      当前系统
     * @param module      当前模块
     * @param channelName 通道名称：eg：message(消息)
     * @param session     会话
     * @param userId      用户id
     * @param deptId      用户部门id
     */
    @OnOpen
    public void onOpen(
            Session session,
            @PathParam(value = "system") String system,
            @PathParam(value = "module") String module,
            @PathParam(value = "channelName") String channelName,
            @PathParam(value = "userId") Long userId,
            @PathParam(value = "deptId") Long deptId
    ) {
        Channel channel = new Channel(system, module, channelName, null);
        session.setMaxTextMessageBufferSize(16 * 1024);
        SessionManager.updateSessionManger(new UserDeptVO(userId, deptId), channel, session);
    }

    /**
     * 建立连接回调
     *
     * @param message     发送的消息
     * @param system      当前系统
     * @param module      当前模块
     * @param channelName 通道名称：eg：message(消息)
     * @param session     session
     * @param userId      用户id
     */
    @OnMessage()
    public void onMessage(
            String message,
            @PathParam(value = "system") String system,
            @PathParam(value = "module") String module,
            @PathParam(value = "channelName") String channelName,
            @PathParam(value = "userId") Long userId,
            Session session
    ) {
        switch (module) {
            case SearchConstant.PANORAMA:
                processPanoramaMessage(session, message);
                break;
            case SearchConstant.AI:
            default:
                session.getAsyncRemote().sendText(message);
        }
    }

    private void processPanoramaMessage(Session session, String message) {
        NewSearchMessageVO request = JsonUtil.parseObject(message, NewSearchMessageVO.class);
        if (request == null) {
            log.error("检索请求处理失败！message：{}", message);
            return;
        }
        final SearchCountSocketService countSocketService = BeanUtil.getBean(SearchCountSocketService.class);
        countSocketService.searchCountTotal2(session, request.getSender(), request.getSearchValue(), request.getArchivesType());
    }

    /**
     * 建立连接回调
     *
     * @param system      当前系统
     * @param module      当前模块
     * @param channelName 通道名称：eg：message(消息)
     * @param userId      用户id
     * @param deptId      部门id
     * @param session     当前链接session
     */
    @OnClose
    public void onClose(
            @PathParam(value = "system") String system,
            @PathParam(value = "module") String module,
            @PathParam(value = "channelName") String channelName,
            @PathParam(value = "userId") Long userId,
            @PathParam(value = "deptId") Long deptId,
            Session session
    ) throws IOException {
        Channel channel = new Channel(system, module, channelName, null);
        session.close();
        SessionManager sessionManager = BeanUtil.getBean(SessionManager.class);
        sessionManager.closeCurrentUserSession(new UserDeptVO(userId, deptId), channel);
        log.info("用户[{}:{}]关闭连接,sessionId:{},channel:{}", userId, deptId, session.getId(), channel);
    }


    /**
     * 出现异常回调
     *
     * @param throwable 异常信息
     */
    @OnError
    public void onError(Throwable throwable) {
        log.error("WebSocket 出现错误: {} ", throwable.getMessage(), throwable);
    }

}
