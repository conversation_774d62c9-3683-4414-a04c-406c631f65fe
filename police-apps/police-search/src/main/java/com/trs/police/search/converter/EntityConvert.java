package com.trs.police.search.converter;

import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.entity.ThemeGjxxbEntity;
import com.trs.police.common.openfeign.starter.DTO.AiGjDTO;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.common.openfeign.starter.vo.RenWuJiZhenFuNengVo;
import com.trs.police.common.openfeign.starter.vo.ThemeGjxxVO;
import com.trs.police.search.domain.entity.*;
import com.trs.police.search.domain.vo.CaseSuspectVO;
import com.trs.police.search.domain.vo.ChatMsgVo;
import com.trs.police.search.domain.vo.DataSearchAuthorizationInfoVO;
import com.trs.police.search.panorama.dto.SearchTableFieldDTO;
import com.trs.police.search.panorama.util.SearchLogNewUtils;
import com.trs.police.search.panorama.vo.AiLabelVo;
import com.trs.police.search.panorama.vo.SearchFieldConfigVO;
import com.trs.police.search.panorama.vo.SearchLogVO;
import com.trs.police.search.panorama.vo.SearchSchemaVO;
import com.trs.police.search.traffic.police.dto.KafkaYiTuMessageDTO;
import com.trs.police.search.traffic.police.dto.WarnConfigDTO;
import com.trs.police.search.traffic.police.entity.TrafficJjwfjlEntity;
import com.trs.police.search.traffic.police.vo.DataListResult;
import com.trs.police.search.traffic.police.vo.DataListVO;
import com.trs.police.search.traffic.police.vo.WarnConfigVO;
import com.trs.police.search.traffic.police.vo.WfjlAndCountVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/8/21 17:53
 * @since 1.0
 */
@Mapper(
        componentModel = "spring",
        imports = {
                SearchLogNewUtils.class
        }
)
public interface EntityConvert {

    EntityConvert INSTANCE = Mappers.getMapper(EntityConvert.class);

    /**
     * voToVo<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/12 15:55
     */
    @Mapping(target = "warnTime", ignore = true)
    @Mapping(target = "assignTime", ignore = true)
    @Mapping(target = "dealTime", ignore = true)
    @Mapping(target = "exportTime", ignore = true)
    @Mapping(target = "captureTimestamp", ignore = true)
    WfjlAndCountVO voToVo(DataListResult vo);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/12 15:47
     */
    @Mapping(source = "tzzhm", target = "recordId")
    ThemeGjxxVO entityToVo(ThemeGjxxbEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/14 10:40
     */
    @Mapping(target = "searchType", ignore = true)
    SearchFieldConfigVO entityToVo(SearchConfigEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/12 15:49
     */
    @Mapping(target = "dealTime", ignore = true)
    @Mapping(target = "exportTime", ignore = true)
    @Mapping(target = "assignTime", ignore = true)
    @Mapping(target = "warnTime", ignore = true)
    @Mapping(target = "captureTimestamp", ignore = true)
    DataListVO entityToVo(TrafficJjwfjlEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/21 17:54
     */
    ChatMsgVo entityToVo(BfInfoEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/21 18:02
     */
    DataSearchAuthorizationInfoVO entityToVo(DataSearchAuthorizationInfoEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/21 18:03
     */
    CaseSuspectVO entityToVo(CaseSuspectEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/14 10:52
     */
    @Mapping(target = "warnTime", source = "yjsj")
    @Mapping(target = "sendTime", source = "fssj")
    RenWuJiZhenFuNengVo entityToVo(ThemeJzxxbEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/3 15:17
     */
    @Mapping(target = "displayFields", ignore = true)
    @Mapping(target = "relatedArchives", ignore = true)
    @Mapping(target = "specialNatureList", ignore = true)
    SearchSchemaVO entityToVo(SearchSchemaEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/3 18:00
     */
    @Mapping(target = "operateModule", expression = "java(SearchLogNewUtils.getModuleName(entity.getOperateModule()))")
    @Mapping(target = "operateType", expression = "java(SearchLogNewUtils.getOperateDesc(entity.getOperateType()))")
    @Mapping(target = "userShowName", expression = "java(entity.getTrueName() + \"(\" + entity.getUsername() + \")\")")
    @Mapping(target = "operateTime", dateFormat = TimeUtils.YYYYMMDD_HHMMSS)
    SearchLogVO entityToVo(SearchLogEntityNew entity);

    /**
     * dtoToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/21 18:05
     */
    WarnConfigVO dtoToVo(WarnConfigDTO entity);

    /**
     * dtoToEntity<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/12 14:11
     */
    SearchConfigEntity dtoToEntity(SearchTableFieldDTO dto);

    /**
     * dtoToEntity<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/12 15:52
     */
    @Mapping(target = "captureTimestamp", ignore = true)
    TrafficJjwfjlEntity dtoToEntity(KafkaYiTuMessageDTO dto);

    /**
     * copy<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/12 15:42
     */
    BaseArchivesSearchDTO copy(BaseArchivesSearchDTO dto);

    /**
     * copy<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/12 15:45
     */
    AiGjDTO copy(AiGjDTO dto);

    /**
     * copy<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/14 11:00
     */
    ThemeJzxxbEntity copy(ThemeJzxxbEntity entity);

    /**
     * copy<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/3 10:44
     */
    @Mapping(target = "ext", ignore = true)
    @Mapping(target = "objExt", ignore = true)
    @Mapping(target = "relationObjExt", ignore = true)
    AiLabelVo copy(AiLabelVo entity);
}
