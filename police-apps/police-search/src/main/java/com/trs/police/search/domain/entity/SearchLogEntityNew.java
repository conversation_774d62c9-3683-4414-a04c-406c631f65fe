package com.trs.police.search.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName SearchLogEntityNew
 * @Description 日志对象实体
 * <AUTHOR>
 * @Date 2023/10/23 16:30
 **/
@Data
@TableName("t_search_log_new")
@AllArgsConstructor
@NoArgsConstructor
public class SearchLogEntityNew implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    @TableField
    private Long userId;

    /**
     * 用户名
     */
    @TableField
    private String username;

    @TableField(exist = false)
    private String trueName;

    /**
     * 所属组织id
     */
    @TableField
    private Long deptId;

    /**
     * 所属组织名称
     */
    @TableField
    private String deptName;

    /**
     * 部门路径
     */
    @TableField
    private String deptIdPath;

    /**
     * ip地址
     */
    @TableField
    private String ipAddress;

    /**
     * 操作时间
     */
    @TableField
    private LocalDateTime operateTime;

    /**
     * 功能模块
     */
    @TableField
    private String operateModule;

    /**
     * 行为类型
     */
    @TableField
    private String operateType;

    /**
     * 登记事由
     */
    @TableField
    private String djsy;

    /**
     * 行为内容
     */
    @TableField
    private String operateContent;

}
