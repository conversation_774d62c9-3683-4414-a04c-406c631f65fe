package com.trs.police.search.panorama.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.RemoteAddrUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.openfeign.starter.DTO.SearchLogDTO;
import com.trs.police.search.config.SearchThreadPoolConfigV2;
import com.trs.police.search.converter.EntityConvert;
import com.trs.police.search.domain.entity.SearchLogEntityNew;
import com.trs.police.search.mapper.SearchLogNewMapper;
import com.trs.police.search.panorama.constant.enums.OperateTypeEnum;
import com.trs.police.search.panorama.service.BaseLogPushService;
import com.trs.police.search.panorama.service.ISearchLogNewService;
import com.trs.police.search.panorama.util.SearchLogNewUtils;
import com.trs.police.search.panorama.vo.SearchLogVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 新版检索日志业务类
 */
@Slf4j
@Service
public class SearchLogNewServiceImpl implements ISearchLogNewService {

    @Resource
    private SearchLogNewMapper logMapper;

    @Override
    public void doLog(SearchLogDTO dto) throws ServiceException {
        final CurrentUser user = AuthHelper.getNotNullUser();
        this.doLog(dto, user);
    }

    @Override
    public void doLog(SearchLogDTO dto, CurrentUser user) throws ServiceException {
        doLog(dto, user, RemoteAddrUtil.getIp());
    }

    @Override
    public void doLog(SearchLogDTO dto, CurrentUser user, String ip) throws ServiceException {
        SearchLogEntityNew entity = new SearchLogEntityNew();
        entity.setOperateType(dto.getOperateType());
        entity.setOperateModule(dto.getOperateModule());
        entity.setOperateContent(dto.getOperateContent());
        entity.setDjsy(dto.getDjsy());
        entity.setOperateTime(LocalDateTime.now());
        entity.setIpAddress(StringUtils.showEmpty(ip, "0.0.0.0"));
        entity.setUserId(user.getId());
        entity.setUsername(user.getUsername());
        entity.setDeptId(user.getDeptId());
        entity.setDeptName(user.getDept().getShortName());
        entity.setDeptIdPath(buildDeptIdPath(user.getDept()));
        logMapper.insert(entity);
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始推送日志[{}]", entity.getId());
                BaseLogPushService.findPushServices().forEach(push -> {
                    log.info("开始推送日志[{}]到[{}]", entity.getId(), push.desc());
                    Integer code = push.pushLog(entity);
                    log.info("完成推送日志[{}]到[{}],Code=[{}]", entity.getId(), push.desc(), code);
                });
                log.info("完成推送日志[{}]", entity.getId());
            } catch (Exception e) {
                log.error("推送日志[{}]失败！", entity.getId(), e);
            }
        }, SearchThreadPoolConfigV2.t);
    }

    private String buildDeptIdPath(DeptDto dept) {
        final String idPath = dept.getPath();
        if (StringUtils.isNullOrEmpty(idPath)) {
            return "1";
        }
        if (idPath.startsWith("-")) {
            return String.format("1%s%s", idPath, dept.getId());
        } else {
            return idPath + dept.getId();
        }
    }

    @Override
    public IPage<SearchLogVO> pageList(SearchLogDTO dto) throws ServiceException {
        dto.isValid();
        if (StringUtils.isNullOrEmpty(dto.getDeptInfo())) {
            final CurrentUser user = AuthHelper.getNotNullUser();
            dto.setDeptInfo(buildDeptIdPath(user.getDept()));
        }
        IPage<SearchLogEntityNew> page = logMapper.pageList(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);
        return page.convert(EntityConvert.INSTANCE::entityToVo);
    }

    @Override
    public List<KeyValueTypeVO> operateTypes() {
        List<KeyValueTypeVO> result = new ArrayList<>();
        for (OperateTypeEnum value : OperateTypeEnum.values()) {
            KeyValueTypeVO vo = new KeyValueTypeVO();
            vo.setKey(value.getType());
            vo.setValue(value.getDesc());
            result.add(vo);
        }
        return result;
    }

    @Override
    public void export(HttpServletResponse response, SearchLogDTO dto) throws ServiceException {
        try {
            String fileName = URLEncoder.encode("SearchLog", StandardCharsets.UTF_8) + "_"
                    + LocalDateTime.now().format(TimeUtil.WARNING_MESSAGE_PATTERN);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            final IPage<SearchLogVO> pageList = pageList(dto);
            final List<SearchLogVO> list = pageList.getRecords();
            int index = 1;
            for (SearchLogVO searchLogVO : list) {
                searchLogVO.setIndex(index);
                index++;
            }
            EasyExcel.write(response.getOutputStream())
                    .head(SearchLogVO.class)
                    .sheet("检索日志")
                    .doWrite(list);
        } catch (ServiceException | IOException e) {
            throw new ServiceException("Excel导出异常！", e);
        }
    }

    @Override
    public List<KeyValueTypeVO> moduleNames() {
        final Map<String, String> map = SearchLogNewUtils.getTypeAndModuleMap();
        List<KeyValueTypeVO> result = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            KeyValueTypeVO vo = new KeyValueTypeVO();
            vo.setKey(entry.getKey());
            vo.setValue(entry.getValue());
            result.add(vo);
        }
        return result;
    }
}
