package com.trs.police.search.filter;

import com.trs.police.common.core.utils.RemoteAddrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/3 18:42
 * @since 1.0
 */
@Component
@Order(1)
@Slf4j
@WebFilter(urlPatterns = "/*", filterName = "ipAddressPrintFilter")
public class IpAddressPrintFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (request instanceof HttpServletRequest) {
            HttpServletRequest req = (HttpServletRequest) request;
            String address = RemoteAddrUtil.getRemoteAddress(req);
            HttpSession session = req.getSession();
            session.setAttribute("clientIpAddress", address);
        }
        chain.doFilter(request, response);
    }
}
