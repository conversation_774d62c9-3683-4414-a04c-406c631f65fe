package com.trs.police.search.panorama.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.trs.common.pojo.BaseVO;
import lombok.Data;

/**
 * @ClassName LogManagementVo
 * @Description 日志相关VO
 * <AUTHOR>
 * @Date 2023/10/24 15:20
 **/
@Data
public class SearchLogVO extends BaseVO {

    /**
     * 数据id
     */
    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private String trueName;

    /**
     * 用户名
     */
    @ExcelIgnore
    private String username;

    /**
     * 导出的序号
     */
    @ExcelProperty(value = "序号", index = 0)
    private Integer index;

    @ExcelProperty(value = "访问用户", index = 1)
    private String userShowName;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "所属组织", index = 2)
    private String deptName;

    /**
     * ip地址
     */
    @ExcelProperty(value = "IP地址", index = 3)
    private String ipAddress;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "时间", index = 4)
    private String operateTime;

    /**
     * 操作模块
     */
    @ExcelProperty(value = "功能模块", index = 5)
    private String operateModule;

    /**
     * 操作类型
     */
    @ExcelProperty(value = "行为类型", index = 6)
    private String operateType;

    /**
     * 登记事由
     */
    @ExcelProperty(value = "登记事由", index = 7)
    private String djsy;

    /**
     * 操作内容
     */
    @ExcelProperty(value = "行为内容", index = 8)
    private String operateContent;

}
