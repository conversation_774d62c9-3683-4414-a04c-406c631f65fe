<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.search.mapper.SearchLogNewMapper">

    <select id="pageList" resultType="com.trs.police.search.domain.entity.SearchLogEntityNew">
        SELECT
        u.real_name as trueName,
        log.*
        FROM t_search_log_new log left join t_user u on log.user_id =u.id
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND log.operate_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND log.operate_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.deptInfo != null and dto.deptInfo != ''">
                AND
                <foreach collection="dto.deptInfo.split(',')" item="path" open="(" separator="OR" close=")">
                    log.dept_id_path LIKE CONCAT('', #{path}, '%' )
                </foreach>
            </if>
            <if test="dto.operateModule != null and dto.operateModule != ''">
                AND log.operate_module = #{dto.operateModule}
            </if>
            <if test="dto.operateType != null and dto.operateType != ''">
                AND log.operate_type = #{dto.operateType}
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                <choose>
                    <when test="dto.keywordType == 'user'">
                        AND (
                        log.username LIKE CONCAT('%', #{dto.keyword}, '%' )
                        OR
                        u.real_name LIKE CONCAT('%', #{dto.keyword}, '%' )
                        )
                    </when>
                    <when test="dto.keywordType == 'ip'">
                        AND log.ip_address LIKE CONCAT('%', #{dto.keyword}, '%' )
                    </when>
                    <otherwise>
                        AND CONCAT(
                        IFNULL(u.real_name,''),
                        IFNULL(log.username,''),
                        IFNULL(log.dept_name,''),
                        IFNULL(log.ip_address,''),
                        IFNULL(log.operate_module,''),
                        IFNULL(log.operate_type,''),
                        IFNULL(log.operate_content,'')
                        ) LIKE CONCAT('%',#{dto.keyword},'%')
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="dto.orderField == 'TIME'">
                log.operate_time
            </when>
            <otherwise>
                log.operate_time
            </otherwise>
        </choose>
        ${dto.orderType}
    </select>

</mapper>