# fight
## 18.1
-XMKFB-8564 后-【云哨】- fight 模块报错问题处理

## 17.4
- XMKFB-8570 【自贡】【协作】技侦协作审批表单替换
- XMKFB-8368 后-【广安】- 警务协作-紧急警情调整

## RC20250527
- XMKFB-8538[XMKFB-8530] 后-【自贡】- 排查警务协作统计数量不一致问题

## 17.3
- XMKFB-8397 【广安】5110热线流转，查看工单详情页报错
- XMKFB-8401 【广安】5110工单，添加工单成功后列表会会重复显示相同的工单数据

## 17.2

- XMKFB-8261 后-【广安】- 紧急警情通知模型运行机制优化
- XMKFB-8498[XMKFB-7097] 【泸州】海致个人极端接口变更

## RC20250514

- XMKFB-8354 【自贡】【警务协作】刑侦民警发起协作时报错

## 17.1

- XMKFB-8192 【自贡】警务协作，资金、反电诈协作的审批流程需做调整
- XMKFB-8190 【自贡】警务协作，派出所发起视侦协作到区县情指的审批流程有误
- XMKFB-8191 【自贡】警务协作，区县警种单位发起视侦协作到市局情指的审批流程有误
- XMKFB-8193 【自贡】警务协作，协查通报协作没有配置审批流程

## RC20250507

- XMKFB-8258 【德阳】合成作战bug+协作bug

## 16.5

- XMKFB-7995 紧急警情协作刷新反馈信息后的toast提示优化
- XMKFB-8095 编辑修改协作的发起单位并重新提交以后审批流程未更新
- XMKFB-8132 警务协作，紧急警情协作下载模板文件的名称需做优化调整
- XMKFB-8020 【德阳】技侦协作流程配置

## 16.4

- XMKFB-7995【自贡】警务协作，紧急警情协作刷新反馈信息后的toast提示需做优化调整
- XMKFB-7921 后-【泸州】- 合成作战我的合成慢问题排查

# RC20250425

- XMKFB-8108 后-【广安】- 协作自动审批、指令单/审批表映射优化

## 16.3

- XMKFB-7763 【自贡】【警务协作】技侦协作表单替换
- XMKFB-7671 【省厅协作】政务微信建群对接
- XMKFB-7713 【自贡】【协作】【紧急警情查询】紧急警情优化
- XMKFB-7850 后-【自贡】- 紧急警情协作-调用JZ接口需生成指令单推送过去
- 自贡紧急警情倒出添加个人签章

## 16.3

- XMKFB-7713 【自贡】【协作】【紧急警情查询】紧急警情优化
- XMKFB-7845 【自贡】区县、派出所发起警务协作后的审批流程缺少分管局领导审批
- XMKFB-7846 【自贡】市局科通发起美团协作的审批流程有误
- XMKFB-7847 【自贡】警务协作，再次提交协作报错

## 16.2

- XMKFB-7647 警务协作，警务协作详情里审批信息展示的全部人员单位信息有误
- XMKFB-7695 【自贡】紧急警情，刷新反馈显示的刷新计时逻辑有误
- XMKFB-7435 【省厅协作】警务协作，重新编辑保存网安协作后需要更新反馈时限

# RC20250411

- XMKFB-7716 【自贡】【协作】【美团协作】美团协作功能、流程配置

# RC20250409

- XMKFB-7714 【自贡】【管控】【临控】临控布控审批流程调整
- XMKFB-7711 合 -【广安】- 紧急警情JZ 反馈处增加查看地图按钮

## RC20250408

- XMKFB-7710 合 -【广安】- 紧急警情协作优化

## RC20250407

- XMKFB-7649 【自贡】警务协作，大数据警务协作审批流程调整

## 16.1

- XMKFB-7584 【省厅协作】技侦协作审批表中部分字段名称的调整
- XMKFB-7667【省厅情指】所有警情数据均无法查看音频

## 15.4

- XMKFB-7462 后-网安协作使用目的修改
- XMKFB-7437 【省厅协作】警务协作，网安协作审批表展示内容的优化与调整
- XMKFB-7440 【省厅协作】警务协作，当审批流程里的某个单位缺少审批人时会直接报错
- XMKFB-7500 后-【广安】- 紧急警情JZ反馈数据适配kafka
- XMKFB-7514[XMKFB-7419] 警务协作-紧急警情协作-添加协理人接口支持编辑
- XMKFB-7503 【省厅情指】合成作战+大屏的bug
- 【省厅协作】警务协作，重新编辑保存网安协作后需要更新反馈时限

## RC20250326

- XMKFB-7501 后-【广安】- JZ 数据推送编号生成规则调整

## RC20250325

- XMKFB-7497 合-【省厅情指】- 省厅情指作战消息bug

## 15.3

- 协作返回typeCode字段保持全局一致
- XMKFB-7326 【省厅协作】警务协作，技侦协作的审批表仅显示了一页
- XMKFB-7408【自贡】警务协作，紧急警情协作里显示的协里人数据有误
- XMKFB-7268 合 -【广安】- 紧急警情表单及功能优化
- XMKFB-7399 【自贡】警务协作，新增且审批通过的紧急警情没有推送到JZ平台
- XMKFB-7343 协同作战，在编辑作战页面将新添加的人员设为主办人、协办人无效
- XMKFB-7107[XMKFB-7079] 【省厅情指】指挥大屏—协同作战，作战详情增加字段判断紧急作战

## RC20250319

- XMKFB-7351 自贡GA-协作详情页的审批流程，目前只看得到一个审批人

## RC20250318

- XMKFB-7291 后-【省厅协作】- 线索、协作模块短信平台对接

## 15.2

- XMKFB-7221[XMKFB-7167] 后-【省厅协作】-网安协作审批表
- XMKFB-7085 后-【自贡】- 紧急警情查询修改
- XMKFB-7220[XMKFB-7167] 后-【省厅协作】-网安协作表单支持
- XMKFB-7255 德阳-协作-技侦协作表单调整

## 15.1

- XMKFB-7107[XMKFB-7079] 【省厅情指】指挥大屏—协同作战，作战详情增加字段判断紧急作战
- XMKFB-7103【省厅情指】协同作战，选择紧急作战类别以后的人员拉取逻辑有误

### 新增配置(fight-new.properties)

```properties
## 泸州已添加
# 20250306 - 海致模型同步配置
fight.sync.haizhi.person.warn.enable=false
fight.sync.haizhi.person.warn.url=
fight.sync.haizhi.person.warn.token=
fight.sync.haizhi.person.warn.defaultClueType=涉案
fight.sync.haizhi.person.warn.userId=
fight.sync.haizhi.person.warn.deptId=
fight.sync.haizhi.person.warn.cron=0 10 6,12 * * ?
```

## RC20250306

- XMKFB-7165 省厅GA-科X的协作功能，JZ审批单模板生成样式调整
- XMKFB-7097 后-【泸州】- 海致“个人极端模型线索”对接，自动输出到线索池中

## 14.3

- XMKFB-6822 警务协作，发起警务协作时切换发起单位后的审核流程需按照发起单位来调整
- XMKFB-6853 省厅情指-协同作战PC+大屏功能和页面调整

## 14.2

- XMKFB-4856[XMKFB-4824] 后-【自贡】-警务协作-紧急警情列表/细览页更改
- XMKFB-6740 【省厅情指】协同作战，作战类别选择紧急作战以后拉取的今日值班人员逻辑有误
- XMKFB-6718 【省厅协作】5110热线流转，热线流转创建成功后未显示在列表中
- XMKFB-6733 自贡-协作-紧急警情类协作相关调整
- XMKFB-6732 【省厅情指】协同作战，编辑作战时切换作战类别以后联动的参与人员有误

## 14.1

- XMKFB-6567 警务协作，我处理的列表中未过滤数据
- XMKFB-6677 【省厅情指】省厅单位发起作战仍需要审批
- XMKFB-6683 【省厅情指】协同作战，发起作战时添加人员后未走申请审批流程
- XMKFB-6509 后-【自贡】- 技侦接口验证

## RC20250207

-XMKFB-6718 【省厅协作】5110热线流转，热线流转创建成功后未显示在列表中

## RC20250124

-XMKFB-6641 【省厅情指】合成作战，我的作战列表报错

## 13.3

-XMKFB-6287 【省厅】协同作战大屏，警务协作统计数据显示为空
-XMKFB-6306 【省厅】协同作战大屏，警种支撑质效TOP10里的统计数据与实际统计数据不一致
-XMKFB-6294 【省厅】协同作战大屏—警务协作，协作分类统计数据无法显示出来
-XMKFB-6276 省厅-协同作战-添加参与人员时显示岗位和值班情况
-XMKFB-6376[XMKFB-6333] 后-【省厅情指】协作作战大屏时间轴
-XMKFB-6312[XMKFB-6308] 【省厅】协同作战大屏—协同作战近期作战功能
-XMKFB-6378 后-【省厅情指】合成作战参与人员状态增加

## 20250116

-XMKFB-6431 【省厅】警务协作，发起协作后未走审批流程

## 13.2

-XMKFB-5148 【泸州】5110工单协作详情里未反显警情、案件、涉案人员数据
-XMKFB-5912 后-【省厅情指】-作战审批流程优化/消息接口对接
-XMKFB-6218 后-【省厅情指】GA-协同作战的审批流程调整+紧急作战增加三个子类型
-XMKFB-6288 【省厅】协同作战大屏，大屏里显示的线索总数与实际系统里的线索数量不一致

## 13.1

- XMKFB-5911[XMKFB-5784] 后-【省厅情指】- 作战相关接口提供
- XMKFB-5910[XMKFB-5784] 后-【省厅情指】- 预案相关接口提供
- XMKFB-6079[XMKFB-6035] 后 - 增加战果数量的回写
- XMKFB-5911[XMKFB-5784] 后-【省厅情指】- 作战相关接口提供

## 12.4

- XMKFB-5148 【泸州】5110工单协作详情里未反显警情、案件、涉案人员数据
- XMKFB-5921 自贡-提示有协作需要审核，点击 立即查看 ，跳转的是合成页面。
- XMKFB-5779 后-【省厅】协同作战大屏开发
- XMKFB-5578 德阳GA-导出人员档案的模板，“管控责任人”应对应责任民警
- XMKFB-6057[XMKFB-6035] 详情接口返回相关协同/线索数量

## 12.3

- XMKFB-5711 【泸州】线索池，线索详情里的战果Tab下未显示相关协同里填写的战果数据
- XMKFB-5718 【泸州】线索池，战果里的案件编号和嫌疑人身份证号未作排重处理
- XMKFB-5714[XMKFB-5712] 后-合成作战提供人员批量移除接口
- XMKFB-5788 自贡-协作-新增警情（案件）来源文书照片
- XMKFB-5813[XMKFB-5363] 后-【省厅】合成作战大屏开发
- XMKFB-5831 【自贡】人力情报，创建或移交情报时审批流程没有审批人时应给出对应的toast提示
- XMKFB-5780 后-【省厅】合成作战大屏开发
- XMKFB-5779 后-【省厅】协同作战大屏开发
- XMKFB-5859 【自贡】人力情报，移交审批存在的问题

## 12.2

- XMKFB-5515 后-【省厅】合成作战大屏开发
- XMKFB-5513[XMKFB-5363] 后-【省厅】合成作战大屏开发
- XMKFB-5720 【泸州】线索池，实战服务中心删除线索失败
- XMKFB-5597[XMKFB-5542] 后-【泸州】- 协作/作战相关信息/检索接口提供
- XMKFB-5596[XMKFB-5542] 后-【泸州】- 完成协作/作战信息同步

## sql

如果需要推送作战和协作，需要推送方创建视图

```mysql
CREATE VIEW v_st_fight_user_relation AS
SELECT
	tfcur.*,
	(SELECT tu.idcard from t_user tu where tu.id = tfcur.create_user_id) as create_user_id_card,
	(SELECT code from t_dept td where td.id = tfcur.create_dept_id) as create_dept_code,
	(SELECT tu.idcard from t_user tu where tu.id = tfcur.update_user_id) as update_user_id_card,
	(SELECT code from t_dept td where td.id = tfcur.update_dept_id) as update_dept_code,
	(SELECT tu.idcard from t_user tu where tu.id = tfcur.user_id) as user_id_card,
	(SELECT code from t_dept td where td.id = tfcur.dept_id) as dept_code
from t_fight_composite_user_relation tfcur;

CREATE VIEW v_collaboration_user_relation AS
SELECT
	tfcur.*,
	(SELECT tu.idcard from t_user tu where tu.id = tfcur.create_user_id) as create_user_id_card,
	(SELECT code from t_dept td where td.id = tfcur.create_dept_id) as create_dept_code,
	(SELECT tu.idcard from t_user tu where tu.id = tfcur.update_user_id) as update_user_id_card,
	(SELECT code from t_dept td where td.id = tfcur.update_dept_id) as update_dept_code,
	(SELECT tu.idcard from t_user tu where tu.id = tfcur.related_user_id) as user_id_card,
	(SELECT code from t_dept td where td.id = tfcur.related_user_dept_id) as dept_code
from t_collaboration_user_relation tfcur;

CREATE  VIEW `t_collaboration_relation_view` AS
select
    `t`.`id` AS `collaboration_id`,
    `t`.`create_user_id` AS `create_user_id`,
    `t`.`create_dept_id` AS `create_dept_id`,
    `t`.`create_time` AS `create_time`,
    `t`.`update_user_id` AS `update_user_id`,
    `t`.`update_dept_id` AS `update_dept_id`,
    `t`.`update_time` AS `update_time`,
    (
    select
        `police`.`t_user`.`idcard`
    from
        `police`.`t_user`
    where
        (`police`.`t_user`.`id` = `t`.`create_user_id`)) AS `create_user_id_card`,
    (
    select
        `police`.`t_user`.`idcard`
    from
        `police`.`t_user`
    where
        (`police`.`t_user`.`id` = `t`.`update_user_id`)) AS `update_user_id_card`,
    (
    select
        `police`.`t_dept`.`code`
    from
        `police`.`t_dept`
    where
        (`police`.`t_dept`.`id` = `t`.`create_dept_id`)) AS `create_dept_code`,
    (
    select
        `police`.`t_dept`.`code`
    from
        `police`.`t_dept`
    where
        (`police`.`t_dept`.`id` = `t`.`update_dept_id`)) AS `update_dept_code`,
    `cr`.`case_event_id` AS `case_event_id`,
    (
    select
        `police`.`t_profile_case`.`asjbh`
    from
        `police`.`t_profile_case`
    where
        (`police`.`t_profile_case`.`id` = `cr`.`case_event_id`)) AS `case_event_code`,
    `sr`.`related_type` AS `related_type`,
    `sr`.`related_id` AS `related_id`,
    (case
        when (`sr`.`related_type` = 1) then (
        select
            `police`.`t_profile_sthy`.`JJDBH`
        from
            `police`.`t_profile_sthy`
        where
            (`police`.`t_profile_sthy`.`ID` = `sr`.`related_id`))
        else `sr`.`related_str_id`
    end) AS `related_str_id`,
    `sr`.`related_info` AS `related_info`
from
    ((`police`.`t_collaboration` `t`
left join `police`.`t_collaboration_case_relation` `cr` on
    ((`t`.`id` = `cr`.`collaboration_id`)))
left join `police`.`t_collaboration_scene_relation` `sr` on
    ((`t`.`id` = `sr`.`collaboration_id`)));
    
CREATE  VIEW `t_fight_composite_relation_view` AS
select
    `t`.`id` AS `composite_id`,
    `t`.`create_user_id` AS `create_user_id`,
    `t`.`create_dept_id` AS `create_dept_id`,
    `t`.`create_time` AS `create_time`,
    `t`.`update_user_id` AS `update_user_id`,
    `t`.`update_dept_id` AS `update_dept_id`,
    `t`.`update_time` AS `update_time`,
    `cer`.`case_event_id` AS `case_event_id`,
    (
    select
        `police`.`t_profile_case`.`asjbh`
    from
        `police`.`t_profile_case`
    where
        (`police`.`t_profile_case`.`id` = `cer`.`case_event_id`)) AS `case_event_code`,
    `pr`.`person_id` AS `person_id`,
    `pr`.`id_number` AS `person_id_number`,
    `pr`.`person_kind` AS `person_kind`,
    `jr`.`intelligence_id` AS `intelligence_id`,
    (
    select
        `police`.`t_user`.`idcard`
    from
        `police`.`t_user`
    where
        (`police`.`t_user`.`id` = `t`.`create_user_id`)) AS `create_user_id_card`,
    (
    select
        `police`.`t_user`.`idcard`
    from
        `police`.`t_user`
    where
        (`police`.`t_user`.`id` = `t`.`update_user_id`)) AS `update_user_id_card`,
    (
    select
        `police`.`t_dept`.`code`
    from
        `police`.`t_dept`
    where
        (`police`.`t_dept`.`id` = `t`.`create_dept_id`)) AS `create_dept_code`,
    (
    select
        `police`.`t_dept`.`code`
    from
        `police`.`t_dept`
    where
        (`police`.`t_dept`.`id` = `t`.`update_dept_id`)) AS `update_dept_code`
from
    (((`police`.`t_fight_composite` `t`
left join `police`.`t_fight_composite_case_event_relation` `cer` on
    ((`t`.`id` = `cer`.`composite_id`)))
left join `police`.`t_fight_composite_person_relation` `pr` on
    ((`t`.`id` = `pr`.`composite_id`)))
left join `police`.`t_fight_composite_jq_relation` `jr` on
    ((`t`.`id` = `jr`.`composite_id`)));
```

## 12.1

- XMKFB-5437[XMKFB-5403] 后-【泸州】- 搜情模块优化
- XMKFB-5491[XMKFB-5467] 后 - 支持过滤已处理的审批单
- XMKFB-5494[XMKFB-5465] 后-情报池和线索池扩展搜索方式
- XMKFB-5403 线索池-线索池战果填报功能优化
- XMKFB-5476 合- 【自贡】- 警务协作优化
- XMKFB-5522 【自贡】人力情报，移交弹窗中重复显示了移交单位数据
- XMKFB-5537 【自贡】人力情报，提交人力情报失败后仍上报成功
- XMKFB-5463 【自贡】人力情报，创建协作或线索后情报仍显示的是未创建状态
- XMKFB-5564 后-【广安】- 合成作战作战成员接口调整
- XMKFB-5462 【自贡】人力情报，实战服务中心单位无法查看保密情报的详情

## 11.4

- XMKFB-5354 后-【省厅】- 发起协作-选择审批人少人问题排查
- XMKFB-5327 【自贡】人力情报，情报列表按照是否发起线索筛选无效
- XMKFB-5366 【自贡】人力情报，上报我的列表中未显示出对应的情报数据
- XMKFB-5445 后-合成作战提供删除接口
- XMKFB-5339 后-【德阳】-北新机械厂-群体档案-上传附件bug
- XMKFB-5444 后-设为关键信息不生效

## 11.2

- XMKFB-4728 广安-预案相关调整
- XMKFB-5054[XMKFB-5052] 后 - 增加相关审批流程
- XMKFB-5176 后-【南充】-搜情统计模块检索不出数据
- XMKFB-5148 【泸州】5110工单协作详情里未反显警情、案件、涉案人员数据

### 新增配置项

```properties
# 20241114 - 蒋俊杰 - 人力情报是否增加审批流程(自贡环境配置)
fight.cluePool.humanIntelligence.needApproval=true
```

## 11.1

- XMKFB-4977[XMKFB-4865] 后 - 优化移交目标列表接口(类别列表)
- XMKFB-4987[XMKFB-4865] 后 - 完善草稿箱的保存跟查询
- XMKFB-4988[XMKFB-4865] 后 - 完善移交行为
- XMKFB-4992[XMKFB-4865] 后 - 优化是否发起协同/线索的过滤
- XMKFB-5010[XMKFB-4865] 后 - 删除接口功能开发
- XMKFB-4993[XMKFB-4865] 后 - 发起协同后增加回调行为
- XMKFB-5014[XMKFB-4865] 后 - 增加获取协同跟情指线索相关信息接口
-

### 新增配置项

```properties
# 新建配置fight-new.properties
# 20241105 - 褚川宝 - 自定义注入移交目标
# 是否开启相关自定义注入功能
fight.cluePool.getTargetDepts.custom.enable=false
# 自定义的移交目标，key为policeType，value为警种代码
fight.cluePool.getTargetDepts.custom.config={"其他":1}
# 中心人员是否可以跳过初研(自贡改为false)
fight.cluePool.centerUser.canSkipSpecific=true

```

## RC20241030

- XMKFB-4878 后-【泸州】-工单系统-添加工单报错

## v10.5

- XMKFB-4851[XMKFB-4824] 后-【自贡】-警务协作-紧急警情表单修改
- XMKFB-4587 【广安】预案的启动记录逻辑判定有误

## v10.4

- XMKFB-4779 合-【泸州】-警务协作-建模服务优化

## v10.3

- XMKFB-4344 后-【泸州】-审批表单支持单位/个人签章
- XMKFB-4585 【广安】预案管理，编辑预案的调度措施无效
- XMKFB-4495 合-【泸州】-发起协作显示协作单位优化
- XMKFB-4523 合-【泸州】-警务协作优化
- XMKFB-4587 【广安】预案的启动记录逻辑判定有误

## RC20241015

- XMKFB-4493 后-【泸州】-警务协作优化
- XMKFB-4494[XMKFB-4492] 后-【泸州】-提供协作期望反馈时间接口
- XMKFB-4520 合-【泸州】-警务协作-协作事由及要求增加默认文本

## v10.2

- XMKFB-4076 广安GA-预案功能
- XMKFB-4332 后-【泸州】-协作类别与表单类型支持动态配置
- XMKFB-4328[XMKFB-4111] 后-【泸州】-协作类别与默认协作单位关系

## v9.4

- 作战细览人员增加排序
- XMKFB-4113[XMKFB-3640] 工单生成协作里数据存在的问题
- XMKFB-4140 后-【省厅】-协作编号自动生成
- XMKFB-4170 后-【泸州】-警务协作填写表单内容优化
- XMKFB-4107 添加事项认证通过的工单失败，接口报错
-

## v9.3

- XMKFB-3864 合-【广安】-警务协作需要支持关联案件、警情、嫌疑人
- STAPP-11 前-【省厅app】审批-人员列表，未设置职务的人员，依旧展示为“班长”

## v9.2

- 作战参与人返回职位和警号
- XMKFB-3870 后-完成lz的改造

## v9.1

- XMKFB-3678[XMKFB-3414] 后-协作列表返回字段判断属于“我发起的/我处理的/由我审核”
- XMKFB-3357 省厅GA-技侦协作功能调整
- XMKFB-3273 新建热线输入的手机未匹配到身份证时仍然进入到了第二步
- XMKFB-3624[XMKFB-3504] 【泸州】发起作战表单中，缺少“发起单位”

## v8.5

- XMKFB-3167 省厅GA-协作审批流程变动，添加“签章”功能

## RC20240827

- XMKFB-3501 后-【泸州】线索池统计检索报错

## v8.4

- XMKFB-3275 新建热线生成的任务编号重复
- APP-245 处突列表展示了已撤回的信息内容
- XMKFB-3397 热线任务编号中的地区编码不是申请人所属单位地区编码
- XMKFB-3281 新建热线时匹配到5110库中的身份证但未匹配到云哨账号的热线所属单位为空
- XMKFB-3318 热线列表关键字检索选择‘全部’时检索不出匹配的数据
- XMKFB-3374 已完结的热线仍然能反馈且反馈之后处置进度变为已反馈
- XMKFB-3368 热线列表页显示了非当前用户创建的热线数据
- XMKFB-3307 部分热线数据详情所属单位为空
- XMKFB-3384 热线列表所属单位选择市公安局匹配出了子组织的数据
- XMKFB-3263 省厅日志接入
- XMKFB-3167 省厅GA-协作审批流程变动，添加“签章”功能

## v8.3

- XMKFB-3068 省厅GA-5110功能开发
- XMKFB-3091 后-【省厅】-发起协作流程优化

## RC20240813

- XMKFB-3056 后-【广安】-合成作战流程变更

## v8.1

- XMKFB-2631 后-【自贡】-处突相关接口提供
- XMKFB-2877 合-【省厅】-协同作战流程优化
- XMKFB-2876 后-【省厅】-发送政务微信消息覆盖整个系统模块
- XMKFB-2947 后-【省厅】-协作bug处理
- XMKFB-2678 【泸州】互联网违法犯罪信息自动同步到线索池

## v7.4

- XMKFB-2721 合-【省厅】-协作及相关审批流程修改

## v7.3

- XMKFB-2599 后-【泸州】-合成作战增加关联对象
- XMKFB-2643 后-【泸州】-合成作战报错

## RC20240717

- XMKFB-2665 合-【省厅】-协同作战系统优化

## RC20240716

- XMKFB-2621 政务微信通知消息支持跳转细览
- XMKFB-2621 协作码表调整

## v7.2

- XMKFB-2357 协同作战，补充全部作战页的合成状态筛选项
- XMKFB-2444 后-当处突中的相关预案被删除或禁用后，对应的相关预案数据应为空

### 新增nacos配置

```
#发送业务档案消息需要报存字段
business:
  archives:
    save:
      field: '[{"serviceCode":"dwd_hecheng_info","fieldNames":"id,create_time,create_user_id,create_dept_id,update_time,update_user_id,update_dept_id,title,type,sub_type,status,require,is_allow_apply,approval_process_id,plan_id,event_relation,case_tag_relation,composite_clue_relation,user_relation,is_classic_case,create_time,is_deleted,composite_id,composite_type,urgent_type_id,warning_info,jq_info"},{"serviceCode":"dwd_hecheng_collaboration","fieldNames":"id,create_time,create_user_id,create_dept_id,update_time,update_user_id,update_dept_id,title,status,collaboration_user_id,collaboration_user_name,collaboration_dept_id,collaboration_dept_name,collaboration_type,query_category,query_category,query_resources,require,matter,search_content,feedback_type,feedback_time,phone,attachments,submit_type,notification,tool_type,agent,applicable_situation,remark,is_urged,form_number,is_deleted,collaboration_id"},{"serviceCode":"dwd_hecheng_chat_message","fieldNames":"id,create_time,create_user_id,create_dept_id,update_time,update_user_id,update_dept_id,at_id,composite_id,content,content_wildcard,key_message,refer_content,refer_simple_content,send_time,sender_id,sender_name,simple_content,type,is_deleted,message_id,dept_id,dept_name"},{"serviceCode":"dwd_hecheng_results","fieldNames":"id,create_time,create_user_id,create_dept_id,update_time,update_user_id,update_dept_id,attachments,case_event,composite_id,related_case,related_otherinfo,related_user,suspect,is_deleted,fight_result_id"},{"serviceCode":"dwd_clue_pool","fieldNames":"id,clue_no,clue_type,police_kind,clue_title,clue_content,related_person,related_phone,related_case,related_car,file_list,status,report_time,report_user,report_user_true_name,report_dept_id,shared,agreed,area_code,is_del,collect_user,collect_user_true_name,"}]'  
```

## RC20240710

- XMKFB-2505 合-【省厅】-迁移云哨后的一些功能调整
- XMKFB-2352 处突新消息提示被归为了作战消息
- 作战每个审批环节发送通知消息

## v7.1

- feat: 预案管理列表接口需要添加禁用状态过滤参数
- XMKFB-2401 处突列表增加警情和预警返回
- XMKFB-2349 当处突、协同作战中参与人员存在不同单位的同一人员时，发送消息失败
- XMKFB-2444 后-当处突中的相关预案被删除或禁用后，对应的相关预案数据应为空
- XMKFB-2479 处突，非指挥长人员应不能设置工作要求

## v6.4

- feat: 线索池发送档案消息添加驼峰转下划线逻辑

## v6.3

- XMKFB-2132[XMKFB-2106] 我的审批列表接口添加报送单位简称字段，reportDeptShortName
- XMKFB-2114[XMKFB-2106] 后 - 线索池统计问题处理
- XMKFB-2036[XMKFB-1725] 后-突发处置-预案管理相关接口提供
- XMKFB-2129 【陇南】合成作战流程配置
- XMKFB-1834[XMKFB-1725] 突发功能开发

### es执行脚本

```text
PUT fight_composite_message/_mapping
{
  "properties": {
    "work": {
      "type": "boolean"
    },
    "deptId": {
      "type": "long"
    },
    "deptName": {
      "type": "keyword"
    }
  }
}
```

## v6.2

- XMKFB-2116[XMKFB-2106] 后 - 支持待处理优先排序
- XMKFB-2113[XMKFB-2106] 后 - 文档中第1跟第6点调整
-

## RC 20240611

- XMKFB-2006[XMKFB-1983] 后 - 5.2，5.3，5.4，5.5问题调整
- XMKFB-2008[XMKFB-1983] 后 - 2.3问题调整

## v6.1

- XMKFB-2009[XMKFB-1983] 后 - 3.1，7.1问题处理
- XMKFB-2001[XMKFB-1983] 后 - 优化采集人信息的存储
-

## v5.4

- XMKFB-1872[XMKFB-1862] 【后】-接口需返回上报的目标主侦单位信息
-

## v1.5 发版日志

- XMKFB-888 添加线索池统计模块基类
- XMKFB-915[XMKFB-887] 后-线索池统计-作战单元统计
- XMKFB-916[XMKFB-887] 后-线索池统计-其他警种统计
- XMKFB-912[XMKFB-887] 后 - 定时维护线索相关数据
- XMKFB-952 作战记录中战果分类的优化与调整
- XMKFB-1011 后-合成作战开启审批功能
- XMKFB-1045 合成作战添加参与人员后页面给出的操作提示应作优化
- XMKFB-1054 合成作战添加参与人员审批功能无效
- XMKFB-1051 合成作战，邀请人员审批被驳回后对应作战会在我的作战列表中消失
- XMKFB-1052 邀请人员审批状态变为审批中后会导致对应作战在我的作战列表中消失
- XMKFB-1053 邀请人员审批列表和审批详情页的内容应作调整优化
- ----- 2024-04-25 -----
- XMKFB-1215 在编辑作战页面移除参与人员后会发起审批流程
- XMKFB-1169[XMKFB-983] 后-协作时间轴改造
- XMKFB-1167[XMKFB-983] 后-完成协作审批流程切换及审批服务改造
- ----- 2024-05-06 -----
- XMKFB-1216 在编辑作战页面进行设为/取消协办操作未生效
- XMKFB-1481[XMKFB-1476] 后 - 线索池线索增加收集人字段的存储跟回显
- XMKFB-1530[XMKFB-1524] 【后】—— 线索流转记录需要包含发起协同操作的
-

## v1.4 发版日志

- XMKFB-797 在我发起的列表中按照全部条件进行搜索，搜索接口报错
- XMKFB-796 协作列表，反馈时限字段下显示的”反馈倒计时1天“标注逻辑有误
- XMKFB-822[XMKFB-819] 后-战果填报优化
- XMKFB-765 我发起的列表，协作应只能评价一次
- XMKFB-820[XMKFB-819] 后-发起合成支持关联线索
- XMKFB-852 审批表中有关意见的内容显示有误
- XMKFB-862 我处理的、全部协作列表按照发起单位筛选列表数据报错
- XMKFB-861 全部协作列表中的可查看数据未作限制
- XMKFB-810 操作失败后弹窗中的确定按钮一直处于加载状态无法点击
- XMKFB-876 关联作战列表中显示的数据未作条件限制

## v1.3 发版日志

- XMKFB-663[XMKFB-552] 后-发起协作/协作列表检索接口提供
- XMKFB-672 查看经典案例详情页面的消息记录顺序与在聊天中显示的记录顺序不一致
- XMKFB-680 参与人员的作战页面未显示查看战果入口
- XMKFB-667[XMKFB-552] 后-催办/撤销/删除/再次提交 接口提供
- XMKFB-665[XMKFB-552] 后-完成时间轴接口提供
- XMKFB-666[XMKFB-552] 后-操作流程相关接口提供
- XMKFB-679 将经典案例的作战合并后该作战的经典案例推荐评语显示为空
- XMKFB-685 设置经典案例失败，报系统错误
- XMKFB-775[XMKFB-747] 后 - 相似风险列表接口
- XMKFB-783[XMKFB-747] 后 - 立线/成案接口
- XMKFB-782[XMKFB-747] 后 - 关注/无效接口
- XMKFB-780[XMKFB-747] 后 - 移交接口

## v1.2 发版日志

- XMKFB-595[XMKFB-454] 后-经典案例接口提供
- XMKFB-593[XMKFB-454] 后-战果填报接口提供
- XMKFB-591[XMKFB-454] 后-发起合成优化
- XMKFB-594[XMKFB-454] 后-消息优化
- XMKFB-592[XMKFB-454] 后-合并作战接口提供

### nacos配置更新

```
```