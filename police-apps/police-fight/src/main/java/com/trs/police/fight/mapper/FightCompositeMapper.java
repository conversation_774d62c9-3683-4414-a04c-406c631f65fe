package com.trs.police.fight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.fight.CompositeListVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.fight.domain.vo.ClassicCaseTypeVO;
import com.trs.police.fight.domain.vo.MyCompositeListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * t_fight_composite 表查询接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FightCompositeMapper extends BaseMapper<FightComposite> {

    /**
     * 查询
     *
     * @param params      检索参数
     * @param currentUser 当前用户
     * @param page        分页
     * @return 分页
     */
    Page<CompositeListVO> getPage(@Param("params") ListParamsRequest params,
        @Param("currentUser") CurrentUser currentUser, Page<CompositeListVO> page);

    /**
     * 获取该用户参与的合成
     *
     * @param params 请求参数
     * @param sortParams 排序
     * @param env stQz
     * @return 合成
     */
    List<FightComposite> getMyCompositeList(@Param("params") ListParamsRequest params, @Param("sortParams") SortParams sortParams,
                                            @Param("env") String env);

    /**
     * 获取该用户参与的合成
     *
     * @param params 请求参数
     * @param sortParams 排序
     * @param areaCodePrefix 地域码前缀
     * @param page pg
     * @return 合成
     */
    Page<FightComposite> getMyPageCompositeList(
            @Param("params") ListParamsRequest params,
            @Param("sortParams") SortParams sortParams,
            @Param("areaCodePrefix") String areaCodePrefix,
            Page<FightComposite> page
    );


    /**
     * 获取合成关联用户
     *
     * @param compositeId 合成id
     * @return {@link  SimpleUserVO}
     */
    List<SimpleUserVO> getUserByCompositeId(@Param("compositeId") Long compositeId);

    /**
     * 获取合成关联案件类别
     *
     * @param compositeId 合成id
     * @return {@link List}<{@link ClassicCaseTypeVO}>
     */
    List<ClassicCaseTypeVO> getClassicCaseTypeByCompositeId(@Param("compositeId") Long compositeId);

    /**
     * 根据案事件id获取经典作战案例
     *
     * @param ids  案事件id
     * @param page page
     * @return {@link List}<{@link FightComposite}>
     */
    Page<FightComposite> getClassicCaseByCaesEventIds(@Param("ids") List<Long> ids, Page<FightComposite> page);

    /**
     * getFightCompositeById
     *
     * @param compositeId 合成id
     * @return {@link FightComposite}
     */
    FightComposite getFightCompositeById(@Param("compositeId") Long compositeId);

    /**
     * 获取到锁
     *
     * @param compositeId 作战id
     * @return 作战
     */
    FightComposite selectForUpdate(@Param("compositeId") Long compositeId);

    /**
     * 获取警情原始录音
     *
     * @param id id
     * @return 数据
     */
    String getjjlyh(@Param("id") Long id);

     /**
     * 查询用户所在合成id
     *
     * @param userId 用户id
     * @return 合成id
     */
    List<String> selectRecordIdsOrderByReplyTime(@Param("userId") Long userId);

    /**
     * 根据合成id查询合成
     *
     * @param recordIds 合成id
     * @param page      分页
     * @return 合成
     */
    Page<MyCompositeListVO> selectRecordById(@Param("compositeIds") List<String> recordIds, Page<CompositeListVO> page);
}