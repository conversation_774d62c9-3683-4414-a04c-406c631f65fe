package com.trs.police.fight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.FightCompositeUserRelation;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.domain.vo.CollaborationVO;
import com.trs.police.fight.domain.vo.DemandVO;
import com.trs.police.fight.domain.vo.MyCompositeListVO;
import com.trs.police.fight.domain.vo.RecordVO;
import com.trs.police.fight.mapper.CollaborationMapper;
import com.trs.police.fight.mapper.FightCompositeMapper;
import com.trs.police.fight.mapper.FightCompositeUserRelationMapper;
import com.trs.police.fight.mapper.RecordMapper;
import com.trs.police.fight.service.CommonService;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公共部分服务实现
 *
 * <AUTHOR>
 * @since 2022/4/2 14:17
 **/
@Service
public class CommonServiceImpl implements CommonService {

    @Resource
    private PermissionService permissionService;
    @Resource
    private FightCompositeUserRelationMapper fightCompositeUserRelationMapper;
    @Resource
    private RecordMapper recordMapper;
    @Resource
    private CollaborationMapper collaborationMapper;
    @Resource
    private FightCompositeMapper compositeMapper;


    @Override
    public List<SimpleUserVO> getUserByUnitId(String code, Long compositeId) {
        String prefix = StringUtil.getPrefixCode(code);
        List<FightCompositeUserRelation> fightCompositeUserRelations = new ArrayList<>();
        if (Objects.nonNull(compositeId)) {
            fightCompositeUserRelations = fightCompositeUserRelationMapper.selectList(
                Wrappers.lambdaQuery(FightCompositeUserRelation.class)
                    .eq(FightCompositeUserRelation::getCompositeId, compositeId));
        }
        List<CurrentUser> users = permissionService.getUserListByDeptCodePrefix(prefix);
        if (fightCompositeUserRelations.isEmpty()) {
            return users.stream().map(SimpleUserVO::new).collect(Collectors.toList());
        } else {
            List<Long> collect = fightCompositeUserRelations.stream().map(FightCompositeUserRelation::getUserId)
                .collect(Collectors.toList());
            return users.stream().filter(user -> !collect.contains(user.getId())).map(SimpleUserVO::new)
                .collect(Collectors.toList());
        }
    }

    @Override
    public PageResult<DemandVO> getMyDemand(PageParams pageParams) {
        CurrentUser user = AuthHelper.getNotNullUser();
        List<DemandVO> result = new ArrayList<>();
        String env = BeanFactoryHolder.getEnv().getProperty("com.trs.fight.unReadCount.env","ys");
        if ("ys".equals(env)) {
            List<CollaborationVO> voList = collaborationMapper.selectDemandByUserId(user.getId());
            result = buildYsMyDemandVO(voList);
        }else if ("yq".equals(env)){
            String userId = recordMapper.selectUserIdByIdNumber(user.getIdNumber());
            if (StringUtils.isBlank(userId)) {
                return PageResult.empty(pageParams);
            }
            List<Map<String, Object>> list = recordMapper.selectDemandByUserId(userId);
            result = buildYqMyDemandVO(list);
        }

        return PageResult.of(result, pageParams);
    }

    private static List<DemandVO> buildYsMyDemandVO(List<CollaborationVO> voList) {
        List<DemandVO> result;
        result = voList.stream().map(map ->{
            DemandVO vo = new DemandVO();
            vo.setId(String.valueOf(map.getId()));
            vo.setTitle(map.getTitle());
            vo.setType(map.getCollaborationTypeName());
            vo.setInitiator("发起人：" + map.getStartUserName() + "(" + map.getStartDeptName() + ")");
            Date date = TimeUtils.stringToDate(map.getCreateTime());
            LocalDateTime time = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            vo.setCreateTime(TimeUtil.getSimpleTime(time));
            vo.setOriginTime(time);
            return  vo;
        }).collect(Collectors.toList());
        return result;
    }


    private static List<DemandVO> buildYqMyDemandVO(List<Map<String, Object>> list) {
        List<DemandVO> result;
        result = list.stream().map(map -> {
            DemandVO vo = new DemandVO();
            vo.setId((String) map.get("ID"));
            vo.setTitle((String) map.get("TITLE"));
            vo.setType((String) map.get("TYPE"));
            vo.setInitiator("发起人：" + map.get("USERNAME") + "(" + map.get("DEPTNAME") + ")");
            Timestamp time = (Timestamp) map.get("CREATETIME");
            vo.setCreateTime(TimeUtil.getSimpleTime(time.toLocalDateTime()));
            vo.setOriginTime(time.toLocalDateTime());
            return vo;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    public PageResult<RecordVO> getMyRecord(PageParams pageParams) {
        CurrentUser user = AuthHelper.getNotNullUser();
        List<RecordVO> result = new ArrayList<>();
        List<String> recordIds = new ArrayList<>();
        String env = BeanFactoryHolder.getEnv().getProperty("com.trs.fight.unReadCount.env","ys");
        if ("ys".equals(env)) {
            recordIds = compositeMapper.selectRecordIdsOrderByReplyTime(user.getId());
            result = buildYsMyRecord(pageParams, recordIds);
        }else if ("yq".equals(env)){
            String yqUserId = recordMapper.selectUserIdByIdNumber(user.getIdNumber());
            if (StringUtils.isBlank(yqUserId)) {
                return PageResult.empty(pageParams);
            }
            recordIds = recordMapper.selectRecordIdsOrderByReplyTime(yqUserId).stream().distinct()
                    .collect(Collectors.toList());
            result = buildYqMyRecord(pageParams, recordIds);
        }
        return PageResult.of(result, pageParams.getPageNumber(), recordIds.size(), pageParams.getPageSize());
    }

    private List<RecordVO> buildYsMyRecord(PageParams pageParams, List<String> recordIds) {
        List<RecordVO> result;
        Page<MyCompositeListVO> list = compositeMapper.selectRecordById(recordIds,pageParams.toPage());
        result = list.getRecords().stream().map(composite -> {
            RecordVO vo = new RecordVO();
            vo.setId(String.valueOf(composite.getId()));
            vo.setTitle(composite.getTitle());
            vo.setType(composite.getCompositionType());
            vo.setMemberCount(composite.getPartnerCount());
            vo.setLastReplyTime(TimeUtil.getSimpleTime(composite.getLastMessageSendTime()));
            vo.setOriginTime(composite.getLastMessageSendTime());
            String lastMessageContent = composite.getLastMessageContent();
            if (StringUtils.isNotEmpty(lastMessageContent)) {
                JSONObject jsonObject = JSON.parseObject(lastMessageContent);
                String messageContent = jsonObject.getJSONObject("message").getString("content");
                String senderName = jsonObject.getJSONObject("sender").getString("realName");
                vo.setLastReply(senderName + ":" + messageContent);
            }
            return vo;
        }).collect(Collectors.toList());
        return result;
    }


    private List<RecordVO> buildYqMyRecord(PageParams pageParams, List<String> recordIds) {
        List<RecordVO> result;
        result = PageResult.pageList(recordIds, pageParams).stream().map(id -> {
           RecordVO vo = new RecordVO();
           vo.setId(id);
           Map<String, String> recordMap = recordMapper.selectRecordById(id);
           vo.setTitle(recordMap.get("TITLE"));
           vo.setType(recordMap.get("BATTLETYPE"));
           Integer memberCount = recordMap.get("PARTUSERS").split(",").length;
           vo.setMemberCount(memberCount);
           Map<String, Object> replyMap = recordMapper.selectRecordReplyById(id);
           if (replyMap != null) {
               Timestamp time = (Timestamp) replyMap.get("REPLYTIME");
               vo.setLastReplyTime(TimeUtil.getSimpleTime(time.toLocalDateTime()));
               vo.setOriginTime(time.toLocalDateTime());
               vo.setLastReply(replyMap.get("REPLYBYNAME") + ":" + replyMap.get("DETAIL"));
           }
           return vo;
       }).collect(Collectors.toList());
        return result;
    }

    private PageResult<TodoTaskVO> getMyCommand(PageParams pageParams) {
        CurrentUser user = AuthHelper.getNotNullUser();
        String yqUserId = recordMapper.selectUserIdByIdNumber(user.getIdNumber());
        if (StringUtils.isBlank(yqUserId)) {
            return PageResult.empty(pageParams);
        }
        List<Map<String, Object>> list = recordMapper.selectCommandByUserId(yqUserId);
        List<TodoTaskVO> result = list.stream().map(map -> {
            TodoTaskVO vo = new TodoTaskVO();
            vo.setYqId((String) map.get("ID"));
            vo.setContent((String) map.get("TITLE"));
            vo.setType(6);
            Timestamp time = (Timestamp) map.get("PUBLISHTIME");
            vo.setStatus(new CodeNameVO(2, "已发布"));
            vo.setOriginTime(time.toLocalDateTime());
            vo.setTime(TimeUtil.getSimpleTime(time.toLocalDateTime()));
            vo.setIsRead(true);
            return vo;
        }).collect(Collectors.toList());
        return PageResult.of(result, pageParams.getPageNumber(), result.size(), pageParams.getPageSize());
    }

    @Override
    public PageResult<TodoTaskVO> getMyDemandTodo(PageParams r) {
        PageResult<DemandVO> demandPageResult = getMyDemand(r);
        List<TodoTaskVO> todoList = demandPageResult.getItems().stream().map(demand -> {
            TodoTaskVO todo = new TodoTaskVO();
            todo.setYqId(demand.getId());
            todo.setType(7);
            todo.setTime(demand.getCreateTime());
            todo.setContent(demand.getTitle());
            todo.setStatus(new CodeNameVO(2, "已发布"));
            todo.setIsRead(true);
            return todo;
        }).collect(Collectors.toList());
        return PageResult.of(todoList, r.getPageNumber(), demandPageResult.getTotal(), r.getPageSize());
    }

    @Override
    public PageResult<TodoTaskVO> getMyRecordTodo(PageParams r) {
        PageResult<RecordVO> recordResult = getMyRecord(r);
        List<TodoTaskVO> todoList = recordResult.getItems().stream().map(recordVO -> {
            TodoTaskVO todo = new TodoTaskVO();
            todo.setYqId(recordVO.getId());
            todo.setType(5);
            todo.setTime(recordVO.getLastReplyTime());
            todo.setContent(recordVO.getTitle());
            todo.setStatus(new CodeNameVO(2, "进行中"));
            todo.setIsRead(true);
            return todo;
        }).collect(Collectors.toList());
        return PageResult.of(todoList, r.getPageNumber(), recordResult.getTotal(), r.getPageSize());
    }

    @Override
    public PageResult<TodoTaskVO> getMyCommandTodo(PageParams r) {
        return getMyCommand(r);
    }

}
