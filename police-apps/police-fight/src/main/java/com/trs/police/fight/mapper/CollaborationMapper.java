package com.trs.police.fight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.vo.permission.DataPermissionInfo;
import com.trs.police.fight.domain.request.CollaborationListRequest;
import com.trs.police.fight.domain.vo.CollaborationVO;
import com.trs.police.fight.statistic.DTO.CommonDTO;
import com.trs.police.statistic.domain.bean.CountItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/03/07
 * @description: t_collaboration表查询接口
 */
@Mapper
public interface CollaborationMapper extends BaseMapper<Collaboration> {

    /**
     * 我发起的
     *
     * @param params      检索参数
     * @param currentUser 当前用户
     * @param page        分页
     * @return 分页
     */
    Page<CollaborationVO> getMeStartCollaborations(@Param("params") CollaborationListRequest params,
                                               @Param("currentUser") CurrentUser currentUser,
                                               Page<CollaborationVO> page);

    /**
     * 我发起的未读数量
     *
     * @param currentUser currentUser
     * @return {@link Long}
     */
    Long meStartUnreadCount(@Param("currentUser") CurrentUser currentUser);

    /**
     * 我审核的
     *
     * @param params 检索参数
     * @param currentUser 当前用户
     * @param page 分页
     * @return {@link Page}<{@link CollaborationVO}>
     */
    Page<CollaborationVO> getMeApprovedCollaborations(@Param("params") CollaborationListRequest params,
                                                   @Param("currentUser") CurrentUser currentUser,
                                                   Page<CollaborationVO> page);

    /**
     * 我审核的未读数量
     *
     * @param currentUser currentUser
     * @return {@link Long}
     */
    Long meApprovedUnreadCount(@Param("currentUser") CurrentUser currentUser);

    /**
     * 我处理的
     *
     * @param params         检索参数
     * @param currentUser    当前用户
     * @param page           分页
     * @param permissionInfo 权限信息
     * @return {@link Page}<{@link CollaborationVO}>
     */
    Page<CollaborationVO> getMeProcessedCollaborations(@Param("params") CollaborationListRequest params,
                                                        @Param("currentUser") CurrentUser currentUser,
                                                       @Param("permissionInfo") DataPermissionInfo permissionInfo, Page<CollaborationVO> page);

    /**
     * 我处理的未读数量
     *
     * @param currentUser    当前用户
     * @param permissionInfo 权限信息
     * @return {@link Long}
     */
    Long meProcessedUnreadCount(@Param("currentUser") CurrentUser currentUser, @Param("permissionInfo") DataPermissionInfo permissionInfo);

    /**
     * 全部协作
     *
     * @param params         检索参数
     * @param currentUser    当前用户
     * @param page           分页
     * @param permissionInfo 权限信息
     * @return {@link Page}<{@link CollaborationVO}>
     */
    Page<CollaborationVO> getAllCollaborations(@Param("params") CollaborationListRequest params,
                                                       @Param("currentUser") CurrentUser currentUser,
                                                       @Param("permissionInfo") DataPermissionInfo permissionInfo,
                                                       Page<CollaborationVO> page);

    /**
     * 全部未读数量
     *
     * @param currentUser    当前用户
     * @param permissionInfo 权限信息
     * @return {@link Long}
     */
    Long allUnreadCount(@Param("currentUser") CurrentUser currentUser, @Param("permissionInfo") DataPermissionInfo permissionInfo);

    /**
     * getCollaborationById
     *
     * @param collaborationId 协作id
     * @return {@link Collaboration}
     */
    Collaboration getCollaborationById(@Param("collaborationId") Long collaborationId);

    /**
     * 获取支撑数量
     *
     * @param currentAreaCode xx
     * @param commonParams xx
     * @return xx
     */
    List<CountItem> getZcCount(@Param("code") String currentAreaCode,@Param("dto") CommonDTO commonParams);

    /**
     * 获取办结数量
     *
     * @param currentAreaCode xx
     * @param commonParams xx
     * @return xx
     */
    List<CountItem> getBjCount(@Param("code") String currentAreaCode,@Param("dto") CommonDTO commonParams);

    /**
     * 获取发起数量
     *
     * @param currentAreaCode xx
     * @param commonParams xx
     * @param result 是否是同一天
     * @return ss
     */
    List<CountItem> getFqCountByDate(@Param("code") String currentAreaCode,@Param("dto") CommonDTO commonParams
                                    ,@Param("result") boolean result);

    /**
     * 获取办结数量数量
     *
     * @param currentAreaCode xx
     * @param commonParams xx
     * @param result 是否是同一天
     * @return sss
     */
    List<CountItem> getBjCountByDate(@Param("code") String currentAreaCode,@Param("dto") CommonDTO commonParams
                                    ,@Param("result") boolean result);

    /**
     * 获取超时间数量
     *
     * @param currentAreaCode xx
     * @param commonParams xx
     * @return ss
     */
    List<Collaboration> getCsCountByDate(@Param("code") String currentAreaCode,@Param("dto") CommonDTO commonParams);
    /**
     * 获取支撑数量
     *
     * @param currentAreaCode xx
     * @param commonParams xx
     * @return xx
     */
    List<CountItem> getDeptZcCount(@Param("code") String currentAreaCode,@Param("dto") CommonDTO commonParams);

    /**
     * 获取支撑数量
     *
     * @param currentAreaCode xx
     * @param commonParams xx
     * @return xx
     */
    List<CountItem> getDeptBjCount(@Param("code") String currentAreaCode,@Param("dto") CommonDTO commonParams);

    /**
     * 查询用户协作
     *
     * @param userId 用户id
     * @return 查询用户协作
     */
    List<CollaborationVO> selectDemandByUserId(@Param("userId") Long userId);
}
