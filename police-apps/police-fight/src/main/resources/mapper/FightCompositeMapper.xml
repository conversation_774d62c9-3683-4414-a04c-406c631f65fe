<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.fight.mapper.FightCompositeMapper">
    <resultMap id="compositeListVOMap" type="com.trs.police.common.core.vo.fight.CompositeListVO">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="fight_require" property="require"/>
        <result column="participant_count" property="participantCount"/>
        <result column="organizer" property="organizer"/>
        <result column="organized_dept" property="organizedDept"/>
        <result column="is_classified" property="isClassified"/>
        <result column="is_display_map" property="isDisplayMap"/>
        <result column="create_time" property="createTime"/>
        <result column="type_name" property="typeName"/>
        <result column="watchFlag" property="watchFlag"></result>
        <result column="subtype_name" property="subtypeName"/>
        <result column="compositeType" property="compositeType"/>
        <result column="urgentTypeId" property="urgentTypeId"/>
        <result column="planId" property="planId"/>
        <result column="warningInfo" property="warningInfo"/>
        <result column="policeIntelligenceInfo" property="policeIntelligenceInfo"/>
        <result column="formType" property="formType"/>
        <result column="jqbh" property="jqbh"/>
        <result column="jqlxdm" property="jqlxdm"/>
        <result column="jqContent" property="jqContent"/>
        <result column="target" property="target"/>
        <result column="element" property="element"/>
        <result column="type" property="type"/>
        <result column="sub_type" property="subType"/>
        <association property="status" column="status" select="getStatus"
                     javaType="com.trs.police.common.core.vo.CodeNameVO"/>
    </resultMap>

    <select id="getStatus" resultType="com.trs.police.common.core.vo.CodeNameVO">
        select code,
        name
        from t_dict
        where code = #{code}
        and type = 'fight_composite_status'
    </select>


    <select id="getPage" resultMap="compositeListVOMap">
        select
        tf.id as id,
        tf.title as title,
        tf.require as fight_require,
        (select count(1) from t_fight_composite_user_relation r where r.composite_id = tf.id) as participant_count,
        (select u.real_name from t_user u where u.id in (select r.user_id from t_fight_composite_user_relation r where
        r.composite_id = tf.id and r.role = 1) limit 1) as organizer,
        (select GROUP_CONCAT(d.short_name) from t_dept d where d.id in (select r.dept_id from
        t_fight_composite_user_relation r where r.composite_id = tf.id and r.role = 1)) as organized_dept,
        if((SELECT count(1) FROM t_fight_composite_user_relation r WHERE r.composite_id = tf.id AND r.user_id =
        #{currentUser.id} AND r.dept_id = #{currentUser.dept.id}) or !is_classified , 0, 1)
        as is_classified,
        tf.create_time as create_time,
        tf.status as status,
        tf.is_classic_case as isClassicCase,
        tf.is_display_map as is_display_map,
        (select name from t_dict td where td.id = tf.type) as type_name,
        (select name from t_dict td where td.id = tf.sub_type) as subtype_name,
        tf.composite_type as compositeType,
        tf.urgent_type_id as urgentTypeId,
        tf.plan_id as planId,
        tf.warning_info as warningInfo,
        tf.jq_info as policeIntelligenceInfo,
        tf.event_place as eventPlace,
        tf.data_source as dataSource,
        tf.watch_flag as watchFlag,
        tf.form_type as formType,
        tf.jqbh as jqbh,
        tf.jqlxdm as jqlxdm,
        tf.jq_content as jqContent,
        tf.target as target,
        tf.element as element,
        tf.`type`,
        tf.sub_type
        from t_fight_composite tf
        <where>
            tf.is_del != 1
            <include refid="findBySearchParams"/>
            <include refid="findByFilterParams"/>
        </where>
        <include refid="orderBySortParams"/>
    </select>

    <select id="getMyCompositeList" resultType="com.trs.police.common.core.entity.FightComposite">
        select distinct t1.*
        from t_fight_composite t1
        join t_fight_composite_user_relation t2
        on t1.id=t2.composite_id
        <choose>
            <when test="env == 'stQz'">
                and (t2.receive_status is null or t2.receive_status = 6 or t2.receive_status = 7)
            </when>
            <otherwise>
                and (t2.status is null or t2.status = 3)
            </otherwise>
        </choose>
        <where>
            <bind name="userId" value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().id"/>
            <bind name="deptId" value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().dept.id"/>
            and t2.user_id=#{userId}
            and t2.dept_id=#{deptId}
            <include refid="getMyCompositeListWhere"/>
        </where>
        <if test="null != sortParams and null != sortParams.sortField and '' != sortParams.sortField">
            <if test="'feedbackTime' == sortParams.sortField">
                order by last_msg_send_time
            </if>
            <if test="'createTime' == sortParams.sortField">
                order by create_time
            </if>
            <if test="'desc' == sortParams.sortDirection">
                desc
            </if>
        </if>
    </select>

    <select id="getMyPageCompositeList" resultType="com.trs.police.common.core.entity.FightComposite">
        select
        t1.*
        from
        t_fight_composite t1
        <where>
            <include refid="getMyCompositeListWhere"/>
            <if test="null != areaCodePrefix and areaCodePrefix != ''">
                and EXISTS (select id from t_dept td where td.id = t1.create_dept_id and td.district_code like
                concat('', #{areaCodePrefix},'%'))
            </if>
        </where>
        ORDER by t1.create_time desc
    </select>

    <sql id="getMyCompositeListWhere">
        and t1.is_del != 1
        <bind name="searchParams" value="params.searchParams"/>
        <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="searchField" value="searchParams.searchField"/>
            <bind name="searchValue"
                  value="'%'+ @com.trs.police.common.core.utils.StringUtil@removeSpecialCharacters(searchParams.searchValue)+ '%'"/>
            <choose>
                <when test="'title' == searchField">
                    and (t1.title like #{searchValue}
                </when>
                <otherwise>
                    and (
                    t1.title like #{searchValue}
                    or EXISTS(SELECT * FROM t_fight_composite_message where t1.id=composite_id and
                    (content->>"$.message.content") like #{searchValue} )
                    )
                </otherwise>
            </choose>
        </if>
        <bind name="params" value="params.filterParams"/>
        <if test="params != null and params.size() > 0">
            <foreach collection="params" item="param">
                <choose>
                    <when test="param.key=='dataSource'">
                        and t1.data_source = #{param.value}
                    </when>
                    <when test="param.key=='compositeStatus'">
                        and t1.status =#{param.value}
                    </when>
                    <when test="param.key == 'principalDept'">
                        <bind name="deptList"
                              value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(param.getProcessedValue())"/>
                        AND t2.composite_id in (SELECT DISTINCT(composite_id)
                        FROM t_fight_composite_user_relation
                        where role =1
                        and JSON_OVERLAPS(#{deptList},(SELECT JSON_ARRAYAGG(d.id)FROM t_dept d WHERE ifnull((select
                        concat(t3.path,t3.id,'-') from t_dept t3 where t3.id=dept_id),'') LIKE CONCAT('%-', d.id,
                        '-%'))) > 0
                        )
                    </when>
                    <when test="param.key == 'createTime' and param.getProcessedValue().isAll() == false ">
                        <bind name="timeParams" value="param.getProcessedValue()"/>
                        AND (t1.create_time between #{timeParams.beginTime} AND #{timeParams.endTime})
                    </when>
                    <when test="param.key == 'onlyMine' and param.getProcessedValue()  ">
                        AND t2.role =1
                    </when>
                    <when test="param.key=='compositeType'">
                        and t1.composite_type = #{param.value}
                    </when>
                    <when test="param.key=='compositeStatusList'">
                        <bind name="compositeStatusList" value="param.getProcessedValue()"/>
                        <if test="compositeStatusList != null and compositeStatusList.size() > 0">
                            and t1.status in
                            <foreach collection="compositeStatusList" open="(" close=")" separator="," item="it">
                                ${it}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </foreach>
        </if>
    </sql>


    <select id="getUserByCompositeId" resultType="com.trs.police.common.core.vo.permission.SimpleUserVO">
        SELECT
        u.id AS userId,
        u.real_name AS userName,
        u.mobile AS tel,
        t.id AS deptId,
        t.`name` AS deptName,
        t.short_name AS deptShortName,
        t.`code` AS deptCode
        FROM
        t_user u
        JOIN t_fight_composite_user_relation udr ON udr.user_id = u.id and (udr.status is null or udr.status = 3)
        JOIN t_dept t ON udr.dept_id = t.id
        where udr.composite_id = #{compositeId}
    </select>

    <select id="getClassicCaseTypeByCompositeId" resultType="com.trs.police.fight.domain.vo.ClassicCaseTypeVO">
        SELECT
        dm as id,
        ct as name
        FROM JWZH_DICT2
        WHERE zdbh = 'GA_D_XSAJLBDM' AND dm IN (
        SELECT
        t2.ajlbdm
        FROM
        t_profile_case t1
        JOIN JWZH_XSAJ_AJ t2 ON t1.asjbh = t2.asjbh
        JOIN t_fight_composite_case_event_relation t3 ON t1.id = t3.case_event_id
        WHERE t3.composite_id = #{compositeId})
    </select>

    <select id="getClassicCaseByCaesEventIds" resultType="com.trs.police.common.core.entity.FightComposite">
        SELECT
        DISTINCT t1.*
        FROM
        t_fight_composite t1
        LEFT JOIN t_fight_composite_case_event_relation t2 ON t1.id = t2.composite_id
        WHERE t1.is_classic_case = 1 AND t2.case_event_id IN
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY t1.create_time DESC
    </select>

    <select id="getFightCompositeById" resultType="com.trs.police.common.core.entity.FightComposite">
        SELECT
        tc.*,
        (SELECT GROUP_CONCAT( case_event_id SEPARATOR ';' ) FROM t_fight_composite_case_event_relation WHERE
        composite_id = #{compositeId} ) AS eventRelation,
        (SELECT GROUP_CONCAT( case_tag_id SEPARATOR ';' ) FROM t_fight_case_tag_relation WHERE composite_id =
        #{compositeId} ) AS caseTagRelation,
        (SELECT GROUP_CONCAT( clue_id SEPARATOR ';' ) FROM t_fight_composite_clue_relation WHERE composite_id =
        #{compositeId} ) AS compositeClueRelation,
        (SELECT GROUP_CONCAT( user_id SEPARATOR ';' ) FROM t_fight_composite_user_relation WHERE composite_id =
        #{compositeId} ) AS userRelation
        FROM
        t_fight_composite tc
        WHERE
        tc.id = #{compositeId}
    </select>

    <select id="selectForUpdate" resultType="com.trs.police.common.core.entity.FightComposite">
        SELECT id FROM t_fight_composite where id = #{compositeId} FOR UPDATE
    </select>
    <select id="getjjlyh" resultType="java.lang.String">
        SELECT
        jjlyh
        FROM
        t_profile_sthy
        WHERE
        id = #{id}
    </select>
    <select id="selectRecordIdsOrderByReplyTime" resultType="java.lang.String">
        select
        tfc.composite_id as id
        from t_fight_composite_user_relation tfc
        <where>
            tfc.user_id = #{userId}
        </where>
        order by
        tfc.create_time desc
    </select>
    <select id="selectRecordById" resultType="com.trs.police.fight.domain.vo.MyCompositeListVO">
        select
        tf.id as id,
        tf.title as title,
        (select name from t_dict d where d.type = 'fight_composite_type' and d.code = tf.type) as compositionType,
        (select count(distinct tfr.user_id)
        from t_fight_composite_user_relation tfr where tfr.composite_id = tf.id)
        as partnerCount,
        tf.last_msg_send_time as lastMessageSendTime,
        (select content from t_fight_composite_message tfm where tfm.id = tf.last_msg_id) as lastMessageContent
        from t_fight_composite tf
        <where>
            1=1
            <if test="compositeIds != null and compositeIds.size() > 0">
                and tf.id in
                <foreach collection="compositeIds" item="compositeId" open="(" close=")" separator=",">
                    #{compositeId}
                </foreach>
            </if>
        </where>
        order by tf.create_time desc
    </select>

    <sql id="orderBySortParams">
        <bind name="sortParams" value="params.sortParams"/>
        <choose>
            <when test="sortParams != null and @org.apache.commons.lang3.StringUtils@isNotBlank(sortParams.sortField)">
                order by
                <if test="sortParams.sortField == 'createTime'">
                    tf.create_time
                </if>
                ${sortParams.getProcessedValue()},tf.update_time desc
            </when>
            <otherwise>
                order by tf.update_time desc
            </otherwise>
        </choose>
    </sql>

    <sql id="findBySearchParams">
        <bind name="searchParams" value="params.searchParams"/>
        <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="searchField" value="searchParams.searchField"/>
            <bind name="searchValue"
                  value="@com.trs.police.common.core.utils.StringUtil@removeSpecialCharacters(searchParams.searchValue)"/>
            <choose>
                <when test="searchField == 'fullText'">
                    and (
                    tf.title like concat('%',#{searchParams.searchValue},'%')
                    or (tf.id in (select fu.composite_id from t_fight_composite_user_relation fu join t_user u on
                    fu.user_id = u.id and fu.role = 1 where u.real_name like
                    concat('%',#{searchParams.searchValue},'%')))
                    or tf.id in (select m.composite_id from t_fight_composite_message m where
                    m.content->>"$.message.content" like concat('%',#{searchParams.searchValue},'%') and m.type = 5 )
                    or tf.attachments->'$[*].name' like concat('%',#{searchParams.searchValue},'%')
                    )
                </when>
                <when test="searchField == 'title'">
                    and tf.title like concat('%',#{searchParams.searchValue},'%')
                </when>
                <when test="searchField == 'organizer'">
                    and tf.id in (select fu.composite_id from t_fight_composite_user_relation fu join t_user u on
                    fu.user_id = u.id and fu.role = 1 where u.real_name like
                    concat('%',#{searchParams.searchValue},'%'))
                </when>
                <when test="searchField == 'message'">
                    and tf.id in (select m.composite_id from t_fight_composite_message m where
                    m.content->>"$.message.simpleContent" like concat('%',#{searchParams.searchValue},'%') and m.type =
                    5)
                </when>
                <when test="searchField == 'attachmentName' ">
                    and tf.attachments->'$[*].name' like concat('%',#{searchParams.searchValue},'%')
                </when>
            </choose>
        </if>
    </sql>

    <sql id="findByFilterParams">
        <bind name="filterParams" value="params.filterParams"/>
        <if test="filterParams != null and filterParams.size() > 0">
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <when test="filterParam.key=='dataSource'">
                        and tf.data_source = #{filterParam.value}
                    </when>
                    <when test="filterParam.key=='status'">
                        and tf.status = #{filterParam.value}
                    </when>
                    <when test="filterParam.key=='compositeType'">
                        and tf.composite_type = #{filterParam.value}
                    </when>
                    <when test="filterParam.key=='organizedDept'">
                        <bind name="organizedDept" value="filterParam.getProcessedValue()"/>
                        <if test="organizedDept != null and organizedDept.size() > 0">
                            and EXISTS (select u.dept_id from t_fight_composite_user_relation u where u.composite_id =
                            tf.id and u.role = 1 and u.dept_id in
                            (SELECT d.id FROM t_dept d WHERE
                            <foreach collection="organizedDept" item="item" separator="OR">
                                CONCAT( d.path, d.id, '-') LIKE CONCAT('%-', #{item}, '-%' )
                            </foreach>)
                            )
                        </if>
                    </when>
                    <when test="filterParam.key == 'createTime' and filterParam.getProcessedValue().isAll() == false ">
                        <bind name="createTime" value="filterParam.getProcessedValue()"/>
                        and tf.create_time >= #{createTime.beginTime}
                        and tf.create_time &lt; #{createTime.endTime}
                    </when>
                    <when test="filterParam.key == 'showCurrentComposite'">
                        <bind name="showCurrentComposite" value="filterParam.getProcessedValue()"/>
                        <if test="showCurrentComposite">
                            and tf.id in (select u.composite_id from t_fight_composite_user_relation u where u.user_id =
                            #{currentUser.id})
                        </if>
                    </when>
                    <when test="filterParam.key=='isDisplayMap'">
                        and tf.is_display_map = #{filterParam.value}
                    </when>
                </choose>
            </foreach>
        </if>
    </sql>
</mapper>