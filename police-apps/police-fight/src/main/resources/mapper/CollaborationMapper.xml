<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.fight.mapper.CollaborationMapper">
    <resultMap id="collaborationVOMap" type="com.trs.police.fight.domain.vo.CollaborationVO">
        <result column="attachments" property="file" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToFileInfoHandler"></result>
        <result column="ws_photos" property="wsPhotos" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToFileInfoHandler"></result>
    </resultMap>

    <select id="getMeStartCollaborations" resultMap="collaborationVOMap">
        SELECT
            tc.*,
            tcu2.related_user_name as startUserName,
            tcu2.related_user_dept_id as startDept,
            tcu2.related_user_dept_name as startDeptName,
            tcu2.is_read
        FROM
            t_collaboration tc
        LEFT JOIN t_collaboration_user_relation tcu2 ON tc.id = tcu2.collaboration_id
        <where>
            tc.is_del = 0
            and tcu2.related_user_id = #{currentUser.id}
            and tcu2.personnel_type = 1
            <include refid="findBySearchParams"/>
            <include refid="findByFilterParams"/>
        </where>
        ORDER BY tc.create_time DESC
    </select>

    <select id="meStartUnreadCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            t_collaboration tc
        LEFT JOIN t_collaboration_user_relation tcu ON tc.id = tcu.collaboration_id
        WHERE tcu.related_user_id = #{currentUser.id}
        AND tcu.personnel_type = 1
        AND tcu.is_read = 0
        and tc.is_del = 0
    </select>

    <select id="getMeApprovedCollaborations" resultMap="collaborationVOMap">
        SELECT
            tc.*,
            tcu.is_read,
            tcu2.related_user_name as startUserName,
            tcu2.related_user_dept_id as startDept,
            tcu2.related_user_dept_name as startDeptName
        FROM
            t_collaboration tc
        LEFT JOIN t_collaboration_user_relation tcu ON tc.id = tcu.collaboration_id
        LEFT JOIN t_collaboration_user_relation tcu2 ON tc.id = tcu2.collaboration_id
        <where>
            tc.is_del = 0
            and tcu.related_user_id = #{currentUser.id}
            and tcu.personnel_type = 2
            and tcu2.personnel_type = 1
            and tc.status = 1
            and tcu.related_user_dept_id not in (
                select
                    tca.create_dept_id
                from
                    t_collaboration_approval tca
                where
                    tca.collaboration_id = tcu.collaboration_id and tca.create_time >= tcu.create_time
            )
            <include refid="findBySearchParams"/>
            <include refid="findByFilterParams"/>
        </where>
        ORDER BY tc.create_time DESC
    </select>

    <select id="meApprovedUnreadCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            t_collaboration tc
        LEFT JOIN t_collaboration_user_relation tcu ON tc.id = tcu.collaboration_id
        WHERE
            tcu.related_user_id = #{currentUser.id}
            and tcu.personnel_type = 2
            and tc.status = 1
            and tc.is_del = 0
            and tcu.is_read = 0
        and tcu.related_user_dept_id not in (
            select
                tca.create_dept_id
            from
                t_collaboration_approval tca
            where
                tca.collaboration_id = tcu.collaboration_id and tca.create_time >= tcu.create_time
        )
    </select>

    <select id="getMeProcessedCollaborations" resultMap="collaborationVOMap">
        SELECT
            tc.*,
            tcu.is_read,
            tcu2.related_user_name as startUserName,
            tcu2.related_user_dept_id as startDept,
            tcu2.related_user_dept_name as startDeptName
        FROM
            t_collaboration tc
        LEFT JOIN t_collaboration_user_relation tcu ON tc.id = tcu.collaboration_id
        <!-- 发起用户 -->
        LEFT JOIN t_collaboration_user_relation tcu2 ON tc.id = tcu2.collaboration_id
        <where>
            tc.is_del = 0
            AND tcu.personnel_type in (3,5,6)
            AND tcu2.personnel_type = 1
            <choose>
                <!-- 如果有协作权限，可能是后加的协作权限、所以当时没建立上关联关系、就通过部门来查询 -->
                <when test="null != params and null != params.hasCollaborationPermission and true == params.hasCollaborationPermission">
                    and (
                        tcu.related_user_id = #{currentUser.id}
                        or (
                                tc.status in (3, 5)
                                and tc.collaboration_user_id is null
                                and tcu.related_user_dept_id = #{currentUser.dept.id}
                                and tcu.personnel_type = 6
                                and NOT EXISTS (select tcur_child.id from t_collaboration_user_relation tcur_child where tcur_child.collaboration_id = tc.id and tcur_child.related_user_id =  #{currentUser.id} and (tcur_child.personnel_type = 3 or tcur_child.personnel_type = 5))
                        )
                    )
                </when>
                <otherwise>
                    and tcu.related_user_id = #{currentUser.id}
                </otherwise>
            </choose>
            <include refid="findBySearchParams"/>
            <include refid="findByFilterParams"/>
        </where>
        ORDER BY tc.create_time DESC
    </select>

    <select id="meProcessedUnreadCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            t_collaboration tc
        LEFT JOIN t_collaboration_user_relation tcu ON tc.id = tcu.collaboration_id
        <where>
            (tcu.personnel_type = 3 or tcu.personnel_type = 5)
            AND tcu.is_read = 0
            AND tcu.related_user_id = #{currentUser.id}
            and tc.is_del = 0
        </where>
    </select>

    <select id="getAllCollaborations" resultMap="collaborationVOMap">
        SELECT
            tc.*,
            tcu2.is_read,
            tcu2.related_user_id as startUserId,
            tcu2.related_user_name as startUserName,
            tcu2.related_user_dept_id as startDept,
            tcu2.related_user_dept_name as startDeptName
        FROM
            t_collaboration tc
        LEFT JOIN t_collaboration_user_relation tcu2 ON tc.id = tcu2.collaboration_id
        <where>
            tcu2.personnel_type in (1, 3, 5, 6)
            and tc.is_del = 0
            <include refid="permissionInfo"></include>
            <include refid="findBySearchParams"/>
            <include refid="findByFilterParams"/>
        </where>
        ORDER BY tc.create_time DESC
    </select>

    <select id="allUnreadCount" resultType="java.lang.Long">
        SELECT
            DISTINCT COUNT(1)
        FROM
            t_collaboration tc
        LEFT JOIN t_collaboration_user_relation tcu ON tc.id = tcu.collaboration_id
        <where>
            tcu.is_read = 0
            AND tcu.personnel_type = 1
        </where>
    </select>

    <select id="getCollaborationById" resultType="com.trs.police.common.core.entity.Collaboration">
        SELECT
            tc.*,
            ( SELECT composite_id FROM t_fight_composite_collaboration_relation WHERE collaboration_id = #{collaborationId} ) AS relatedComposite,
            ( SELECT GROUP_CONCAT( case_event_id SEPARATOR ';') FROM t_collaboration_case_relation WHERE collaboration_id = #{collaborationId} ) AS relatedCase
        FROM
        t_collaboration tc
        WHERE
        tc.id = #{collaborationId}
    </select>

    <select id="getZcCount" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select c.collaboration_type as `key`,count(*) as count
        from t_collaboration c inner join t_dept d on c.collaboration_dept_id = d.id
        <where>
            c.`status` in (3,5)
            <if test="code != null and code != ''">
                and d.district_code like concat('',#{code},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and c.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and c.create_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.deptId != null and dto.deptId != ''">
                <choose>
                    <when test="dto.deptId == 99">
                        and (d.police_kind = 99 or d.police_kind is null)
                    </when>
                    <otherwise>
                        and d.police_kind = #{dto.deptId}
                    </otherwise>
                </choose>
            </if>
        </where>
        group by c.collaboration_type
    </select>

    <select id="getBjCount" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select c.collaboration_type as `key`,count(*) as count
        from t_collaboration c inner join t_dept d on c.collaboration_dept_id = d.id
        <where>
            c.status = 5
            <if test="code != null and code != ''">
                and d.district_code like concat('',#{code},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and c.update_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and c.update_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.deptId != null and dto.deptId != ''">
                <choose>
                    <when test="dto.deptId == 99">
                        and (d.police_kind = 99 or d.police_kind is null)
                    </when>
                    <otherwise>
                        and d.police_kind = #{dto.deptId}
                    </otherwise>
                </choose>
            </if>
        </where>
        group by c.collaboration_type
    </select>

    <select id="getFqCountByDate" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select
         <if test="result != null and result == true">
             DATE_FORMAT(c.create_time, '%H')
         </if>
         <if test="result != null and result == false">
             DATE_FORMAT(c.create_time, '%Y%m%d')
         </if>
         as `key`,count(*) as count
        from t_collaboration c inner join t_dept d on c.collaboration_dept_id = d.id
        <where>
            c.`status` in (3,5)
            <if test="code != null and code != ''">
                and d.district_code like concat('',#{code},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and c.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and c.create_time &lt;= #{dto.endTime}
            </if>
        </where>
        group by `key`
    </select>
    <select id="getBjCountByDate" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select
        <if test="result != null and result == true">
            DATE_FORMAT(c.create_time, '%H')
        </if>
        <if test="result != null and result == false">
            DATE_FORMAT(c.create_time, '%Y%m%d')
        </if>
         as `key`,count(*) as count
        from t_collaboration c inner join t_dept d on c.collaboration_dept_id = d.id
        <where>
            c.status = 5
            <if test="code != null and code != ''">
                and d.district_code like concat('',#{code},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and c.update_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and c.update_time &lt;= #{dto.endTime}
            </if>
        </where>
        group by `key`
    </select>
    <select id="getCsCountByDate" resultType="com.trs.police.common.core.entity.Collaboration">
        select feedback_time
        from t_collaboration c inner join t_dept d on c.collaboration_dept_id = d.id
        <where>
            c.status != 5 and c.feedback_time is not null
            <if test="code != null and code != ''">
                and d.district_code like concat('',#{code},'%')
            </if>
        </where>
    </select>
    <select id="getDeptZcCount" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select d.police_kind as `key`,count(*) as count
        from t_collaboration c inner join t_dept d on c.collaboration_dept_id = d.id
        <where>
            c.`status` in (3,5)
            <if test="code != null and code != ''">
                and d.district_code like concat('',#{code},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and c.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and c.create_time &lt;= #{dto.endTime}
            </if>
        </where>
        group by d.police_kind
    </select>
    <select id="getDeptBjCount" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select d.police_kind as `key`,count(*) as count
        from t_collaboration c inner join t_dept d on c.collaboration_dept_id = d.id
        <where>
            c.status = 5
            <if test="code != null and code != ''">
                and d.district_code like concat('',#{code},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and c.update_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and c.update_time &lt;= #{dto.endTime}
            </if>
        </where>
        group by d.police_kind
    </select>

    <select id="selectDemandByUserId" resultType="com.trs.police.fight.domain.vo.CollaborationVO">
        select
        c.id as id,
        c.title as title,
        (select d.name from t_dict d where d.id = c.collaboration_type) as collaborationTypeName,
        (select u.real_name from t_user u where u.id = tcu.related_user_id) as startUserName,
        (select d.short_name from t_dept d where d.id = tcu.related_user_dept_id) as startDeptName,
        c.create_time as createTime
        from t_collaboration c
        inner join t_collaboration_user_relation tcu on c.id = tcu.collaboration_id
        <where>
            1=1
            <if test="userId != null and userId != ''">
                and tcu.related_user_id = #{userId}
            </if>
        </where>
        order by c.create_time desc
    </select>

    <sql id="findBySearchParams">
        <bind name="searchParams" value="params.searchParams"/>
        <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="searchField" value="searchParams.searchField"/>
            <bind name="searchValue" value="@com.trs.police.common.core.utils.StringUtil@removeSpecialCharacters(searchParams.searchValue)"/>
            <choose>
                <when test="searchField == 'fullText'">
                    and (
                    tc.title like concat('%',#{searchParams.searchValue},'%')
                    or tcu2.related_user_name like concat('%',#{searchParams.searchValue},'%')
                    or tc.collaboration_user_name like concat('%',#{searchParams.searchValue},'%')
                    or tc.collaboration_dept_name like  concat('%',#{searchParams.searchValue},'%')
                    )
                </when>
                <when test="searchField == 'title'">
                    and tc.title like concat('%',#{searchParams.searchValue},'%')
                </when>
                <when test="searchField == 'startUser'">
                    and tcu2.related_user_name like concat('%',#{searchParams.searchValue},'%')
                </when>
                <when test="searchField == 'startDept'">
                    and tcu2.related_user_dept_name like concat('%',#{searchParams.searchValue},'%')
                </when>
                <when test="searchField == 'collaborationUser'">
                    and tc.collaboration_user_name like concat('%',#{searchParams.searchValue},'%')
                </when>
                <when test="searchField == 'collaborationDept' ">
                    and tc.collaboration_dept_name like  concat('%',#{searchParams.searchValue},'%')
                </when>
            </choose>
        </if>
    </sql>

    <sql id="findByFilterParams">
        <bind name="filterParams" value="params.filterParams"/>
        <if test="filterParams != null and filterParams.size() > 0">
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <when test="filterParam.key=='dataSource'">
                        <if test="filterParam.value != null and filterParam.value != ''">
                            and tc.data_source =#{filterParam.value}
                        </if>
                    </when>
                    <when test="filterParam.key=='status'">
                        <if test="filterParam.value != null and filterParam.value != ''">
                            and tc.status =#{filterParam.value}
                        </if>
                    </when>
                    <when test="filterParam.key=='collaborationDept'">
                        <bind name="collaborationDept" value="filterParam.getProcessedValue()"/>
                        <if test="collaborationDept != null and collaborationDept.size() > 0">
                            and tc.collaboration_dept_id in
                            <foreach collection="collaborationDept" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </if>
                    </when>
                    <when test="filterParam.key=='collaborationType'">
                        <bind name="collaborationType" value="filterParam.getProcessedValue()"/>
                        <bind name="collaborationTypeIds"
                              value="@com.trs.police.fight.builder.CollaborationListBuilder@getCollaborationTypeIds(collaborationType)"/>
                        <if test="collaborationTypeIds != null and collaborationTypeIds.size() > 0">
                            and tc.collaboration_type in
                            <foreach collection="collaborationTypeIds" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </if>
                    </when>
                    <when test="filterParam.key=='startDept'">
                        <bind name="startDept" value="filterParam.getProcessedValue()"/>
                        <if test="startDept != null and startDept.size() > 0">
                            and tcu2.related_user_dept_id in
                            <foreach collection="startDept" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                            and tcu2.personnel_type = 1
                        </if>
                    </when>
                    <when test="filterParam.key == 'createTime' and filterParam.getProcessedValue().isAll() == false ">
                        <bind name="createTime" value="filterParam.getProcessedValue()"/>
                        and tc.create_time >= #{createTime.beginTime}
                        and tc.create_time &lt; #{createTime.endTime}
                    </when>
                </choose>
            </foreach>
        </if>
    </sql>

    <sql id="permissionInfo">
        <!-- 数据不是本人的权限 就是按照部门来的 只关联查询发起人 -->
        <if test="permissionInfo.permission != 'SELF'">
            and tcu2.personnel_type = 1
        </if>
        <choose>
            <!-- 数据权限为本人 -->
            <when test="permissionInfo.permission == 'SELF'">
                <choose>
                    <!-- 如果有协作权限，可能是后加的协作权限、所以当时没建立上关联关系、就通过部门来查询 -->
                    <when test="null != params and null != params.hasCollaborationPermission and true == params.hasCollaborationPermission">
                        and (
                            tcu2.related_user_id = #{currentUser.id}
                            or (
                                    tc.collaboration_user_id is null
                                    and tcu2.related_user_dept_id = #{currentUser.dept.id}
                                    and tcu2.personnel_type = 6
                                    and NOT EXISTS (select tcur_child.id from t_collaboration_user_relation tcur_child where tcur_child.collaboration_id = tc.id and tcur_child.related_user_id =  #{currentUser.id} and (tcur_child.personnel_type = 3 or tcur_child.personnel_type = 5))
                        )
                        )
                    </when>
                    <otherwise>
                        and tcu2.related_user_id = #{currentUser.id}
                    </otherwise>
                </choose>
            </when>
            <!-- 数据权限为本部门 -->
            <when test="permissionInfo.permission == 'DEPT'">
                and tcu2.related_user_dept_id = #{currentUser.dept.id}
            </when>
            <!-- 数据权限为本部门及以下 -->
            <when test="permissionInfo.permission == 'DEPT_AND_CHILD'">
                and tcu2.related_user_dept_id in
                <foreach collection="permissionInfo.deptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <!-- 数据权限为超级管理员 -->
            <when test="permissionInfo.permission == 'ALL'">
            </when>
            <otherwise>
                and tcu2.related_user_dept_id = #{currentUser.dept.id}
            </otherwise>
        </choose>
    </sql>
</mapper>