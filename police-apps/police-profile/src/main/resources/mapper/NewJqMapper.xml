<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.mapper.NewJqMapper">

    <resultMap id="jqListMap" type="com.trs.police.profile.domain.vo.JqListVO">
        <id column="id" property="id"/>
        <result column="jjdbh" property="jjdbh"/>
        <result column="relatedRisk" property="relatedRisk"/>
        <result column="score" property="score"/>
        <result column="tel" property="tel"/>
        <result column="source" property="source"/>
        <result column="jjlx" property="jjlx"/>
        <result column="lhlx" property="lhlx"/>
        <result column="content" property="content"/>
        <result column="districtName" property="districtName"/>
        <result column="handleDept" property="handleDept"/>
        <result column="alarmTime" property="alarmTime"/>
        <result column="jiejingTime" property="jiejingTime"/>
        <result column="idNumber" property="idNumber"/>
        <result column="labels" property="labels" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="jjdwmc" property="jjdwmc"/>
        <result column="gxdwmc" property="gxdwmc"/>
        <result column="jd" property="jd"/>
        <result column="wd" property="wd"/>
        <result column="jqlbdm" property="jqlbdm"/>
        <result column="jqlbmc" property="jqlbmc"/>
        <result column="jqlxdm" property="jqlxdm"/>
        <result column="jqlxmc" property="jqlxmc"/>
        <result column="jqxldm" property="jqxldm"/>
        <result column="jqxlmc" property="jqxlmc"/>
        <result column="jqzldm" property="jqzldm"/>
        <result column="jqzlmc" property="jqzlmc"/>
        <result column="bjdz" property="bjdz"/>
        <result column="bjrxb" property="bjrxbdm"/>
        <association property="status" column="{type=jqclztdm_type,code=jqclztdm}"
            select="com.trs.police.common.core.mapper.CommonMapper.getDict"
            javaType="com.trs.police.common.core.vo.CodeNameVO"/>
        <association property="level" column="{type=jqdjdm_type,code=jqdjdm}"
            select="com.trs.police.common.core.mapper.CommonMapper.getDict"
            javaType="com.trs.police.common.core.vo.CodeNameVO"/>
        <association property="source" column="{type=jqlyType,dictDesc=jqlyfs}"
            select="com.trs.police.common.core.mapper.CommonMapper.getDictNameByDesc"
            javaType="java.lang.String"/>
        <association property="jjlx" column="{type=jqlyType,dictDesc=jjlx}"
            select="com.trs.police.common.core.mapper.CommonMapper.getDictNameByDesc"
            javaType="java.lang.String"/>
        <association property="lhlx" column="{type=jqlyType,dictDesc=lhlx}"
            select="com.trs.police.common.core.mapper.CommonMapper.getDictNameByDesc"
            javaType="java.lang.String"/>
        <collection property="category" column="{jqlbdm=jqlbdm,jqlxdm=jqlxdm,jqxldm=jqxldm,jqzldm=jqzldm}"
            select="getCategory"/>
    </resultMap>

    <sql id="jqFileds">
        t1.id as id,
        t1.jjdbh as jjdbh,
        t1.jqclztdm as jqclztdm,
        'sthy_jqzt' as jqclztdm_type,
        t1.jqdjdm as jqdjdm,
        'sthy_jqdj' as jqdjdm_type,
        t1.jqlbdm,
        t1.jqlbmc,
        t1.jqlxdm,
        t1.jqlxmc,
        t1.jqxldm,
        t1.jqxlmc,
        t1.jqzldm,
        t1.jqzlmc,
        t1.jqbq as jqbq,
        t1.SCORE_DETAIL ->>'$.contentScore' as score,
        ifnull((t1.tags ->> '$[*].tagName'),'[]') as labels,
        t1.lxdh as tel,
        'sthy_jjlx' as jqlyType,
        t1.jqlyfs,
        t1.jjlx,
        t1.lhlx,
        ifnull(t1.bjnr,'- -') as content,
        t1.xzqhdm,
        t1.gxdwdm,
        <if test="timeOffsetEnabled">
            DATE_ADD(t1.bjsj , INTERVAL 8 HOUR) as alarmTime,
        </if>
        <if test="!timeOffsetEnabled">
            t1.BJSJ as alarmTime,
        </if>
        t1.JJSJ as jiejingTime,
        t1.BJRZJHM as idNumber,
        t1.jd,
        t1.wd,
        t1.bjdh,
        t1.jqdz,
        t1.bjrmc,
        t1.jjdwmc,
        t1.GXDWMC as gxdwmc,
        t1.BJRXBDM as bjrxb,
        t1.jjlyh,
        t1.jjyxm,
        t1.jjsj
    </sql>

    <sql id="jqCommonFileds">
        t1.id as id,
        t1.jjyxm as jjyxm,
        t1.jjdbh as jjdbh,
        t1.jqclztdm as jqclztdm,
        'sthy_jqzt' as jqclztdm_type,
        t1.jqdjdm as jqdjdm,
        'sthy_jqdj' as jqdjdm_type,
        t1.jqlbdm,
        t1.jqlxdm,
        t1.jqxldm,
        t1.jqzldm,
        t1.jqbq as jqbq,
        t1.SCORE_DETAIL ->>'$.contentScore' as score,
        ifnull((t1.tags ->> '$[*].tagName'),'[]') as labels,
        t1.lxdh as tel,
        'sthy_jjlx' as jqlyType,
        t1.jqlyfs,
        t1.jjlx,
        t1.lhlx,
        ifnull(t1.bjnr,'- -') as content,
        t1.xzqhdm,
        t1.gxdwdm,
        t1.BJSJ as alarmTime,
        t1.BJRZJHM as idNumber,
        t1.jd,
        t1.wd,
        t1.bjdh,
        t1.jqdz,
        t1.bjrmc,
        t1.jjdwmc,
        t1.GXDWMC as gxdwmc,
        t1.JQLBMC as jqlbmc,
        t1.JQLXMC as jqlxmc,
        t1.LX_MJ_XM as lxmjxm,
        t1.BJRZJHM as bjrsfz,
        t1.BJRXBDM as bjrxb,
        t1.jjsj
    </sql>

    <select id="findByBh" resultType="com.trs.police.common.core.vo.profile.JqCommonVO">
        select
            <include refid="jqCommonFileds"></include>
        from
            t_profile_sthy t1
        where jjdbh = #{bh}
    </select>

    <select id="findCommonVoByIds" resultType="com.trs.police.common.core.vo.profile.JqCommonVO">
        select
        <include refid="jqCommonFileds"></include>
        from
            t_profile_sthy t1
        where
            id in
            <foreach collection="ids" item="it" open="(" close=")" separator=",">
                #{it}
            </foreach>
    </select>

    <insert id="insert" parameterType="com.trs.police.profile.domain.entity.JQ">
        insert ignore into t_profile_sthy (JJDBH, XZQHDM, JJDWDM, JJDWMC, JJLX, JQLYFS, LHLX, JJYBH, JJYXM, BJSJ, JJSJ,
                                           JJWCSJ, BJDH, BJDHYHM, BJDHYHDZ, BJRMC, BJRXBDM, LXDH, BJRZJHM, BJDZ, JQDZ,
                                           BJNR, GXDWDM, GXDWMC, JQLBDM, JQLBMC, JQLXDM, JQLXMC, JQXLDM, JQXLMC, JQZLDM,
                                           JQZLMC, ZARS, BJRXZB, BJRYZB, BCJJNR, JQDJDM, JQCLZTDM, JQBQ, WXBS, JJXWH,
                                           JJFS,SOURCE_TYPE,relation_type,JD,WD,jjlyh)
        values (#{jq.jjdbh,jdbcType=VARCHAR}, #{jq.xzqhdm,jdbcType=VARCHAR}, #{jq.jjdwdm,jdbcType=VARCHAR},
                #{jq.jjdwmc,jdbcType=VARCHAR}, #{jq.jjlx,jdbcType=VARCHAR}, #{jq.jqlyfs,jdbcType=VARCHAR},
                #{jq.lhlx,jdbcType=VARCHAR}, #{jq.jjybh,jdbcType=VARCHAR}, #{jq.jjyxm,jdbcType=VARCHAR},
                #{jq.bjsj,jdbcType=TIMESTAMP}, #{jq.jjsj,jdbcType=TIMESTAMP}, #{jq.jjwcsj,jdbcType=TIMESTAMP},
                #{jq.bjdh,jdbcType=VARCHAR}, #{jq.bjdhyhm,jdbcType=VARCHAR}, #{jq.bjdhyhdz,jdbcType=VARCHAR},
                #{jq.bjrmc,jdbcType=VARCHAR}, #{jq.bjrxbdm,jdbcType=VARCHAR}, #{jq.lxdh,jdbcType=VARCHAR},
                #{jq.bjrzjhm,jdbcType=VARCHAR}, #{jq.bjdz,jdbcType=VARCHAR}, #{jq.jqdz,jdbcType=VARCHAR},
                #{jq.bjnr,jdbcType=LONGVARCHAR}, #{jq.gxdwdm,jdbcType=VARCHAR}, #{jq.gxdwmc,jdbcType=VARCHAR},
                #{jq.jqlbdm,jdbcType=VARCHAR}, #{jq.jqlbmc,jdbcType=VARCHAR}, #{jq.jqlxdm,jdbcType=VARCHAR},
                #{jq.jqlxmc,jdbcType=VARCHAR}, #{jq.jqxldm,jdbcType=VARCHAR}, #{jq.jqxlmc,jdbcType=VARCHAR},
                #{jq.jqzldm,jdbcType=VARCHAR}, #{jq.jqzlmc,jdbcType=VARCHAR}, #{jq.zars,jdbcType=INTEGER},
                #{jq.bjrxzb,jdbcType=DOUBLE}, #{jq.bjryzb,jdbcType=DOUBLE}, #{jq.bcjjnr,jdbcType=LONGVARCHAR},
                #{jq.jqdjdm,jdbcType=VARCHAR}, #{jq.jqclztdm,jdbcType=VARCHAR}, #{jq.jqbq,jdbcType=LONGVARCHAR},
                #{jq.wxbs,jdbcType=BOOLEAN}, #{jq.jjxwh,jdbcType=VARCHAR}, #{jq.jjfs,jdbcType=VARCHAR},#{jq.sourceType,jdbcType=INTEGER},#{jq.relationType,jdbcType=VARCHAR},
                #{jq.jd,jdbcType=DOUBLE},#{jq.wd,jdbcType=DOUBLE},#{jq.jjlyh,jdbcType=VARCHAR})
    </insert>

    <select id="getJqPageList" resultMap="jqListMap">
        <bind name="searchParams" value="params.searchParams"/>
        <bind name="filterParams" value="params.filterParams"/>
        <bind name="sortParams" value="params.sortParams"/>
        select
            <include refid="jqFileds"></include>
        from t_profile_sthy t1
        <if test="filterParams.size > 0 and filterParams != null">
            <foreach collection="filterParams" item="param">
                <if test="param.key == 'sensitive' and param.value == 1">
                    left join t_profile_sthy_fkxx fkxx on t1.jjdbh = fkxx.jjdbh
                </if>
            </foreach>
        </if>
        <where>
            <include refid="jqSearchParams"></include>
            <include refid="jqFilterParams"></include>
            <include refid="operateFilterParams"></include>
        </where>
        <choose>
            <when
                test="sortParams != null and @org.apache.commons.lang3.StringUtils@isNotBlank(sortParams.sortField)">
                order by
                <if test="sortParams.sortField == 'alarmTime'">
                    alarmTime
                </if>
                <if test="sortParams.sortField == 'handleTime'">
                    handleTime
                </if>
                <if test="sortParams.sortField == 'time'">
                    t1.bjsj
                </if>
                <if test="sortParams.sortField == 'jiejingTime'">
                    jiejingTime
                </if>
                ${sortParams.getProcessedValue()}, t1.id desc
            </when>
            <otherwise>
                order by alarmTime desc
            </otherwise>
        </choose>
    </select>

    <select id="getCategory" resultType="java.lang.String">
        SELECT ifnull(name, '- -')
        from t_dict
        where type = 'sthy_jq_label'
          and dict_desc in
              (#{jqlbdm}, #{jqlxdm}, #{jqxldm}, #{jqzldm})
        order by field(dict_desc, #{jqlbdm}, #{jqlxdm}, #{jqxldm}, #{jqzldm})
    </select>

    <select id="jqIndex" resultType="com.trs.police.profile.domain.vo.JqIndexVO">
        SELECT
        c.category as indexName,
        10-sum(case when s.id is not null then c.score else 0 end) as score
        FROM
        t_profile_archives_config c
        left join t_dict td on td.type = 'sthy_jq_label' and c.jq_type = td.code
        left join t_profile_sthy s on
                (s.JQLBDM = td.dict_desc
                or s.JQLXDM = td.dict_desc
                or s.JQXLDM = td.dict_desc
                or s.JQZLDM = td.dict_desc)
        where
            c.category is not NULL
        <if test="dto.startTime!=null and dto.startTime !=''">
            and s.bjsj >= #{dto.startTime}
        </if>
        <if test="dto.endTime!=null and dto.endTime !=''">
            and s.bjsj &lt;= #{dto.endTime}
        </if>
        group by c.category
    </select>

    <select id="jqIndexDetail" resultType="com.trs.police.profile.domain.vo.JqIndexDetailVO">
        SELECT
        s.id,
        s.bjnr,
        s.jjdbh,
        -c.score as score
        FROM
        t_profile_archives_config c
        left join t_dict td on td.type = 'sthy_jq_label' and c.jq_type = td.code
        left join t_profile_sthy s on
                (s.JQLBDM = td.dict_desc
                or s.JQLXDM = td.dict_desc
                or s.JQXLDM = td.dict_desc
                or s.JQZLDM = td.dict_desc)
        where
            c.category is not NULL
        <if test="dto.indexName!=null and dto.indexName!=''">
            and c.category = #{dto.indexName}
        </if>
        <if test="dto.startTime!=null and dto.startTime !=''">
            and s.bjsj >= #{dto.startTime}
        </if>
        <if test="dto.endTime!=null and dto.endTime !=''">
            and s.bjsj &lt;= #{dto.endTime}
        </if>
    </select>

    <select id="relatedRiskCount" resultType="com.trs.police.common.core.vo.IdNameCountVO">
        select code name, count(1) count from t_risk_traceable_data_relation
        where
            source_type = 2
        <if test="jjdbhList != null and jjdbhList.size() > 0">
            and code in
            <foreach collection="jjdbhList" item="jjdbh" open="(" close=")" separator=",">
                #{jjdbh}
            </foreach>
        </if>
        group by code
    </select>


    <select id="getJqPageListWithFxxx" resultType="com.trs.police.profile.domain.vo.JqListVO">
        <bind name="searchParams" value="params.searchParams"/>
        <bind name="filterParams" value="params.filterParams"/>
        <bind name="sortParams" value="params.sortParams"/>
        select
            <include refid="jqFileds"></include>
        from t_profile_sthy t1
        join t_profile_sthy_fkxx fkxx on t1.JJDBH = fkxx.jjdbh
        <where>
            <include refid="jqSearchParams"></include>
            <include refid="jqFilterParams"></include>
            <!-- 反馈信息查询条件 -->
            <if test="filterParams.size > 0 and filterParams != null">
                <foreach collection="filterParams" item="param">
                    <choose>
                        <when test="param.key == 'fkPoliceCode'">
                            and fkxx.fkybh = #{param.value}
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
        <choose>
            <when
                    test="sortParams != null and @org.apache.commons.lang3.StringUtils@isNotBlank(sortParams.sortField)">
                order by
                <if test="sortParams.sortField == 'alarmTime'">
                    t1.alarmTime
                </if>
                <if test="sortParams.sortField == 'handleTime'">
                    t1.handleTime
                </if>
                <if test="sortParams.sortField == 'time'">
                    t1.bjsj
                </if>
                ${sortParams.getProcessedValue()}, t1.id desc
            </when>
            <otherwise>
                order by alarmTime desc
            </otherwise>
        </choose>
    </select>

    <select id="findByTelAndIdCard" resultType="com.trs.police.common.core.vo.profile.JqCommonVO">
        select
        <include refid="jqCommonFileds"></include>
        from
        t_profile_sthy t1
        <where>
            (
            t1.BJRZJHM in <foreach collection="idCard" open="(" item="i" separator="," close=")">
            #{i}
            </foreach>
            OR
            t1.BJDH in <foreach collection="tel" open="(" item="i" separator="," close=")">
            #{i}
            </foreach>
            )
        </where>
    </select>

    <select id="findByTelAndIdCardPage" resultType="com.trs.police.common.core.vo.profile.JqCommonVO">
        select
        <include refid="jqCommonFileds"></include>
        from
        t_profile_sthy t1
        <where>
            (
            t1.BJRZJHM in <foreach collection="idCard" open="(" item="i" separator="," close=")">
            #{i}
        </foreach>
            OR
            t1.BJDH in <foreach collection="tel" open="(" item="i" separator="," close=")">
            #{i}
        </foreach>
            )
        </where>
    </select>

    <select id="findByGroupId" resultType="com.trs.police.common.core.vo.profile.JqCommonVO">
        select
            <include refid="jqCommonFileds"></include>
        from
            t_profile_sthy t1 join t_profile_group_jq_relation r on t1.jjdbh = r.jjdbh
        where
            r.group_id = #{groupId}
    </select>

    <sql id="jqSearchParams">
        <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
            <if test="searchParams.searchField == 'fullText'">
                and (
                t1.bjnr like #{pattern,jdbcType=VARCHAR}
                or t1.jjdbh like #{pattern,jdbcType=VARCHAR}
                or t1.lxdh like #{pattern,jdbcType=VARCHAR}
                )
            </if>
            <if test="searchParams.searchField == 'bjnr'">
                and t1.bjnr like #{pattern,jdbcType=VARCHAR}
            </if>
            <if test="searchParams.searchField == 'jjdbh'">
                and t1.jjdbh like #{pattern,jdbcType=VARCHAR}
            </if>
            <if test="searchParams.searchField == 'tel'">
                and t1.lxdh like #{pattern,jdbcType=VARCHAR}
            </if>
            <if test="searchParams.searchField == 'idNumber'">
                and t1.bjrzjhm like #{pattern,jdbcType=VARCHAR}
            </if>
            <if test="searchParams.searchField == 'bjrmc'">
                and t1.bjrmc like #{pattern,jdbcType=VARCHAR}
            </if>
        </if>
    </sql>

    <sql id="jqFilterParams">
        <if test="filterParams.size > 0 and filterParams != null">
            <foreach collection="filterParams" item="param">
                <choose>
                    <when test="param.key == 'sthyly'">
                        and t1.source_type = #{param.value}
                    </when>
                    <when test="param.key == 'tag'">
                        and JSON_CONTAINS(t1.tags, JSON_OBJECT('tagId',#{param.value}))
                    </when>
                    <when test="param.key == 'relatedPersonIdCard'">
                        and BJRZJHM = #{param.value}
                    </when>
                    <when test="param.key == 'category'">
                        and (t1.jqlbdm = #{param.value}
                        or t1.jqlbdm = #{param.value}
                        or t1.jqlbdm = #{param.value}
                        or t1.jqlbdm = #{param.value} )
                    </when>
                    <when test="param.key == 'level'">
                        and t1.jqdjdm=#{param.value}
                    </when>
                    <when test="param.key == 'jjlx'">
                        and t1.jjlx=#{param.value}
                    </when>
                    <when test="param.key == 'jqclztdm' and param.value != null and param.value != ''">
                        and t1.jqclztdm in
                        <foreach collection="param.value.split(',')" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                    <when test="param.key == 'jqlyfs'">
                        and t1.jqlyfs=#{param.value}
                    </when>
                    <when test="param.key == 'lhlx'">
                        and t1.lhlx=#{param.value}
                    </when>
                    <when test="param.key == 'bjsj'">
                        AND (t1.bjsj between #{param.value.beginTime} AND #{param.value.endTime})
                    </when>
                    <when test="param.key == 'jjsj'">
                        AND (t1.JJSJ between #{param.value.beginTime} AND #{param.value.endTime})
                    </when>
                    <when test="param.key == 'userAreaCode'">
                        and t1.XZQHDM like concat('',#{param.value},'%')
                    </when>
                    <when test="param.key == 'permissionDeptCode'">
                        AND t1.jjdwdm in
                        <foreach collection="param.value" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                    <when test="param.key == 'jjdwCode'">
                        AND t1.jjdwdm in
                            <foreach collection="param.value" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                    </when>
<!--                    <when test="param.key == 'invalidJq'">-->
<!--                        AND t1.wxbs = #{param.value}-->
<!--                    </when>-->
                    <!--                        <when test="param.key == 'cjsj'">-->
                    <!--                            AND (t1.cjsj between #{param.value.beginTime}AND #{param.value.endTime})-->
                    <!--                        </when>-->
                    <when test="param.key == 'district'">
                        <bind name="districtList"
                              value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(param.getProcessedValue())"/>
                        AND
                        JSON_OVERLAPS(#{districtList}
                        ,(select JSON_ARRAYAGG(t3.code)
                        from t_district t3
                        where (select concat(t4.path, t4.code, '-') from t_district t4 where
                        t4.code=t1.xzqhdm)
                        like CONCAT('%-', t3.code, '-%')) ) > 0
                    </when>
                    <when test="param.key == 'dept'">
                        <bind name="deptList"
                              value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(param.getProcessedValue())"/>
                        AND
                        JSON_OVERLAPS(#{deptList}
                        ,   (SELECT JSON_ARRAYAGG(d.id)
                        FROM t_dept d
                        WHERE ifnull((select concat(path, d.id, '-') from t_dept d where d.code=t1.gxdwdm), '')
                        LIKE CONCAT('%-', d.id, '-%'))
                        ) > 0
                    </when>
                    <when test="param.key == 'jqlbdm' and param.value != null and param.value != ''">
                        <foreach collection="param.value.split(',')" item="value" separator="or" open="and (" close=")">
                            <choose>
                                <when test="value.length()==2">
                                    t1.jqlbdm = concat(#{value},'000000')
                                </when>
                                <when test="value.length()==4">
                                    t1.jqlxdm = concat(#{value},'0000')
                                </when>
                                <when test="value.length()==6">
                                    t1.jqxldm = concat(#{value},'00')
                                </when>
                                <otherwise>
                                    t1.jqzldm = #{value}
                                </otherwise>
                            </choose>
                        </foreach>
                    </when>
                    <when test="param.key == 'jqTypes' and param.value != null and param.value != ''">
                        <foreach collection="param.value.split(',')" item="value" separator="or" open="and (" close=")" index="index">
                            <bind value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(value)"
                                  name="jqTypeDm"/>
                            <choose>
                                <when test="jqTypeDm.length()==1">
                                    t1.jqlbdm = concat('${jqTypeDm}','0000000')
                                </when>
                                <when test="jqTypeDm.length()==2">
                                    t1.jqlbdm = concat('${jqTypeDm}','000000')
                                </when>
                                <when test="jqTypeDm.length()==3">
                                    t1.jqlxdm = concat('${jqTypeDm}','00000')
                                </when>
                                <when test="jqTypeDm.length()==4">
                                    t1.jqlxdm = concat('${jqTypeDm}','0000')
                                </when>
                                <when test="jqTypeDm.length()==5">
                                    t1.jqxldm = concat('${jqTypeDm}','000')
                                </when>
                                <when test="jqTypeDm.length()==6">
                                    t1.jqxldm = concat('${jqTypeDm}','00')
                                </when>
                                <otherwise>
                                    t1.jqzldm = #{value}
                                </otherwise>
                            </choose>
                        </foreach>
                    </when>
                    <when test="param.key == 'jqlxdm'">
                        and t1.jqlxdm = #{param.value}
                    </when>
                    <when test="param.key == 'sensitive' and param.value == 1">
                        <bind name="sensitiveKey" value="@com.trs.web.builder.util.BeanFactoryHolder@getEnv().getProperty(&quot;profile.jq.sensitiveKey&quot;)"/>
                        <if test="sensitiveKey != null and sensitiveKey!=''">
                            <foreach collection="sensitiveKey.split(',')"  item="item" separator="or" open="and (" close=")">
                                t1.bjnr like concat('%',#{item},'%')
                                or fkxx.jqcljgsm like concat('%',#{item},'%')
                            </foreach>
                        </if>
                    </when>
                    <when test="param.key == 'cjrSfhm'">
                        and t1.cjr_jmsfhm like concat('%',#{param.value},'%')
                    </when>
                    <when test="param.key == 'areaCode' and param.value != null and param.value != ''">
                        and
                        <foreach collection="param.value.split(',')" item="item" open="(" close=")" separator="or">
                            t1.gxdwdm like '${@com.trs.police.common.core.utils.StringUtil@getShortAreaCode(item)}%'
                        </foreach>
                    </when>
                    <when test="param.key == 'jqId'">
                        and t1.id in
                        <foreach collection="param.value.split(',')" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                </choose>
            </foreach>
        </if>
    </sql>

    <sql id="operateFilterParams">
        <if test="operateFilterParams!=null and operateFilterParams.size>0">
            <foreach collection="operateFilterParams" item="param" separator="or" open="and (" close=")">
                <choose>
                    <when test="param.key == 'content'">
                        <choose>
                            <when test="param.type == 'Like'">
                                <foreach collection="param.value" item="item" separator="or">
                                    t1.bjnr like concat('%',#{item},'%')
                                </foreach>
                            </when>
                            <when test="param.type == 'NotLike'">
                                <foreach collection="param.value" item="item" separator="or">
                                    t1.bjnr not like concat('%',#{item},'%')
                                </foreach>
                            </when>
                            <when test="param.type == 'isNull'">
                                    t1.bjnr = '' or t1.bjnr is null
                            </when>
                            <when test="param.type == 'isNotNull'">
                                    t1.bjnr != '' or t1.bjnr is not null
                            </when>
                        </choose>
                    </when>
                    <when test="param.key == 'category'">
                        <choose>
                            <when test="param.type == 'Equal'">
                                <foreach collection="param.value" item="item" separator="or">
                                    t1.jqlbdm = #{item} or
                                    t1.jqlxdm = #{item} or
                                    t1.jqxldm = #{item} or
                                    t1.jqzldm = #{item}
                                </foreach>
                            </when>
                            <when test="param.type == 'NotEqual'">
                                <foreach collection="param.value" item="item" separator="or">
                                    t1.jqlbdm != #{item} or
                                    t1.jqlxdm != #{item} or
                                    t1.jqxldm != #{item} or
                                    t1.jqzldm != #{item}
                                </foreach>
                            </when>
                        </choose>
                    </when>
                </choose>
            </foreach>
        </if>
    </sql>

    <update id="updateSyncInfo">
        update t_profile_sthy set cjr_jmsfhm = #{jq.cjrJmsfhm}, cjr_xm = #{jq.cjrXm} where id = #{id}
    </update>

    <insert id="insertDsr">
        INSERT IGNORE INTO t_profile_sthy_jqdsrxx
        (
        XZQHDM,XZQHMC,JQDSRDBH,JJDBH,PJDBH,FKDBH,JQDSRXM,XBDM,ZJDM,ZJHM,SF,SFZDRY,ZDRYSX,HJXZQH,HJXZ,XZXZQH,XZXZ,GZDW,ZY,
        LXDH,QTZJ,SSWPXX,RKSJ,GXSJ,CSRQ,BJJQDSRDBM,BJJJJDBM,BJPJDBM,BJFKDBM
        )
        values (
        #{data.xzqhdm},
        #{data.xzqhmc},
        #{data.jqdsrdbh},
        #{data.jjdbh},
        #{data.pjdbh},
        #{data.fkdbh},
        #{data.jqdsrxm},
        #{data.xbdm},
        #{data.zjdm},
        #{data.zjhm},
        #{data.sf},
        #{data.sfzdry},
        #{data.zdrysx},
        #{data.hjxzqh},
        #{data.hjxz},
        #{data.xzxzqh},
        #{data.xzxz},
        #{data.gzdw},
        #{data.zy},
        #{data.lxdh},
        #{data.qtzj},
        #{data.sswpxx},
        #{data.rksj},
        #{data.gxsj},
        #{data.csrq},
        #{data.bjjqdsrdbm},
        #{data.bjjjjdbm},
        #{data.bjpjdbm},
        #{data.bjfkdbm}
        )
    </insert>
</mapper>