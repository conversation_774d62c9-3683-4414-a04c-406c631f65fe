# 职级晋升配置示例
police:
  promotion:
    # 职级晋升映射关系配置
    # key: 当前职级代码, value: 目标晋升职级代码
    rank-mapping:
      # 执法勤务警员职务晋升路径
      52: 51  # 二级警员 -> 一级警员
      51: 44  # 一级警员 -> 四级警长
      44: 43  # 四级警长 -> 三级警长
      43: 42  # 三级警长 -> 二级警长
      42: 41  # 二级警长 -> 一级警长
      41: 31  # 一级警长 -> 四级高级警长
      31: 30  # 四级高级警长 -> 三级高级警长
      30: 29  # 三级高级警长 -> 二级高级警长
      29: 28  # 二级高级警长 -> 一级高级警长

      # 技术职务晋升路径
      25: 24  # 警务技术员 -> 警务技术四级主管
      24: 23  # 警务技术四级主管 -> 警务技术三级主管
      23: 22  # 警务技术三级主管 -> 警务技术二级主管
      22: 21  # 警务技术二级主管 -> 警务技术一级主管
      21: 14  # 警务技术一级主管 -> 警务技术四级主任
      14: 13  # 警务技术四级主任 -> 警务技术三级主任
      13: 12  # 警务技术三级主任 -> 警务技术二级主任
      12: 11  # 警务技术二级主任 -> 警务技术一级主任

    # 职级对应的策略配置
    # key: 职级类别, value: Map<目标职级代码, 策略类名列表>
    rank-strategies:
      # 执法勤务警员职务晋升
      执法勤务警员职务晋升:
        # 高级警长系列（需要年满50周岁）
        28:  # 一级高级警长
          - "RankTenurePromotionStrategy"      # 任职年限要求
          - "ViolationPromotionStrategy"       # 违纪违规检查
          - "SeniorRankAgePromotionStrategy"   # 年满50周岁要求
        29:  # 二级高级警长
          - "RankTenurePromotionStrategy"      # 任职年限要求
          - "ViolationPromotionStrategy"       # 违纪违规检查
          - "SeniorRankAgePromotionStrategy"   # 年满50周岁要求
        30:  # 三级高级警长
          - "RankTenurePromotionStrategy"      # 任职年限要求
          - "ViolationPromotionStrategy"       # 违纪违规检查
          - "SeniorRankAgePromotionStrategy"   # 年满50周岁要求
        31:  # 四级高级警长
          - "RankTenurePromotionStrategy"      # 任职年限要求
          - "ViolationPromotionStrategy"       # 违纪违规检查
          - "SeniorRankAgePromotionStrategy"   # 年满50周岁要求

# 日志配置
logging:
  level:
    com.trs.police.profile.strategy.promotion: DEBUG
    com.trs.police.profile.service.impl.PolicePromotionServiceImpl: INFO
