# 职级晋升配置示例
police:
  promotion:
    # 职级晋升映射关系配置
    # key: 当前职级代码, value: 目标晋升职级代码
    rank-mapping:
      30: 29  # 三级高级警长 -> 二级高级警长
      29: 28  # 二级高级警长 -> 一级高级警长
      28: 27  # 一级高级警长 -> 三级警务技术
      27: 26  # 三级警务技术 -> 二级警务技术
      26: 25  # 二级警务技术 -> 一级警务技术
      25: 24  # 一级警务技术 -> 四级高级警长
      24: 23  # 四级高级警长 -> 三级高级警长
      # 可以根据实际职级体系继续添加

    # 默认晋升规则配置
    default-rules:
      # 工作年限规则
      - rule-id: "work_years_001"
        rule-name: "工作年限要求"
        description: "晋升需要满足最低工作年限要求"
        rule-type: "WORK_YEARS"
        category: "basic"
        enabled: true
        priority: 1
        weight: 1.0
        required: true
        rule-config:
          required-years: 5  # 要求工作年限（年）
          required-months: 0 # 要求工作年限（月）

      # 年龄限制规则
      - rule-id: "age_limit_001"
        rule-name: "年龄限制"
        description: "晋升年龄限制要求"
        rule-type: "AGE_LIMIT"
        category: "basic"
        enabled: true
        priority: 2
        weight: 1.0
        required: true
        rule-config:
          min-age: 25      # 最低年龄
          max-age: 55      # 最高年龄
          result-type: "FORBIDDEN"  # 不符合时的结果类型

      # 违纪违规规则
      - rule-id: "violation_001"
        rule-name: "违纪违规限制"
        description: "存在违纪违规记录的限制晋升"
        rule-type: "VIOLATION"
        category: "discipline"
        enabled: true
        priority: 3
        weight: 1.0
        required: true
        rule-config:
          max-violations: 0     # 允许的最大违纪违规次数
          violation-type: "ALL" # 违纪违规类型（ALL、MAJOR、MINOR）
          result-type: "FORBIDDEN"  # 不符合时的结果类型

      # 教育背景规则
      - rule-id: "education_001"
        rule-name: "教育背景要求"
        description: "晋升需要满足最低学历要求"
        rule-type: "EDUCATION"
        category: "background"
        enabled: true
        priority: 4
        weight: 0.8
        required: false
        rule-config:
          min-education-level: "BACHELOR"  # 最低学历要求
          result-type: "NOT_QUALIFIED"

      # 立功受奖规则
      - rule-id: "awards_001"
        rule-name: "立功受奖加分"
        description: "立功受奖记录加分项"
        rule-type: "AWARDS"
        category: "performance"
        enabled: true
        priority: 5
        weight: 0.5
        required: false
        rule-config:
          min-awards: 1  # 最低立功受奖次数
          result-type: "QUALIFIED"

    # 职级特定规则配置（可以为不同职级配置不同的规则）
    rank-specific-rules:
      # 高级警长系列的特殊规则
      "29,28,27":  # 二级、一级高级警长，三级警务技术
        - rule-id: "senior_rank_001"
          rule-name: "高级职级特殊要求"
          description: "高级职级需要更严格的要求"
          rule-type: "WORK_YEARS"
          category: "basic"
          enabled: true
          priority: 1
          weight: 1.0
          required: true
          rule-config:
            required-years: 8  # 高级职级要求更长工作年限
            required-months: 0

# 日志配置
logging:
  level:
    com.trs.police.profile.strategy.promotion: DEBUG
    com.trs.police.profile.service.impl.PolicePromotionServiceImpl: INFO
