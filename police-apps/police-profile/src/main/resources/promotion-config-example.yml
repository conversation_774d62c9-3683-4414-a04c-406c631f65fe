# 职级晋升配置示例
police:
  promotion:
    # 职级晋升映射关系配置
    # key: 当前职级代码, value: 目标晋升职级代码
    rank-mapping:
      30: 29  # 三级高级警长 -> 二级高级警长
      29: 28  # 二级高级警长 -> 一级高级警长
      28: 27  # 一级高级警长 -> 三级警务技术
      27: 26  # 三级警务技术 -> 二级警务技术
      26: 25  # 二级警务技术 -> 一级警务技术
      25: 24  # 一级警务技术 -> 四级高级警长
      24: 23  # 四级高级警长 -> 三级高级警长
      # 可以根据实际职级体系继续添加

    # 职级对应的策略配置
    # key: 目标职级代码, value: 策略类名列表
    rank-strategies:
      29:  # 二级高级警长
        - "WorkYearsPromotionStrategy"
        - "ViolationPromotionStrategy"
        - "AgePromotionStrategy"
      28:  # 一级高级警长
        - "WorkYearsPromotionStrategy"
        - "ViolationPromotionStrategy"
        - "AgePromotionStrategy"
      27:  # 三级警务技术
        - "WorkYearsPromotionStrategy"
        - "ViolationPromotionStrategy"
        - "AgePromotionStrategy"
      26:  # 二级警务技术
        - "WorkYearsPromotionStrategy"
        - "ViolationPromotionStrategy"
        - "AgePromotionStrategy"
      25:  # 一级警务技术
        - "WorkYearsPromotionStrategy"
        - "ViolationPromotionStrategy"
        - "AgePromotionStrategy"

# 日志配置
logging:
  level:
    com.trs.police.profile.strategy.promotion: DEBUG
    com.trs.police.profile.service.impl.PolicePromotionServiceImpl: INFO
