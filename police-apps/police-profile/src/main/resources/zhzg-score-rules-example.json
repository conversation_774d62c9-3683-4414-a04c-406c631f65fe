{"description": "智慧政工积分规则配置示例", "version": "1.0", "rules": [{"id": 1, "name": "基础素质", "description": "基础素质评价，包括工作年限、教育背景等", "parentId": null, "score": 50, "isLeaf": false, "ruleType": "BASE_QUALITY", "enabled": true, "children": [{"id": 11, "name": "工作年限", "description": "工作年限每满1个月计0.1分", "parentId": 1, "score": 30, "isLeaf": true, "ruleType": "WORK_YEARS", "enabled": true}, {"id": 12, "name": "教育经历", "description": "根据最高学历计分：博士20分，硕士15分，本科10分，专科5分", "parentId": 1, "score": 20, "isLeaf": true, "ruleType": "EDUCATION", "enabled": true}]}, {"id": 2, "name": "表现评价", "description": "工作表现评价，包括立功受奖、违纪违规等", "parentId": null, "score": 30, "isLeaf": false, "ruleType": "PERFORMANCE", "enabled": true, "children": [{"id": 21, "name": "立功受奖", "description": "每个立功受奖记录计5分", "parentId": 2, "score": 25, "isLeaf": true, "ruleType": "AWARDS", "enabled": true}, {"id": 22, "name": "违纪违规", "description": "每个违纪违规记录扣10分", "parentId": 2, "score": -50, "isLeaf": true, "ruleType": "VIOLATION", "enabled": true}]}], "scoreLevel": {"excellent": {"threshold": 90, "name": "优秀"}, "good": {"threshold": 80, "name": "良好"}, "average": {"threshold": 70, "name": "中等"}, "pass": {"threshold": 60, "name": "及格"}}}