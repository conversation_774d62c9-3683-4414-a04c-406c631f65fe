package com.trs.police.profile.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 警员职级枚举
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Getter
@AllArgsConstructor
public enum PoliceRankEnum {

    // 执法勤务警员职务系列 (police_zj_group_one)
    LAW_ENFORCEMENT_SENIOR_1(PoliceRankCategory.LAW_ENFORCEMENT, 1, "一级高级警长"),
    LAW_ENFORCEMENT_SENIOR_2(PoliceRankCategory.LAW_ENFORCEMENT, 2, "二级高级警长"),
    LAW_ENFORCEMENT_SENIOR_3(PoliceRankCategory.LAW_ENFORCEMENT, 3, "三级高级警长"),
    LAW_ENFORCEMENT_SENIOR_4(PoliceRankCategory.LAW_ENFORCEMENT, 4, "四级高级警长"),
    LAW_ENFORCEMENT_SERGEANT_1(PoliceRankCategory.LAW_ENFORCEMENT, 5, "一级警长"),
    LAW_ENFORCEMENT_SERGEANT_2(PoliceRankCategory.LAW_ENFORCEMENT, 6, "二级警长"),
    LAW_ENFORCEMENT_SERGEANT_3(PoliceRankCategory.LAW_ENFORCEMENT, 7, "三级警长"),
    LAW_ENFORCEMENT_SERGEANT_4(PoliceRankCategory.LAW_ENFORCEMENT, 8, "四级警长"),
    LAW_ENFORCEMENT_OFFICER_1(PoliceRankCategory.LAW_ENFORCEMENT, 9, "一级警员"),
    LAW_ENFORCEMENT_OFFICER_2(PoliceRankCategory.LAW_ENFORCEMENT, 10, "二级警员"),
    LAW_ENFORCEMENT_GENERAL(PoliceRankCategory.LAW_ENFORCEMENT, 100, "执法勤务警员职务"),

    // 警务技术职务系列 (police_zj_group_two)
    TECHNICAL_DIRECTOR_1(PoliceRankCategory.TECHNICAL, 1, "一级主任"),
    TECHNICAL_DIRECTOR_2(PoliceRankCategory.TECHNICAL, 2, "二级主任"),
    TECHNICAL_DIRECTOR_3(PoliceRankCategory.TECHNICAL, 3, "三级主任"),
    TECHNICAL_DIRECTOR_4(PoliceRankCategory.TECHNICAL, 4, "四级主任"),
    TECHNICAL_SUPERVISOR_1(PoliceRankCategory.TECHNICAL, 5, "一级主管"),
    TECHNICAL_SUPERVISOR_2(PoliceRankCategory.TECHNICAL, 6, "二级主管"),
    TECHNICAL_SUPERVISOR_3(PoliceRankCategory.TECHNICAL, 7, "三级主管"),
    TECHNICAL_SUPERVISOR_4(PoliceRankCategory.TECHNICAL, 8, "四级主管"),
    TECHNICAL_OFFICER(PoliceRankCategory.TECHNICAL, 9, "警务技术员"),
    TECHNICAL_GENERAL(PoliceRankCategory.TECHNICAL, 101, "警务技术职务");

    /**
     * 职级类别
     */
    private final PoliceRankCategory category;

    /**
     * 职级代码
     */
    private final Integer code;

    /**
     * 职级名称
     */
    private final String name;

    /**
     * 职级类别枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PoliceRankCategory {
        LAW_ENFORCEMENT("police_zj_group_one", "执法勤务警员职务晋升"),
        TECHNICAL("police_zj_group_two", "技术职务晋升");

        /**
         * 类别代码
         */
        private final String categoryCode;

        /**
         * 类别名称
         */
        private final String categoryName;
    }

    /**
     * 根据职级代码和类别获取职级枚举
     *
     * @param category 职级类别
     * @param code     职级代码
     * @return 职级枚举
     */
    public static PoliceRankEnum getByCode(PoliceRankCategory category, Integer code) {
        if (category == null || code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(rank -> rank.getCategory() == category && rank.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据职级代码获取职级枚举（自动判断类别）
     *
     * @param code 职级代码
     * @return 职级枚举列表（可能有多个相同代码的不同类别）
     */
    public static List<PoliceRankEnum> getByCode(Integer code) {
        if (code == null) {
            return List.of();
        }
        return Arrays.stream(values())
                .filter(rank -> rank.getCode().equals(code))
                .collect(Collectors.toList());
    }

    /**
     * 根据职级名称获取职级枚举
     *
     * @param name 职级名称
     * @return 职级枚举
     */
    public static PoliceRankEnum getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        return Arrays.stream(values())
                .filter(rank -> rank.getName().equals(name.trim()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据类别获取所有职级
     *
     * @param category 职级类别
     * @return 职级列表
     */
    public static List<PoliceRankEnum> getByCategory(PoliceRankCategory category) {
        if (category == null) {
            return List.of();
        }
        return Arrays.stream(values())
                .filter(rank -> rank.getCategory() == category)
                .collect(Collectors.toList());
    }

    /**
     * 获取执法勤务警员职务系列
     *
     * @return 执法勤务职级列表
     */
    public static List<PoliceRankEnum> getLawEnforcementRanks() {
        return getByCategory(PoliceRankCategory.LAW_ENFORCEMENT);
    }

    /**
     * 获取警务技术职务系列
     *
     * @return 技术职务职级列表
     */
    public static List<PoliceRankEnum> getTechnicalRanks() {
        return getByCategory(PoliceRankCategory.TECHNICAL);
    }

    /**
     * 判断是否为高级警长系列（需要年满50周岁）
     *
     * @return 是否为高级警长
     */
    public boolean isSeniorRank() {
        return this.category == PoliceRankCategory.LAW_ENFORCEMENT && 
               this.code >= 1 && this.code <= 4;
    }

    /**
     * 判断是否为主任系列
     *
     * @return 是否为主任系列
     */
    public boolean isDirectorRank() {
        return this.category == PoliceRankCategory.TECHNICAL && 
               this.code >= 1 && this.code <= 4;
    }

    /**
     * 判断是否为主管系列
     *
     * @return 是否为主管系列
     */
    public boolean isSupervisorRank() {
        return this.category == PoliceRankCategory.TECHNICAL && 
               this.code >= 5 && this.code <= 8;
    }

    /**
     * 获取职级的完整描述
     *
     * @return 完整描述
     */
    public String getFullDescription() {
        return String.format("%s - %s (%d)", category.getCategoryName(), name, code);
    }

    /**
     * 获取所有职级的代码列表
     *
     * @return 代码列表
     */
    public static List<Integer> getAllCodes() {
        return Arrays.stream(values())
                .map(PoliceRankEnum::getCode)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    /**
     * 获取所有职级的名称列表
     *
     * @return 名称列表
     */
    public static List<String> getAllNames() {
        return Arrays.stream(values())
                .map(PoliceRankEnum::getName)
                .distinct()
                .collect(Collectors.toList());
    }
}
