# 警员职级晋升状态判断功能

## 概述

警员职级晋升状态判断功能是一个基于策略模式的可扩展规则引擎，用于评估警员是否符合职级晋升条件。系统支持四种互斥的晋升状态，并按照优先级进行判断。

## 核心特性

1. **四种晋升状态**：有资格、无资格、不得晋升、暂缓晋升
2. **优先级机制**：不得晋升 > 暂缓晋升 > 有资格/无资格
3. **可扩展规则**：基于策略模式，支持动态添加新的规则类型
4. **职级映射**：支持配置化的职级晋升关系映射
5. **批量处理**：支持单个和批量评估
6. **详细记录**：记录每个规则的评估过程和结果

## 系统架构

### 核心组件

1. **PromotionStatusEnum**：晋升状态枚举，定义四种状态及优先级
2. **PromotionRuleStrategy**：规则策略接口
3. **PromotionRuleStrategyFactory**：策略工厂，负责策略注册和获取
4. **PolicePromotionService**：晋升评估核心服务
5. **PromotionRankMappingConfig**：职级映射配置
6. **PolicePromotionController**：REST API控制器

### 数据模型

1. **PromotionEvaluationRequest**：晋升评估请求DTO
2. **PromotionEvaluationResult**：晋升评估结果VO
3. **PromotionRuleDetailVO**：规则评估详情VO

## 使用指南

### 1. 基本使用

#### 评估单个警员的晋升资格

```java
@Autowired
private PolicePromotionService promotionService;

// 根据警员ID评估
PromotionEvaluationResult result = promotionService.evaluatePromotionById(policeId);

// 根据身份证号评估
PromotionEvaluationResult result = promotionService.evaluatePromotionByIdNumber("身份证号");

// 自定义评估请求
PromotionEvaluationRequest request = PromotionEvaluationRequest.builder()
    .policeId(policeId)
    .currentRankCode(30)  // 当前职级：三级高级警长
    .targetRankCode(29)   // 目标职级：二级高级警长
    .build();
PromotionEvaluationResult result = promotionService.evaluatePromotion(request);
```

#### 批量评估

```java
List<PromotionEvaluationRequest> requests = Arrays.asList(
    PromotionEvaluationRequest.builder().policeId(1L).build(),
    PromotionEvaluationRequest.builder().policeId(2L).build()
);
List<PromotionEvaluationResult> results = promotionService.batchEvaluatePromotion(requests);
```

#### 更新晋升状态

```java
// 更新单个警员的晋升状态
boolean success = promotionService.updatePromotionStatus(policeId, PromotionStatusEnum.QUALIFIED.getCode());

// 批量更新
List<Long> policeIds = Arrays.asList(1L, 2L, 3L);
int successCount = promotionService.batchUpdatePromotionStatus(policeIds, PromotionStatusEnum.QUALIFIED.getCode());
```

### 2. REST API 使用

#### 评估晋升资格

```bash
# 根据警员ID评估
GET /api/police/promotion/evaluate/{policeId}

# 根据身份证号评估
GET /api/police/promotion/evaluate/idNumber/{idNumber}

# 自定义评估
POST /api/police/promotion/evaluate
Content-Type: application/json
{
  "policeId": 1,
  "currentRankCode": 30,
  "targetRankCode": 29
}
```

#### 更新晋升状态

```bash
# 更新单个警员状态
PUT /api/police/promotion/status/{policeId}?promotionStatus=1

# 批量更新状态
PUT /api/police/promotion/status/batch?policeIds=1,2,3&promotionStatus=1
```

#### 批量计算任务

```bash
# 计算所有警员的晋升状态
POST /api/police/promotion/calculate/all

# 计算指定部门警员的晋升状态
POST /api/police/promotion/calculate/dept/{deptId}
```

### 3. 创建自定义规则策略

实现 `PromotionRuleStrategy` 接口：

```java
@Component
public class CustomPromotionStrategy implements PromotionRuleStrategy {

    @Override
    public PromotionRuleDetailVO evaluateRule(ProfilePolice police, Integer targetRankCode) {
        // 实现具体的规则评估逻辑
        return PromotionRuleDetailVO.builder()
                .ruleName(getStrategyName())
                .description(getDescription())
                .ruleType(getSupportedRuleType())
                .ruleResult(PromotionStatusEnum.QUALIFIED)
                .isHit(true)
                .calculateDescription("自定义规则评估通过")
                .required(false)
                .build();
    }

    @Override
    public String getStrategyName() {
        return "自定义规则";
    }

    @Override
    public String getSupportedRuleType() {
        return "CUSTOM_RULE";
    }
}
```

### 4. 配置职级映射和策略关系

在 `application.yml` 中配置：

```yaml
police:
  promotion:
    # 职级晋升映射关系
    rank-mapping:
      30: 29  # 三级高级警长 -> 二级高级警长
      29: 28  # 二级高级警长 -> 一级高级警长
      28: 27  # 一级高级警长 -> 三级警务技术

    # 职级对应的策略配置
    rank-strategies:
      29:  # 二级高级警长
        - "WorkYearsPromotionStrategy"
        - "ViolationPromotionStrategy"
        - "AgePromotionStrategy"
      28:  # 一级高级警长
        - "WorkYearsPromotionStrategy"
        - "ViolationPromotionStrategy"
        - "AgePromotionStrategy"
        - "CustomPromotionStrategy"  # 可以添加自定义策略
```

## 已实现的规则策略

### 1. 工作年限规则 (WorkYearsPromotionStrategy)
- **规则类型**：WORK_YEARS
- **评估逻辑**：检查警员的工作年限是否满足要求
- **必要条件**：是
- **配置方式**：在策略类中根据目标职级硬编码配置

### 2. 违纪违规规则 (ViolationPromotionStrategy)
- **规则类型**：VIOLATION
- **评估逻辑**：检查警员的违纪违规记录
- **必要条件**：是
- **配置方式**：在策略类中硬编码，不允许有违纪违规记录

### 3. 年龄限制规则 (AgePromotionStrategy)
- **规则类型**：AGE_LIMIT
- **评估逻辑**：检查警员年龄是否在允许范围内
- **必要条件**：否
- **配置方式**：在策略类中硬编码，25-55岁范围

## 扩展指南

### 添加新的规则策略

1. 创建实现 `PromotionRuleStrategy` 接口的类
2. 使用 `@Component` 注解标记为Spring组件
3. 实现 `evaluateRule` 方法定义具体的评估逻辑
4. 定义唯一的策略名称和规则类型
5. 在配置文件的 `rank-strategies` 中添加策略类名

### 自定义职级映射和策略配置

1. 在配置文件中添加新的职级映射关系
2. 在配置文件中为职级配置对应的策略类名列表
3. 支持运行时动态添加映射关系和策略配置

## 注意事项

1. **优先级机制**：系统严格按照"不得晋升 > 暂缓晋升 > 有资格/无资格"的优先级进行判断
2. **必要条件**：标记为 `required=true` 的规则如果不满足，会直接影响最终结果
3. **数据完整性**：确保警员档案中的关键信息（如出生日期、参加工作日期等）完整
4. **性能考虑**：批量处理时建议分批进行，避免一次性处理过多数据
5. **配置管理**：策略类名必须与实际的Spring Bean名称一致
6. **扩展性**：新增规则策略时只需要实现接口并在配置文件中添加即可

## 数据库字段

系统会更新 `t_profile_police` 表的 `promotion_status` 字段：
- 1：有资格
- 2：无资格  
- 3：不得晋升
- 4：暂缓晋升
