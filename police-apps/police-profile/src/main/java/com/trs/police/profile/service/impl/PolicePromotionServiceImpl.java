package com.trs.police.profile.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trs.police.profile.config.PromotionRankMappingConfig;
import com.trs.police.profile.constant.PromotionStatusEnum;
import com.trs.police.profile.domain.dto.promotion.PromotionEvaluationRequest;

import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionEvaluationResult;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.factory.promotion.PromotionRuleStrategyFactory;
import com.trs.police.profile.mapper.zhzg.ProfilePoliceMapper;
import com.trs.police.profile.service.PolicePromotionService;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 警员职级晋升服务实现
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Service
public class PolicePromotionServiceImpl implements PolicePromotionService {

    @Autowired
    private ProfilePoliceMapper profilePoliceMapper;

    @Autowired
    private PromotionRuleStrategyFactory strategyFactory;

    @Autowired
    private PromotionRankMappingConfig rankMappingConfig;

    @Override
    public PromotionEvaluationResult evaluatePromotion(PromotionEvaluationRequest request) {
        log.info("开始评估警员晋升资格，警员ID：{}", request.getPoliceId());

        try {
            // 验证请求参数
            if (!validateRequest(request)) {
                return buildErrorResult(request, "请求参数验证失败");
            }

            // 查询警员档案
            ProfilePolice police = profilePoliceMapper.selectById(request.getPoliceId());
            if (police == null) {
                return buildErrorResult(request, "未找到警员档案信息");
            }

            // 确定目标晋升职级
            Integer targetRankCode = determineTargetRank(police, request);
            if (targetRankCode == null) {
                return buildErrorResult(request, "无法确定目标晋升职级");
            }

            // 获取晋升策略
            List<PromotionRuleStrategy> strategies = getPromotionStrategies(targetRankCode);
            if (CollectionUtils.isEmpty(strategies)) {
                return buildErrorResult(request, "未找到适用的晋升策略");
            }

            // 执行规则评估
            List<PromotionRuleDetailVO> ruleDetails = evaluateStrategies(police, targetRankCode, strategies);

            // 计算最终晋升状态
            PromotionStatusEnum finalStatus = calculateFinalStatus(ruleDetails);

            // 构建评估结果
            return buildSuccessResult(police, targetRankCode, finalStatus, ruleDetails);

        } catch (Exception e) {
            log.error("评估警员晋升资格失败，警员ID：{}，错误：{}", request.getPoliceId(), e.getMessage(), e);
            return buildErrorResult(request, "评估失败：" + e.getMessage());
        }
    }

    @Override
    public List<PromotionEvaluationResult> batchEvaluatePromotion(List<PromotionEvaluationRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return new ArrayList<>();
        }

        return requests.stream()
                .map(this::evaluatePromotion)
                .collect(Collectors.toList());
    }

    @Override
    public PromotionEvaluationResult evaluatePromotionById(Long policeId) {
        PromotionEvaluationRequest request = PromotionEvaluationRequest.builder()
                .policeId(policeId)
                .build();
        return evaluatePromotion(request);
    }

    @Override
    public PromotionEvaluationResult evaluatePromotionByIdNumber(String idNumber) {
        if (!StringUtils.hasText(idNumber)) {
            return PromotionEvaluationResult.builder()
                    .success(false)
                    .errorMessage("身份证号不能为空")
                    .evaluationTime(LocalDateTime.now())
                    .build();
        }

        // 根据身份证号查询警员档案
        LambdaQueryWrapper<ProfilePolice> queryWrapper = new LambdaQueryWrapper<ProfilePolice>()
                .eq(ProfilePolice::getIdNumber, idNumber)
                .eq(ProfilePolice::getDeleted, false);

        ProfilePolice police = profilePoliceMapper.selectOne(queryWrapper);
        if (police == null) {
            return PromotionEvaluationResult.builder()
                    .idNumber(idNumber)
                    .success(false)
                    .errorMessage("未找到对应的警员档案")
                    .evaluationTime(LocalDateTime.now())
                    .build();
        }

        return evaluatePromotionById(police.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePromotionStatus(Long policeId, Integer promotionStatus) {
        if (policeId == null || promotionStatus == null) {
            return false;
        }

        ProfilePolice police = new ProfilePolice();
        police.setId(policeId);
        police.setPromotionStatus(promotionStatus);

        int updateCount = profilePoliceMapper.updateById(police);
        boolean success = updateCount > 0;

        log.info("更新警员晋升状态，警员ID：{}，状态：{}，结果：{}", policeId, promotionStatus, success ? "成功" : "失败");
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdatePromotionStatus(List<Long> policeIds, Integer promotionStatus) {
        if (CollectionUtils.isEmpty(policeIds) || promotionStatus == null) {
            return 0;
        }

        int successCount = 0;
        for (Long policeId : policeIds) {
            if (updatePromotionStatus(policeId, promotionStatus)) {
                successCount++;
            }
        }

        log.info("批量更新警员晋升状态完成，总数：{}，成功：{}，状态：{}", 
                policeIds.size(), successCount, promotionStatus);
        return successCount;
    }

    @Override
    public int executePromotionStatusCalculation() {
        log.info("开始执行晋升状态批量计算任务");

        // 查询所有未删除的警员档案
        LambdaQueryWrapper<ProfilePolice> queryWrapper = new LambdaQueryWrapper<ProfilePolice>()
                .eq(ProfilePolice::getDeleted, false);

        List<ProfilePolice> policeList = profilePoliceMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(policeList)) {
            log.info("未找到需要计算的警员档案");
            return 0;
        }

        int processedCount = 0;
        for (ProfilePolice police : policeList) {
            try {
                PromotionEvaluationResult result = evaluatePromotionById(police.getId());
                if (result.getSuccess() && result.getPromotionStatus() != null) {
                    updatePromotionStatus(police.getId(), result.getPromotionStatusCode());
                    processedCount++;
                }
            } catch (Exception e) {
                log.error("计算警员晋升状态失败，警员ID：{}，错误：{}", police.getId(), e.getMessage(), e);
            }
        }

        log.info("晋升状态批量计算任务完成，总数：{}，处理成功：{}", policeList.size(), processedCount);
        return processedCount;
    }

    @Override
    public int calculatePromotionStatusByDept(Long deptId) {
        if (deptId == null) {
            return 0;
        }

        log.info("开始计算部门警员晋升状态，部门ID：{}", deptId);

        // TODO: 这里需要根据实际的部门关联关系查询警员
        // 暂时查询所有警员，实际实现时需要添加部门过滤条件
        LambdaQueryWrapper<ProfilePolice> queryWrapper = new LambdaQueryWrapper<ProfilePolice>()
                .eq(ProfilePolice::getDeleted, false);
        // .like(ProfilePolice::getDeptIds, deptId.toString()); // 需要根据实际字段调整

        List<ProfilePolice> policeList = profilePoliceMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(policeList)) {
            log.info("部门{}下未找到警员档案", deptId);
            return 0;
        }

        int processedCount = 0;
        for (ProfilePolice police : policeList) {
            try {
                PromotionEvaluationResult result = evaluatePromotionById(police.getId());
                if (result.getSuccess() && result.getPromotionStatus() != null) {
                    updatePromotionStatus(police.getId(), result.getPromotionStatusCode());
                    processedCount++;
                }
            } catch (Exception e) {
                log.error("计算警员晋升状态失败，警员ID：{}，错误：{}", police.getId(), e.getMessage(), e);
            }
        }

        log.info("部门{}警员晋升状态计算完成，总数：{}，处理成功：{}", deptId, policeList.size(), processedCount);
        return processedCount;
    }

    @Override
    public Integer getTargetPromotionRank(Integer currentRankCode) {
        return rankMappingConfig.getTargetRank(currentRankCode);
    }

    @Override
    public boolean canPromote(Integer currentRankCode) {
        return rankMappingConfig.canPromote(currentRankCode);
    }

    /**
     * 验证请求参数
     */
    private boolean validateRequest(PromotionEvaluationRequest request) {
        return request != null && request.getPoliceId() != null;
    }

    /**
     * 确定目标晋升职级
     */
    private Integer determineTargetRank(ProfilePolice police, PromotionEvaluationRequest request) {
        // 如果请求中指定了目标职级，优先使用
        if (request.getTargetRankCode() != null) {
            return request.getTargetRankCode();
        }

        // 如果请求中指定了当前职级，使用指定的
        Integer currentRankCode = request.getCurrentRankCode();
        if (currentRankCode == null) {
            // TODO: 从警员档案的职级关联表中查询当前职级
            // 这里需要查询 t_police_rank_relation 表获取当前职级
            // 暂时返回null，实际实现时需要添加相应的查询逻辑
            log.warn("无法确定警员当前职级，警员ID：{}", police.getId());
            return null;
        }

        return rankMappingConfig.getTargetRank(currentRankCode);
    }

    /**
     * 获取晋升策略
     */
    private List<PromotionRuleStrategy> getPromotionStrategies(Integer targetRankCode) {
        // 根据目标职级获取职级类别
        String category = rankMappingConfig.getCategoryByTargetRank(targetRankCode);

        // 从配置中获取策略类名列表
        List<String> strategyClassNames = rankMappingConfig.getStrategiesByTargetRank(category, targetRankCode);

        // 根据类名获取策略实例
        return strategyFactory.getStrategiesByClassNames(strategyClassNames);
    }

    /**
     * 执行策略评估
     */
    private List<PromotionRuleDetailVO> evaluateStrategies(ProfilePolice police, Integer targetRankCode,
                                                          List<PromotionRuleStrategy> strategies) {
        List<PromotionRuleDetailVO> ruleDetails = new ArrayList<>();

        for (PromotionRuleStrategy strategy : strategies) {
            try {
                PromotionRuleDetailVO ruleDetail = strategy.evaluateRule(police, targetRankCode);
                if (ruleDetail != null) {
                    ruleDetails.add(ruleDetail);
                }
            } catch (Exception e) {
                log.error("执行策略评估失败，策略：{}，警员ID：{}，错误：{}",
                        strategy.getStrategyName(), police.getId(), e.getMessage(), e);

                // 创建错误结果
                PromotionRuleDetailVO errorDetail = PromotionRuleDetailVO.builder()
                        .ruleName(strategy.getStrategyName())
                        .description(strategy.getDescription())
                        .ruleType(strategy.getSupportedRuleType())
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .success(false)
                        .errorMessage("策略执行失败：" + e.getMessage())
                        .build();
                ruleDetails.add(errorDetail);
            }
        }

        // 按必要条件排序（必要条件优先）
        ruleDetails.sort(Comparator.comparing(detail ->
                Boolean.TRUE.equals(detail.getRequired()) ? 0 : 1));

        return ruleDetails;
    }

    /**
     * 计算最终晋升状态
     * 优先级：不得晋升 > 暂缓晋升 > 有资格/无资格
     */
    private PromotionStatusEnum calculateFinalStatus(List<PromotionRuleDetailVO> ruleDetails) {
        if (CollectionUtils.isEmpty(ruleDetails)) {
            return PromotionStatusEnum.NOT_QUALIFIED;
        }

        PromotionStatusEnum finalStatus = PromotionStatusEnum.QUALIFIED;

        for (PromotionRuleDetailVO detail : ruleDetails) {
            if (!detail.getSuccess() || detail.getRuleResult() == null) {
                continue;
            }

            // 如果是必要条件且不满足，直接返回不符合
            if (Boolean.TRUE.equals(detail.getRequired()) &&
                detail.getRuleResult() != PromotionStatusEnum.QUALIFIED) {
                return detail.getRuleResult();
            }

            // 按优先级比较状态
            finalStatus = finalStatus.compareWithPriority(detail.getRuleResult());
        }

        return finalStatus;
    }

    /**
     * 构建成功结果
     */
    private PromotionEvaluationResult buildSuccessResult(ProfilePolice police, Integer targetRankCode,
                                                        PromotionStatusEnum finalStatus,
                                                        List<PromotionRuleDetailVO> ruleDetails) {
        return PromotionEvaluationResult.builder()
                .policeId(police.getId())
                .policeName(police.getName())
                .idNumber(police.getIdNumber())
                .targetRankCode(targetRankCode)
                .promotionStatus(finalStatus)
                .ruleDetails(ruleDetails)
                .evaluationTime(LocalDateTime.now())
                .success(true)
                .resultDescription(buildResultDescription(finalStatus, ruleDetails))
                .suggestion(buildSuggestion(finalStatus, ruleDetails))
                .build();
    }

    /**
     * 构建错误结果
     */
    private PromotionEvaluationResult buildErrorResult(PromotionEvaluationRequest request, String errorMessage) {
        return PromotionEvaluationResult.builder()
                .policeId(request.getPoliceId())
                .idNumber(request.getIdNumber())
                .evaluationTime(LocalDateTime.now())
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }

    /**
     * 构建结果描述
     */
    private String buildResultDescription(PromotionStatusEnum finalStatus, List<PromotionRuleDetailVO> ruleDetails) {
        StringBuilder sb = new StringBuilder();
        sb.append("晋升评估结果：").append(finalStatus.getName()).append("。");

        long hitCount = ruleDetails.stream().filter(detail -> Boolean.TRUE.equals(detail.getIsHit())).count();
        sb.append("共评估").append(ruleDetails.size()).append("个规则，");
        sb.append("命中").append(hitCount).append("个规则。");

        return sb.toString();
    }

    /**
     * 构建建议信息
     */
    private String buildSuggestion(PromotionStatusEnum finalStatus, List<PromotionRuleDetailVO> ruleDetails) {
        if (finalStatus == PromotionStatusEnum.QUALIFIED) {
            return "恭喜！您符合晋升条件，可以申请晋升。";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("暂不符合晋升条件，建议：");

        for (PromotionRuleDetailVO detail : ruleDetails) {
            if (!Boolean.TRUE.equals(detail.getIsHit()) &&
                detail.getRuleResult() != PromotionStatusEnum.QUALIFIED) {
                sb.append(detail.getCalculateDescription()).append("；");
            }
        }

        return sb.toString();
    }
}
