package com.trs.police.profile.controller;

import com.trs.police.profile.domain.dto.promotion.PromotionEvaluationRequest;
import com.trs.police.profile.domain.vo.promotion.PromotionEvaluationResult;
import com.trs.police.profile.service.PolicePromotionService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 警员职级晋升控制器
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Api(tags = "警员职级晋升管理")
@RestController
@RequestMapping("/api/police/promotion")
@Validated
public class PolicePromotionController {

    @Autowired
    private PolicePromotionService policePromotionService;

    @ApiOperation("评估警员晋升资格")
    @PostMapping("/evaluate")
    public RestfulResultsV2<PromotionEvaluationResult> evaluatePromotion(
            @ApiParam(value = "晋升评估请求", required = true)
            @Valid @RequestBody PromotionEvaluationRequest request) {
        
        try {
            PromotionEvaluationResult result = policePromotionService.evaluatePromotion(request);
            return RestfulResultsV2.success(result);
        } catch (Exception e) {
            log.error("评估警员晋升资格失败", e);
            return RestfulResultsV2.error("评估失败：" + e.getMessage());
        }
    }

    @ApiOperation("批量评估警员晋升资格")
    @PostMapping("/evaluate/batch")
    public RestfulResultsV2<List<PromotionEvaluationResult>> batchEvaluatePromotion(
            @ApiParam(value = "晋升评估请求列表", required = true)
            @Valid @RequestBody List<PromotionEvaluationRequest> requests) {
        
        try {
            List<PromotionEvaluationResult> results = policePromotionService.batchEvaluatePromotion(requests);
            return RestfulResultsV2.success(results);
        } catch (Exception e) {
            log.error("批量评估警员晋升资格失败", e);
            return RestfulResultsV2.error("批量评估失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据警员ID评估晋升资格")
    @GetMapping("/evaluate/{policeId}")
    public RestfulResultsV2<PromotionEvaluationResult> evaluatePromotionById(
            @ApiParam(value = "警员档案ID", required = true)
            @PathVariable @NotNull Long policeId) {
        
        try {
            PromotionEvaluationResult result = policePromotionService.evaluatePromotionById(policeId);
            return RestfulResultsV2.success(result);
        } catch (Exception e) {
            log.error("根据警员ID评估晋升资格失败，警员ID：{}", policeId, e);
            return RestfulResultsV2.error("评估失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据身份证号评估晋升资格")
    @GetMapping("/evaluate/idNumber/{idNumber}")
    public RestfulResultsV2<PromotionEvaluationResult> evaluatePromotionByIdNumber(
            @ApiParam(value = "身份证号", required = true)
            @PathVariable @NotEmpty String idNumber) {
        
        try {
            PromotionEvaluationResult result = policePromotionService.evaluatePromotionByIdNumber(idNumber);
            return RestfulResultsV2.success(result);
        } catch (Exception e) {
            log.error("根据身份证号评估晋升资格失败，身份证号：{}", idNumber, e);
            return RestfulResultsV2.error("评估失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新警员晋升状态")
    @PutMapping("/status/{policeId}")
    public RestfulResultsV2<Boolean> updatePromotionStatus(
            @ApiParam(value = "警员档案ID", required = true)
            @PathVariable @NotNull Long policeId,
            @ApiParam(value = "晋升状态代码", required = true)
            @RequestParam @NotNull Integer promotionStatus) {
        
        try {
            boolean success = policePromotionService.updatePromotionStatus(policeId, promotionStatus);
            return RestfulResultsV2.success(success);
        } catch (Exception e) {
            log.error("更新警员晋升状态失败，警员ID：{}，状态：{}", policeId, promotionStatus, e);
            return RestfulResultsV2.error("更新失败：" + e.getMessage());
        }
    }

    @ApiOperation("批量更新警员晋升状态")
    @PutMapping("/status/batch")
    public RestfulResultsV2<Integer> batchUpdatePromotionStatus(
            @ApiParam(value = "警员档案ID列表", required = true)
            @RequestParam @NotEmpty List<Long> policeIds,
            @ApiParam(value = "晋升状态代码", required = true)
            @RequestParam @NotNull Integer promotionStatus) {
        
        try {
            int successCount = policePromotionService.batchUpdatePromotionStatus(policeIds, promotionStatus);
            return RestfulResultsV2.success(successCount);
        } catch (Exception e) {
            log.error("批量更新警员晋升状态失败，状态：{}", promotionStatus, e);
            return RestfulResultsV2.error("批量更新失败：" + e.getMessage());
        }
    }

    @ApiOperation("执行晋升状态批量计算任务")
    @PostMapping("/calculate/all")
    public RestfulResultsV2<Integer> executePromotionStatusCalculation() {
        try {
            int processedCount = policePromotionService.executePromotionStatusCalculation();
            return RestfulResultsV2.success(processedCount);
        } catch (Exception e) {
            log.error("执行晋升状态批量计算任务失败", e);
            return RestfulResultsV2.error("计算任务执行失败：" + e.getMessage());
        }
    }

    @ApiOperation("计算指定部门警员的晋升状态")
    @PostMapping("/calculate/dept/{deptId}")
    public RestfulResultsV2<Integer> calculatePromotionStatusByDept(
            @ApiParam(value = "部门ID", required = true)
            @PathVariable @NotNull Long deptId) {
        
        try {
            int processedCount = policePromotionService.calculatePromotionStatusByDept(deptId);
            return RestfulResultsV2.success(processedCount);
        } catch (Exception e) {
            log.error("计算部门警员晋升状态失败，部门ID：{}", deptId, e);
            return RestfulResultsV2.error("计算失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取指定职级的目标晋升职级")
    @GetMapping("/target-rank/{currentRankCode}")
    public RestfulResultsV2<Integer> getTargetPromotionRank(
            @ApiParam(value = "当前职级代码", required = true)
            @PathVariable @NotNull Integer currentRankCode) {
        
        try {
            Integer targetRank = policePromotionService.getTargetPromotionRank(currentRankCode);
            return RestfulResultsV2.success(targetRank);
        } catch (Exception e) {
            log.error("获取目标晋升职级失败，当前职级：{}", currentRankCode, e);
            return RestfulResultsV2.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation("检查指定职级是否支持晋升")
    @GetMapping("/can-promote/{currentRankCode}")
    public RestfulResultsV2<Boolean> canPromote(
            @ApiParam(value = "当前职级代码", required = true)
            @PathVariable @NotNull Integer currentRankCode) {
        
        try {
            boolean canPromote = policePromotionService.canPromote(currentRankCode);
            return RestfulResultsV2.success(canPromote);
        } catch (Exception e) {
            log.error("检查职级晋升支持失败，当前职级：{}", currentRankCode, e);
            return RestfulResultsV2.error("检查失败：" + e.getMessage());
        }
    }
}
