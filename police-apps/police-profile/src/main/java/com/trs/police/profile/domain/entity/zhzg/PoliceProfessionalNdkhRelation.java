package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 年度考核表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_police_professional_ndkh_relation", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class PoliceProfessionalNdkhRelation extends AbstractBaseEntity {

    /**
     * 关联警员档案表（t_police_profile）的主键
     */
    private Long profileId;
    /**
     * 考核日期
     */
    private Date assessmentTime;
    /**
     * 考核结果, 码表，type = police_assessment_result
     */
    private Integer assessmentResult;
    /**
     * 是否删除
     */
    private Boolean deleted;
}

