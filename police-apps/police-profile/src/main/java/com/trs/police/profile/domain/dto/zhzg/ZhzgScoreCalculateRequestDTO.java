package com.trs.police.profile.domain.dto.zhzg;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 智慧政工积分计算请求DTO
 */
@Data
public class ZhzgScoreCalculateRequestDTO {

    /**
     * 积分规则列表
     */
    @NotEmpty(message = "积分规则列表不能为空")
    private List<ZhzgScoreRuleDTO> rules;

    /**
     * 人员档案信息
     */
    @NotNull(message = "人员档案信息不能为空")
    private ZhzgPersonArchiveDTO personArchive;

    /**
     * 计算类型（可选，用于区分不同的计算场景）
     */
    private String calculateType;

    /**
     * 是否返回详细计算过程
     */
    private Boolean includeDetails = true;

}
