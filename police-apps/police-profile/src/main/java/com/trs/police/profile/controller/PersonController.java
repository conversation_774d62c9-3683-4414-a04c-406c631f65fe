package com.trs.police.profile.controller;

import com.trs.police.common.core.annotation.SkipResponseBodyAdvice;
import com.trs.police.common.core.dto.ProfileVirtualIdentityDto;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.CheckResult;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.ImportResultVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.common.core.vo.profile.PersonCaseVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.vo.PersonListVO;
import com.trs.police.common.openfeign.starter.vo.PersonPoliceVO;
import com.trs.police.common.openfeign.starter.vo.UpdatePersonRequest;
import com.trs.police.profile.domain.dto.ProfilePersonDto;
import com.trs.police.profile.domain.dto.UpdatePersonFollowDTO;
import com.trs.police.profile.domain.vo.PersonByIdCardVO;
import com.trs.police.profile.domain.vo.PersonGroupVO;
import com.trs.police.profile.domain.vo.ProfilePersonPoliceControlVo;
import com.trs.police.profile.excel.ImportService;
import com.trs.police.profile.excel.ImportVO;
import com.trs.police.profile.excel.PersonImportService;
import com.trs.police.profile.excel.download.PersonDownloadSerivce;
import com.trs.police.profile.schema.service.DynamicTableService;
import com.trs.police.profile.schema.vo.DynamicTableResultVO;
import com.trs.police.profile.service.PersonPoliceControlService;
import com.trs.police.profile.service.PersonService;
import com.trs.police.profile.service.ProfilePersonService;
import com.trs.police.profile.service.VirtualIdentityService;
import com.trs.police.profile.service.impl.PersonMonitorServiceImpl;
import com.trs.police.profile.service.impl.SubscribeService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> yanghy
 * @date : 2022/10/9 16:43
 */
@RestController
@RequestMapping("/person")
public class PersonController {

    @Resource
    private PersonService personService;

    @Resource
    ProfilePersonService profilePersonService;

    @Resource
    SubscribeService subscribeService;

    @Resource
    VirtualIdentityService virtualIdentityService;
    @Resource
    ImportService importService;
    @Resource
    private DynamicTableService dynamicTableService;

    @Resource
    private PersonPoliceControlService personPoliceControlService;

    @Autowired
    private PersonMonitorServiceImpl personMonitorService;

    @Resource
    private PersonImportService personImportService;

    @Resource
    private PersonDownloadSerivce personDownloadSerivce;


    /**
     * 根据证件类型和证件号码模糊查询人员档案
     *
     * @param certificateNumber 证件号码
     * @param certificateType   证件类型
     * @return {@link PersonVO}
     */
    @GetMapping("/fuzzy")
    public List<PersonVO> getPersonListFuzzy(@RequestParam("certificateNumber") String certificateNumber,
                                             @RequestParam("certificateType") String certificateType) {
        return personService.getPersonListFuzzy(certificateNumber, certificateType);
    }

    /**
     * 根据证件号码查询人员档案
     *
     * @param idNumber 证件号码
     * @return {@link PersonVO}
     */
    @GetMapping(value = {"", "/public"})
    public PersonVO getPersonByIdNumber(@RequestParam("idNumber") String idNumber) {
        return personService.getPersonByIdNumber(idNumber);
    }

    /**
     * 根据身份证号码查询人员档案西悉尼
     *
     * @param idNumber 省份证号码
     * @return 人员档案信息
     */
    @GetMapping("/personInfoByIdNumber")
    public PersonByIdCardVO getPersonInfoByIdNumber(@RequestParam("idNumber") String idNumber) {
        PersonVO person = personService.getPersonByIdNumber(idNumber);
        if (Objects.isNull(person) || Objects.isNull(person.getId())) {
            return new PersonByIdCardVO();
        }
        DynamicTableResultVO personInfo = dynamicTableService.selectTableSchema(3L, person.getId());
        DynamicTableResultVO govControl = dynamicTableService.selectTableSchema(7L, person.getId());
        List<ProfilePersonPoliceControlVo> controlInfoByPersonId = personPoliceControlService.getControlInfoByPersonId(person.getId(), null);
        return new PersonByIdCardVO(personInfo, govControl, controlInfoByPersonId);
    }

    /**
     * 根据标识符及类型查询人员信息
     *
     * @param identifierNumber 标识符号码
     * @param identifierType   标识符类型
     * @return 人员信息
     */
    @GetMapping("/public/identifier")
    List<PersonVO> findByIdentifier(@RequestParam("identifierNumber") String identifierNumber,
                                    @RequestParam("identifierType") Integer identifierType) {
        return personService.findByIdentifier(identifierNumber, identifierType);
    }

    /**
     * 人员列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link com.trs.police.common.core.vo.profile.PersonVO}
     */
    @PostMapping("/list")
    public PageResult<PersonVO> selectList(@RequestBody ListParamsRequest paramsRequest) {
        return profilePersonService.getPage(paramsRequest);
    }

    /**
     * 人员列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link com.trs.police.common.core.vo.profile.PersonVO}
     */
    @PostMapping("/listV1")
    public PageResult<PersonListVO> selectListV1(@RequestBody ListParamsRequest paramsRequest) {
        return profilePersonService.getPageV1(paramsRequest, false);
    }

    /**
     * 导出人员列表
     *
     * @param response response
     * @param params   导出参数
     */
    @PostMapping("/exportV1")
    @ApiOperation("导出人员列表")
    public void exportV1(HttpServletResponse response, @RequestBody ExportParams params) throws IOException {
        profilePersonService.exportV1(response, params);
    }

    /**
     * 人员列表查询V2
     *
     * @param paramsRequest 列表请求参数
     * @return {@link com.trs.police.common.core.vo.profile.PersonVO}
     */
    @PostMapping("/listV2")
    public PageResult<PersonListVO> selectListV2(@RequestBody ListParamsRequest paramsRequest) {
        return profilePersonService.getPageV1(paramsRequest, true);
    }

    /**
     * 人员列表查询 https://yapi-192.trscd.com.cn/project/4974/interface/api/141479
     *
     * @param paramsRequest 列表请求参数
     * @return {@link PersonCardVO}
     */
    @PostMapping("/list/card")
    public PageResult<PersonCardVO> selectCardList(@RequestBody ListParamsRequest paramsRequest) {
        return profilePersonService.selectCardList(paramsRequest);
    }

    /**
     * 人员id查询
     *
     * @param paramsRequest 列表请求参数
     * @return 人员Id
     */
    @PostMapping("/public/id/list")
    public List<Long> getPersonIds(@RequestBody ListParamsRequest paramsRequest) {
        return personService.getPersonIds(paramsRequest);
    }

    /**
     * 根据人员id，批量获取人员信息
     *
     * @param ids 人员id数组
     * @return {@link PersonVO}
     */
    @PostMapping(value = {"/batch-id", "/public/batch-id"})
    public List<PersonVO> getPersonByIds(@RequestBody List<Long> ids) {
        return personService.getPersonByIds(ids);
    }

    /**
     * 根据人员id获取人员信息
     *
     * @param personId 人员id
     * @return {@link PersonVO}
     */
    @GetMapping(value = {"/{personId}", "/public/{personId}"})
    public PersonVO getPersonId(@PathVariable("personId") Long personId) {
        return personService.getById(personId);
    }

    /**
     * 根据人员id获取人员信息
     *
     * @param personId 人员id
     * @return {@link PersonVO}
     */
    @GetMapping(value = {"/public/findById"})
    public PersonVO findById(Long personId) {
        return personService.getById(personId);
    }

    /**
     * 更新人员档案信息 http://192.168.200.192:3001/project/4974/interface/api/141919
     *
     * @param personVO 人员信息
     * @return 结果
     */
    @PostMapping("/public/update")
    public Long updatePersonInfo(@RequestBody PersonVO personVO) {
        return personService.updatePersonInfo(personVO);
    }

    /**
     * 更新人员档案信息
     *
     * @param request 人员信息
     * @return id
     */
    @PostMapping("/public/update/batch")
    public List<Long> updatePersonInfo(@RequestBody UpdatePersonRequest request) {
        return personService.updatePersonList(request);
    }

    /**
     * 设置人员临控状态
     *
     * @param personIds     人员id
     * @param monitorStatus 布控状态
     * @return {@link Integer}
     */
    @PostMapping(value = {"/set-monitor/{monitorStatus}", "/public/set-monitor/{monitorStatus}"})
    public Integer setPersonsMonitorStatus(@RequestBody List<Long> personIds,
                                           @PathVariable("monitorStatus") Integer monitorStatus) {
        return personService.setPersonsMonitorStatus(personIds, monitorStatus);
    }

    /**
     * 设置人员临控状态
     *
     * @param idNumbers     人员身份证
     * @param monitorStatus 布控状态
     * @return {@link Integer}
     */
    @PostMapping(value = {"/set-monitor/id-number/{monitorStatus}", "/public/set-monitor/id-number/{monitorStatus}"})
    public Integer setPersonsMonitorStatusByIdNumbers(@RequestBody List<String> idNumbers,
                                                      @PathVariable("monitorStatus") Integer monitorStatus) {
        return personService.setPersonsMonitorStatusByIdNumbers(idNumbers, monitorStatus);
    }

    /**
     * 设置人员常控状态
     *
     * @param personIds     人员id
     * @param controlStatus 布控状态
     * @return {@link Integer}
     */
    @PostMapping(value = {"/set-control/{controlStatus}", "/public/set-control/{controlStatus}"})
    public Integer setPersonsControlStatus(@RequestBody List<Long> personIds,
                                           @PathVariable("controlStatus") Integer controlStatus) {
        return personService.setPersonsControlStatus(personIds, controlStatus);
    }

    /**
     * 取消人员常控状态
     *
     * @param id 人员id
     */
    @PutMapping(value = {"/{id}/un-control", "/public/{id}/un-control"})
    public void setPersonUnControl(@PathVariable("id") Long id) {
        personService.setPersonUnControl(id);
    }

    /**
     * 根据群体id查人员列表
     *
     * @param paramsRequest 列表请求参数
     * @return {@link com.trs.police.common.core.vo.profile.PersonVO}
     */
    @PostMapping("/list/by-group")
    public PageResult<PersonCardVO> selectListByGroup(@RequestBody ListParamsRequest paramsRequest) {
        return personService.getPageByGroup(paramsRequest);
    }

    /**
     * 根据人员身份证号查询所在群体
     *
     * @param idNumber 身份证号
     * @return 群体id
     */
    @GetMapping("/public/group/{idNumber}")
    public List<Long> selectGroupIdsByIdNumber(@PathVariable String idNumber) {
        return personService.selectGroupIdsByIdNumber(idNumber);
    }

    /**
     * 查询符合条件的人员的idNumbers
     *
     * @param paramsRequest 请求参数
     * @return 人员id
     */
    @PostMapping("/idNumbers")
    public List<String> selectPersonIds(@RequestBody ListParamsRequest paramsRequest) {
        return profilePersonService.selectPersonIds(paramsRequest);
    }

    /**
     * 根据人员idNumber，批量获取人员信息
     *
     * @param idNumbers 人员id数组
     * @return {@link PersonVO}
     */
    @PostMapping("/public/batch-idNumbers")
    public List<PersonVO> getPersonByIdNumbers(@RequestBody List<String> idNumbers) {
        return profilePersonService.getPersonByIdNumbers(idNumbers);
    }

    /**
     * 查看人员是否存在
     *
     * @param idType   证件号码
     * @param idNumber 证件类型
     * @return id
     */
    @GetMapping(value = {"/id/{idType}/{idNumber}", "/public/id/{idType}/{idNumber}"})
    public Long getIdByIdTypeAndIdNumber(@PathVariable("idType") Integer idType,
                                         @PathVariable("idNumber") String idNumber) {
        return profilePersonService.getIdByIdTypeAndIdNumber(idType, idNumber);
    }

    /**
     * 新增常控时人员档案列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link com.trs.police.common.core.vo.profile.PersonVO}
     */
    @PostMapping("/list/regular")
    public PageResult<PersonCardVO> selectRegularList(@RequestBody ListParamsRequest paramsRequest) {
        return profilePersonService.getRegularPage(paramsRequest);
    }

    /**
     * 查询身份证号里不存在的
     *
     * @param idNumbers 身份证号列表
     * @return 结果
     */
    @PostMapping("/exist")
    public List<String> checkPersonExist(@RequestBody List<String> idNumbers) {
        return profilePersonService.checkPersonExist(idNumbers);
    }

    /**
     * 新增常控时人员档案列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link Long}
     */
    @PostMapping("/list/regular/ids")
    public List<Long> getIdsByRegularParams(@RequestBody ListParamsRequest paramsRequest) {
        return profilePersonService.getIdsByRegularParams(paramsRequest);
    }

    /**
     * 根据人员档案id查询公安管控信息
     *
     * @param personId 人员id
     * @return 公安管控信息
     */
    @GetMapping(value = {"public/{personId}/control", "/{personId}/control"})
    PersonPoliceVO getControlInfoByPersonId(@PathVariable("personId") Long personId) {
        return profilePersonService.getControlInfoByPersonId(personId);
    }

    /**
     * 人员卡片列表
     * <p>
     * http://192.168.200.192:3001/project/4974/interface/api/142999
     *
     * @param request 请求参数
     * @return {@link PersonCardVO}
     */
    @PostMapping("/card/list")
    public PageResult<PersonCardVO> getSuspectList(@RequestBody ListParamsRequest request) {
        return profilePersonService.getPersonCardList(request);
    }

    /**
     * 根据人员id查询人员卡片
     *
     * @param id 人员id
     * @return 卡片信息
     */
    @GetMapping("/{id}/card/by-id")
    public PersonCardVO getCardById(@PathVariable("id") Long id) {
        return personService.getCardById(id);
    }

    /**
     * 根据人员身份证号查询人员卡片
     *
     * @param idNumber 人员身份证号
     * @return 卡片信息
     */
    @GetMapping("/{idNumber}/card")
    public PersonCardVO getCardByIdNumber(@PathVariable("idNumber") String idNumber) {
        return personService.getCardByIdNumber(idNumber);
    }

    /**
     * 向moye 推送当前系统所有人员-标签信息
     */
    @GetMapping("/public/moye")
    public void updatePersonLabelInfoToMoye() {
        subscribeService.updatePersonLabelInfoToMoye();
    }

    /**
     * 向moye 推送指定人员档案的标签信息
     *
     * @param personId personId
     */
    @GetMapping("/public/moye/{personId}")
    public void updatePersonLabelInfoToMoyeById(@PathVariable Integer personId) {
        subscribeService.updatePersonLabelInfoToMoyeById(personId);
    }

    /**
     * 根据标识符及类型查询人员管控单位id
     *
     * @param identifierNumber 标识符号码
     * @param identifierType   标识符类型
     * @return 管控单位id
     */
    @GetMapping("/public/control-dept")
    Long getPersonControlDeptIdByIdentifier(@RequestParam("identifierNumber") String identifierNumber,
                                            @RequestParam("identifierType") Integer identifierType) {
        return personService.getPersonControlDeptIdByIdentifier(identifierNumber, identifierType);
    }

    /**
     * 人员电话号码查重 http://192.168.200.192:3001/project/4974/interface/api/147468
     *
     * @param tel 电话号码
     * @return 查重结果
     */
    @GetMapping("/check-tel/{tel}")
    CheckResult checkTel(@PathVariable(value = "tel", required = false) String tel) {
        return personService.checkTel(tel);
    }

    /**
     * 新增新虚拟身份
     *
     * @param virtualIdentity 虚拟身份
     */
    @PostMapping("/virtual-identity")
    void addVirtualIdentity(@RequestBody ProfileVirtualIdentityDto virtualIdentity) {
        virtualIdentityService.addVirtualIdentity(virtualIdentity);
    }

    /**
     * 根据虚拟身份查询人员信息
     *
     * @param tel 电话号码
     * @return 人员信息
     */
    @GetMapping(value = {"/public/tel", "/tel"})
    List<PersonVO> getPersonVoByTel(@RequestParam("tel") String tel) {
        return personService.getPersonByTel(tel);
    }

    /**
     * 更新人员电话号码
     *
     * @param id  人员id
     * @param tel 电话号码
     */
    @PutMapping(value = {"/public/{id}/tel", "/{id}/tel"})
    void updateTel(@PathVariable("id") Long id, @RequestBody List<String> tel) {
        personService.updateTel(id, tel);
    }

    /**
     * 检查身份证号码是否存在
     *
     * @param idNumber 身份证号码
     */
    @GetMapping("/check/id-number/{idNumber}")
    void checkIdNumberExists(@PathVariable(value = "idNumber", required = false) String idNumber) {
        personService.checkIdNumberExists(idNumber);
    }

    /**
     * 查询人员群体活跃程度
     *
     * @param personId 人员id
     * @param groupId  人员群体id
     * @return 活跃程度
     */
    @GetMapping("/activity-level")
    CodeNameVO getActivityLevel(@RequestParam("id") Long personId, @RequestParam("groupId") Long groupId) {
        return personService.getActivityLevel(personId, groupId);
    }

    /**
     * 批量导入
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    @PostMapping("/import")
    ImportResultVO importPerson(ImportVO vo) {
        return importService.importPerson(vo);
    }

    @GetMapping("/download/template")
    @SkipResponseBodyAdvice
    void downloadTemplate(HttpServletResponse response) throws IOException {
        importService.downloadTemplate(response);
    }


    /**
     * 列表导出
     *
     * @param response HttpServletResponse
     * @param moduleId 动态列表id
     * @param params   参数
     */
    @PostMapping("/list/export/{moduleId}")
    @SkipResponseBodyAdvice
    public void personExport(HttpServletResponse response, @PathVariable("moduleId") Long moduleId,
                             @RequestBody ExportParams params) {
        personService.personExport(response, params, moduleId);
    }

    /**
     * 是否开启档案信息回填
     *
     * @return 结果
     */
    @GetMapping("/openBackFill")
    @SkipResponseBodyAdvice
    public CheckResult openBackFill() {
        return personService.openBackFill();
    }

    /**
     * 下载人员档案
     *
     * @param response HttpServletResponse
     * @param personId 人员档案id
     */
    @GetMapping("/download/personRecord/{personId}")
    @SkipResponseBodyAdvice
    void downloadPersonRecord(HttpServletResponse response, @PathVariable("personId") Long personId) {
        personService.downloadPersonRecord(response, personId);
    }

    /**
     * 获取人员档案列表
     *
     * @param profilePersonDto dto
     * @return 结果
     */
    @PostMapping("/getProfilePersonList")
    public PageResult<PersonGroupVO> getProfilePersonList(@RequestBody ProfilePersonDto profilePersonDto) {
        return personService.getProfilePersonList(profilePersonDto);
    }

    /**
     * 涉案人员列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link com.trs.police.common.core.vo.profile.PersonVO}
     */
    @PostMapping("/personCaseList")
    public PageResult<PersonCaseVO> getPersonCaseList(@RequestBody ListParamsRequest paramsRequest) {
        return profilePersonService.getPersonCaseList(paramsRequest);
    }

    /**
     * 归档
     *
     * @param idNumber 身份证号码
     */
    @PostMapping("/finishArchive")
    public void finishArchive(String idNumber) {
        profilePersonService.finishArchive(idNumber);
    }

    /**
     * 重新启用
     *
     * @param idNumber 省份证号码
     */
    @PostMapping("/openArchive")
    public void openArchive(String idNumber) {
        profilePersonService.openArchive(idNumber);
    }

    /**
     * 删除群体
     *
     * @param idList     群体id列表
     * @param policeKind 归属警种
     */
    @GetMapping("delete")
    public void delGroup(@RequestParam("idList") String idList, @RequestParam("policeKind") Long policeKind) {
        profilePersonService.delPerson(idList, policeKind);
    }

    /**
     * 对人员发起常控
     *
     * @param ids       id列表
     * @param startTime 创建开始时间
     * @param endTime   创建结束时间
     */
    @GetMapping("/addRegularPerson")
    public void addRegularPerson(String ids, String startTime, String endTime) {
        personMonitorService.addRegularPerson(ids, startTime, endTime);
    }

    /**
     * 修改人员是否关注状态
     *
     * @param dto 参数
     * @param isFollowed 是否关注状态：0-未关注，1-已关注
     */
    @PostMapping("/updateFollowStatus/{isFollowed}")
    public void updateFollowStatus(@RequestBody UpdatePersonFollowDTO dto, @PathVariable("isFollowed") Integer isFollowed) {
        profilePersonService.updateFollowStatus(dto, isFollowed);
    }

    /**
     * 批量导入-zg
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    @PostMapping("/importV2")
    ImportResultVO importPersonV2(ImportVO vo) {
        return personImportService.importPerson(vo);
    }

    /**
     * 下载模板-zg
     *
     * @param response   HttpServletResponse
     * @param policeKind 管控警种
     */
    @GetMapping("/download/templateV2")
    @SkipResponseBodyAdvice
    void downloadTemplateV2(HttpServletResponse response, Integer policeKind) throws IOException {
        personImportService.downLoadPersonTemplate(response, policeKind);
    }

    /**
     * 下载人员档案
     *
     * @param response HttpServletResponse
     * @param personId 人员档案id
     */
    @GetMapping("/download/personRecordV2")
    @SkipResponseBodyAdvice
    void downloadPersonRecordV2(HttpServletResponse response, Long personId) throws Exception {
        personDownloadSerivce.personRecord(response, personId);
    }

    /**
     * 导出人员列表
     *
     * @param response 响应
     * @param params   参数
     * @throws Exception 异常
     */
    @PostMapping("/exportV2")
    void personExportV2(HttpServletResponse response, @RequestBody ExportParams params) throws Exception {
        profilePersonService.personExportV2(response, params);
    }

    /**
     * sw人员风险预测同步
     */
    @GetMapping("/syncSwLabel")
    public void syncSwLabel() {
        personService.syncSwLabel("sw");
    }
}
