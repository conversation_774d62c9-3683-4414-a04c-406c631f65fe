package com.trs.police.profile.strategy.promotion.impl;

import com.trs.police.profile.constant.PromotionStatusEnum;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 技术职务学历要求晋升规则策略
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class TechnicalEducationPromotionStrategy implements PromotionRuleStrategy {

    private static final String STRATEGY_NAME = "技术职务学历要求规则";
    private static final String RULE_TYPE = "TECHNICAL_EDUCATION";

    @Override
    public PromotionRuleDetailVO evaluateRule(ProfilePolice police, Integer targetRankCode) {
        log.debug("开始评估技术职务学历要求规则，警员：{}，目标职级：{}", police.getName(), targetRankCode);

        PromotionRuleDetailVO.PromotionRuleDetailVOBuilder builder = PromotionRuleDetailVO.builder()
                .ruleName(getStrategyName())
                .description(getDescription())
                .ruleType(getSupportedRuleType())
                .required(false)  // 学历要求不是强制必要条件
                .success(true);

        try {
            // 验证数据
            if (!validatePolice(police) || !validateTargetRank(targetRankCode)) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取学历要求
            String requiredEducation = getRequiredEducationByRank(targetRankCode);
            
            // TODO: 这里需要从警员档案中获取学历信息
            // 目前ProfilePolice实体中可能没有学历字段，需要从相关表中查询
            // 暂时模拟学历信息
            String currentEducation = getCurrentEducation(police);

            boolean isQualified = checkEducationQualification(currentEducation, requiredEducation);
            PromotionStatusEnum result = isQualified ? PromotionStatusEnum.QUALIFIED : PromotionStatusEnum.NOT_QUALIFIED;

            String hitData = String.format("当前学历：%s", currentEducation);
            String calculateDescription;

            if (isQualified) {
                calculateDescription = String.format("当前学历%s，满足技术职务晋升要求（要求：%s），符合晋升条件",
                        currentEducation, requiredEducation);
            } else {
                calculateDescription = String.format("当前学历%s，不满足技术职务晋升要求（要求：%s），建议提升学历",
                        currentEducation, requiredEducation);
            }

            return builder
                    .isHit(!isQualified)
                    .ruleResult(result)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("评估技术职务学历要求规则失败，警员：{}，目标职级：{}，错误：{}", 
                    police.getName(), targetRankCode, e.getMessage(), e);
            return builder
                    .isHit(false)
                    .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                    .success(false)
                    .errorMessage("评估失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    /**
     * 根据目标职级获取学历要求
     */
    private String getRequiredEducationByRank(Integer targetRankCode) {
        // 技术职务对学历要求更高
        switch (targetRankCode) {
            // 警务技术主任系列
            case 11: // 警务技术一级主任
                return "硕士";
            case 12: // 警务技术二级主任
                return "硕士";
            case 13: // 警务技术三级主任
                return "本科";
            case 14: // 警务技术四级主任
                return "本科";
            
            // 警务技术主管系列
            case 21: // 警务技术一级主管
                return "本科";
            case 22: // 警务技术二级主管
                return "本科";
            case 23: // 警务技术三级主管
                return "专科";
            case 24: // 警务技术四级主管
                return "专科";
            
            default:
                return "本科"; // 默认本科
        }
    }

    /**
     * 获取警员当前学历
     * TODO: 需要从实际的学历表中查询
     */
    private String getCurrentEducation(ProfilePolice police) {
        // 这里需要查询警员的学历信息
        // 暂时返回模拟数据
        return "本科";
    }

    /**
     * 检查学历是否符合要求
     */
    private boolean checkEducationQualification(String currentEducation, String requiredEducation) {
        // 学历等级映射
        int currentLevel = getEducationLevel(currentEducation);
        int requiredLevel = getEducationLevel(requiredEducation);
        
        return currentLevel >= requiredLevel;
    }

    /**
     * 获取学历等级
     */
    private int getEducationLevel(String education) {
        if (education == null) {
            return 0;
        }
        switch (education) {
            case "博士":
                return 4;
            case "硕士":
                return 3;
            case "本科":
                return 2;
            case "专科":
                return 1;
            default:
                return 0;
        }
    }
}
