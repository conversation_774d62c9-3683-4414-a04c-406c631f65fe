package com.trs.police.profile.strategy.promotion.impl;

import com.trs.police.profile.constant.PromotionStatusEnum;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import com.trs.police.profile.util.PoliceRankUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;

/**
 * 高级职级年龄限制晋升规则策略
 * 处理"晋升四级高级警长及以上职务的，须年满50周岁"的规则
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class SeniorRankAgePromotionStrategy implements PromotionRuleStrategy {

    private static final String STRATEGY_NAME = "高级职级年龄限制规则";
    private static final String RULE_TYPE = "SENIOR_RANK_AGE";

    /**
     * 需要年满50周岁的职级（四级高级警长及以上）
     */
    private static final Set<Integer> SENIOR_RANKS_REQUIRING_50_YEARS = new HashSet<>();

    static {
        // 四级高级警长及以上职务
        SENIOR_RANKS_REQUIRING_50_YEARS.add(31); // 四级高级警长
        SENIOR_RANKS_REQUIRING_50_YEARS.add(30); // 三级高级警长
        SENIOR_RANKS_REQUIRING_50_YEARS.add(29); // 二级高级警长
        SENIOR_RANKS_REQUIRING_50_YEARS.add(28); // 一级高级警长
        
        // 可以根据需要添加其他高级职级
        // 如果技术职务也有类似要求，可以在这里添加
    }

    @Override
    public PromotionRuleDetailVO evaluateRule(ProfilePolice police, Integer targetRankCode) {
        log.debug("开始评估高级职级年龄限制规则，警员：{}，目标职级：{}", police.getName(), targetRankCode);

        PromotionRuleDetailVO.PromotionRuleDetailVOBuilder builder = PromotionRuleDetailVO.builder()
                .ruleName(getStrategyName())
                .description(getDescription())
                .ruleType(getSupportedRuleType())
                .required(true)  // 年龄限制是必要条件
                .success(true);

        try {
            // 验证数据
            if (!validatePolice(police) || !validateTargetRank(targetRankCode)) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 检查是否为需要年龄限制的职级
            if (!SENIOR_RANKS_REQUIRING_50_YEARS.contains(targetRankCode)) {
                // 不需要年龄限制的职级，直接通过
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.QUALIFIED)
                        .calculateDescription("该职级无年龄限制要求，符合条件")
                        .build();
            }

            // 获取出生日期
            if (police.getBirthday() == null) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription("缺少出生日期信息")
                        .build();
            }

            // 计算年龄
            LocalDate birthDate = police.getBirthday().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate currentDate = LocalDate.now();
            Period period = Period.between(birthDate, currentDate);
            int age = period.getYears();

            // 检查是否年满50周岁
            boolean isQualified = age >= 50;
            PromotionStatusEnum result = isQualified ? PromotionStatusEnum.QUALIFIED : PromotionStatusEnum.NOT_QUALIFIED;

            String hitData = String.format("当前年龄：%d岁", age);
            String calculateDescription;

            if (isQualified) {
                calculateDescription = String.format("当前年龄%d岁，年满50周岁，符合四级高级警长及以上职务晋升年龄要求", age);
            } else {
                calculateDescription = String.format("当前年龄%d岁，未满50周岁，不符合四级高级警长及以上职务晋升年龄要求", age);
            }

            return builder
                    .isHit(!isQualified)
                    .ruleResult(result)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("评估高级职级年龄限制规则失败，警员：{}，目标职级：{}，错误：{}", 
                    police.getName(), targetRankCode, e.getMessage(), e);
            return builder
                    .isHit(false)
                    .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                    .success(false)
                    .errorMessage("评估失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public String getDescription() {
        return "晋升四级高级警长及以上职务的，须年满50周岁";
    }

    /**
     * 检查指定职级是否需要年满50周岁
     *
     * @param rankCode 职级代码
     * @return 是否需要年满50周岁
     */
    public static boolean requiresAge50(Integer rankCode) {
        return SENIOR_RANKS_REQUIRING_50_YEARS.contains(rankCode);
    }

    /**
     * 获取所有需要年满50周岁的职级
     *
     * @return 职级代码集合
     */
    public static Set<Integer> getSeniorRanksRequiring50Years() {
        return new HashSet<>(SENIOR_RANKS_REQUIRING_50_YEARS);
    }
}
