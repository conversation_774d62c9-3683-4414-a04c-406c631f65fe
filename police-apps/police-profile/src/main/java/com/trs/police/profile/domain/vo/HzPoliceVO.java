package com.trs.police.profile.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 慧政police vo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HzPoliceVO {

    private String name;          // 姓名
    private String idCardNumber;  // 身份证号码
    private String currentRank;   // 现任职级
    private String rankTenureTime;// 职级任职时间
    private String department;    // 任职部门
    private String currentPosition;// 现任职务
    private String positionTenureTime;// 任职时间
    private String score;         // 积分（示例为double类型，可根据实际存储调整）
    private String promotionQualification;// 晋升资格

}
