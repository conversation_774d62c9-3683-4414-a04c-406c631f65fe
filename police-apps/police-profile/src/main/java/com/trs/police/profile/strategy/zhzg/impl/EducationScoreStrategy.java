package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 教育经历积分计算策略
 * 规则描述：根据最高学历计分，博士20分，硕士15分，本科10分，专科5分
 */
@Slf4j
@Component
public class EducationScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "教育经历";
    private static final String RULE_TYPE = "EDUCATION";
    
    // 学历积分映射
    private static final Map<String, Double> EDUCATION_SCORES = new HashMap<>();
    
    static {
        EDUCATION_SCORES.put("博士", 20.0);
        EDUCATION_SCORES.put("硕士", 15.0);
        EDUCATION_SCORES.put("本科", 10.0);
        EDUCATION_SCORES.put("专科", 5.0);
        EDUCATION_SCORES.put("高中", 2.0);
        EDUCATION_SCORES.put("中专", 2.0);
        EDUCATION_SCORES.put("初中", 1.0);
    }

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算教育经历积分，人员：{}，规则：{}", personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取教育经历
            List<String> educations = personArchive.getEducations();
            if (CollectionUtils.isEmpty(educations)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无教育经历记录")
                        .build();
            }

            // 查找最高学历
            String highestEducation = null;
            double highestScore = 0.0;
            
            for (String education : educations) {
                for (Map.Entry<String, Double> entry : EDUCATION_SCORES.entrySet()) {
                    if (education.contains(entry.getKey()) && entry.getValue() > highestScore) {
                        highestEducation = entry.getKey();
                        highestScore = entry.getValue();
                    }
                }
            }

            if (highestEducation == null) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("未识别到有效的学历信息")
                        .build();
            }

            // 应用规则配置的最大分值限制
            double finalScore = highestScore;
            if (rule.getScore() != null && highestScore > rule.getScore()) {
                finalScore = rule.getScore();
            }

            String hitData = String.format("最高学历：%s", highestEducation);
            String calculateDescription = String.format("最高学历为%s，得分：%.1f分", highestEducation, highestScore);
            
            if (finalScore != highestScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.1f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算教育经历积分失败，人员：{}，规则：{}，错误：{}", 
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public boolean supports(Long ruleId, String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
