package com.trs.police.profile.domain.vo.zhzg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 智慧政工积分计算结果VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ZhzgScoreResultVO {

    /**
     * 人员ID
     */
    private Long personId;

    /**
     * 人员姓名
     */
    private String personName;

    /**
     * 总积分
     */
    private Double totalScore;

    /**
     * 积分等级
     */
    private String scoreLevel;

    /**
     * 计算时间
     */
    private LocalDateTime calculateTime;

    /**
     * 规则详细得分列表
     */
    private List<ZhzgRuleScoreDetailVO> ruleScoreDetails;

    /**
     * 计算说明
     */
    private String description;

    /**
     * 是否计算成功
     */
    private Boolean success;

    /**
     * 错误信息（如果计算失败）
     */
    private String errorMessage;

}
