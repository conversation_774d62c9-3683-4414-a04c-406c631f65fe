package com.trs.police.profile.constant;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 职级晋升状态枚举
 * 
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Getter
public enum PromotionStatusEnum {

    /**
     * 有资格晋升
     */
    QUALIFIED(1, "有资格"),
    
    /**
     * 无资格晋升
     */
    NOT_QUALIFIED(2, "无资格"),
    
    /**
     * 不得晋升
     */
    FORBIDDEN(3, "不得晋升"),
    
    /**
     * 暂缓晋升
     */
    DEFERRED(4, "暂缓晋升");

    @JsonValue
    @EnumValue
    private final Integer code;
    
    private final String name;

    PromotionStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static PromotionStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PromotionStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 获取优先级（数值越小优先级越高）
     * 优先级：不得晋升 > 暂缓晋升 > 有资格/无资格
     *
     * @return 优先级
     */
    public int getPriority() {
        switch (this) {
            case FORBIDDEN:
                return 1;
            case DEFERRED:
                return 2;
            case QUALIFIED:
            case NOT_QUALIFIED:
                return 3;
            default:
                return 999;
        }
    }

    /**
     * 比较优先级，返回优先级更高的状态
     *
     * @param other 另一个状态
     * @return 优先级更高的状态
     */
    public PromotionStatusEnum compareWithPriority(PromotionStatusEnum other) {
        if (other == null) {
            return this;
        }
        return this.getPriority() <= other.getPriority() ? this : other;
    }
}
