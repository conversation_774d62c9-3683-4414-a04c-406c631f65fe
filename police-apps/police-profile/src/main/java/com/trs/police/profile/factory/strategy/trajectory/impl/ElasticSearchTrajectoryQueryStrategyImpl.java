package com.trs.police.profile.factory.strategy.trajectory.impl;

import com.trs.common.utils.expression.Condition;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.db.sdk.domain.impl.CategoryResult;
import com.trs.db.sdk.exception.TrsCrudException;
import com.trs.db.sdk.repository.paramProperties.SearchProperties;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.openfeign.starter.DTO.AiGjDTO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.SearchService;
import com.trs.police.common.openfeign.starter.vo.ThemeGjxxVO;
import com.trs.police.profile.domain.dto.TrajectoryQueryDTO;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.domain.vo.TrackActivityTime;
import com.trs.police.profile.domain.vo.TrackPointVO;
import com.trs.police.profile.factory.TrajectoryQueryStrategyFactory;
import com.trs.police.profile.factory.strategy.trajectory.TrajectoryQueryStrategy;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.repository.WarningTrackRepository;
import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.control.Either;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.trs.police.profile.service.impl.PersonArchiveServiceImpl.FUNCTION_DAY;
import static com.trs.police.profile.service.impl.PersonArchiveServiceImpl.FUNCTION_HOUR;

/**
 * elasticsearch 查询轨迹列表
 * 查询到之后的分析交给其它类去操作
 *
 * <AUTHOR>
 * @since 2025/3/7 11:23
 */
@Slf4j
@Component
public class ElasticSearchTrajectoryQueryStrategyImpl implements TrajectoryQueryStrategy, InitializingBean {

    @Resource
    private WarningTrackRepository warningTrackRepository;

    @Resource
    private DictService dictService;

    @Resource
    private PersonMapper personMapper;

    @Resource
    private SearchService searchService;

    private static final String WARNING_SOURCE_TYPE = "control_warning_source_type";

    private static final String HDSJ = "hdsj";

    private static final String TZZHM = "tzzhm";

    private static final String DESC = "desc";

    private static final String ASC = "asc";

    private static final String YEAR = "year";

    private static final String MONTH = "month";

    private static final String DAY = "day";

    private static final String HOUR = "hour";



    /**
     * 统计小时或月的时候, 缺失值给统计为99, 后面排除掉
     */
    private static final String MISSING_NUMBER = "99";

    /**
     * 查询人员轨迹列表
     *
     * @param personId 人员id
     * @param request  查询条件
     * @return 分页列表
     */
    @Override
    public PageResult<TrackPointVO> getPersonTrackPage(Long personId, ListParamsRequest request) {
        Person person = personMapper.selectById(personId);
        try {
            // 调用超级检索接口
            RestfulResultsV2<ThemeGjxxVO> restfulResultsV2 =
                    searchService.aiAnalysisGjList(buildAiGjDTOFrom(person, request));

            if (restfulResultsV2 == null || restfulResultsV2.getDatas() == null) {
                throw new RuntimeException("超级检索接口返回为空");
            }

            List<ThemeGjxxVO> datas = restfulResultsV2.getDatas();
            List<TrackPointVO> collect = datas.stream().map(item -> {
                TrackPointVO trackPointVO = new TrackPointVO();
                trackPointVO.setTime(item.getHdsj());
                trackPointVO.setName(person.getName());
                trackPointVO.setLocation(item.getHddz());
                trackPointVO.setLng(Double.valueOf(item.getJdwgs84()));
                trackPointVO.setLat(Double.valueOf(item.getWdwgs84()));
                trackPointVO.setType(getParentDictForTrack(item.getGzylx()));
                trackPointVO.setIsStayPoint(false);
                trackPointVO.setSourceName(item.getGzymc());
                trackPointVO.setId(String.valueOf(personId));
                trackPointVO.setContent(new String[]{});
                return trackPointVO;
            }).collect(Collectors.toList());
            PageParams pageParams = request.getPageParams();
            return PageResult.of(collect, pageParams.getPageNumber(), restfulResultsV2.getSummary().getTotal(), pageParams.getPageSize());

//            List<Expression> conditions = new ArrayList<>(2);
//            ArrayList<String> lists = new ArrayList<>();
//            lists.add(person.getIdNumber());
//            // 追加当前人关联的手机号, 车牌号等
//            final TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(), "activeTime",
//                    TimeParams.class);
//            final String beginStr = TimeUtil.localDateTimeToString(timeParams.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
//            final String endStr = TimeUtil.localDateTimeToString(timeParams.getEndTime(), "yyyy-MM-dd HH:mm:ss");
//            conditions.add(new Condition<>(HDSJ, Operator.GreaterThanOrEqual, beginStr));
//            conditions.add(new Condition<>(HDSJ, Operator.LessThanOrEqual, endStr));
//            conditions.add(new Condition<>(TZZHM, Operator.In, lists));
//
//            Integer pageNumber = request.getPageParams().getPageNumber();
//            Integer pageSize = request.getPageParams().getPageSize();
//            String sortField = HDSJ;
//
//            String sortDirection = request.getSortParams().getSortDirection();
//
//            final PageInfo pageInfo = PageInfo.newPage(pageNumber, pageSize);
//            if (DESC.equals(sortDirection)) {
//                pageInfo.setOrders(List.of(Order.desc(sortField)));
//            } else if (ASC.equals(sortDirection)) {
//                pageInfo.setOrders(List.of(Order.asc(sortField)));
//            } else {
//                // 默认倒序
//                pageInfo.setOrders(List.of(Order.desc(sortField)));
//            }
//            Expression e = ExpressionBuilder.And(conditions.toArray(new Expression[0]));
//            log.info("es检索条件：{}", warningTrackRepository.getSqlByExpression(e));
//            PageList<ThemeGjxxbEntity> pageList = warningTrackRepository.findPageList(
//                    e,
//                    pageInfo);
//            log.debug("result is :{}", JsonUtil.toJsonString(pageList));
//            List<TrackPointVO> collect = pageList.getContents().stream().map(item -> {
//                TrackPointVO trackPointVO = new TrackPointVO();
//                trackPointVO.setTime(item.getHdsj());
//                trackPointVO.setName(person.getName());
//                trackPointVO.setLocation(item.getHddz());
//                trackPointVO.setLng(Double.valueOf(item.getJdwgs84()));
//                trackPointVO.setLat(Double.valueOf(item.getWdwgs84()));
//                trackPointVO.setType(getParentDictForTrack(item.getGzylx()));
//                trackPointVO.setIsStayPoint(false);
//                trackPointVO.setSourceName(item.getGzymc());
//                trackPointVO.setId(String.valueOf(personId));
//                trackPointVO.setContent(new String[]{});
//                return trackPointVO;
//            }).collect(Collectors.toList());
//
//            PageResult<TrackPointVO> pageResult = PageResult.of(collect, pageNumber, pageList.getTotal(), pageSize);
//            pageResult.setTotal(pageList.getTotal());
//            return pageResult;
        } catch (Exception e) {
            log.error("查询轨迹列表失败", e);
            throw new RuntimeException("查询轨迹列表失败", e);
        }
    }

    /**
     * 构建es检索条件
     *
     * @param person 人员信息
     * @param request 查询条件
     * @return es检索条件
     */
    private AiGjDTO buildAiGjDTOFrom(Person person, ListParamsRequest request) {
        // 设置recordId为人员身份证号码
        if (person == null || person.getIdNumber() == null) {
            // 如果有多个身份证号，使用第一个
            throw new RuntimeException("人员信息或身份证号码为空");
        }
        PageParams pageParams = request.getPageParams();
        AiGjDTO dto = new AiGjDTO();

        // 设置基本分页参数
        dto.setPageNum(pageParams.getPageNumber());
        dto.setPageSize(pageParams.getPageSize());
        // 设置档案类型为人员
        dto.setArchivesType("person");
        // 设置recordId为人员身份证号码
        dto.setRecordId(person.getIdNumber());
        final TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(), "activeTime",
                    TimeParams.class);
        if (timeParams == null ||timeParams.getBeginTime() == null) {
            throw new RuntimeException("未传入时间参数！");
        }
        final String beginStr = TimeUtil.localDateTimeToString(timeParams.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
        final String endStr = TimeUtil.localDateTimeToString(timeParams.getEndTime(), "yyyy-MM-dd HH:mm:ss");

        // 设置时间范围
        dto.setStartTime(beginStr);
        dto.setEndTime(endStr);

        dto.setHavePhoto(1);
        dto.setHours("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
        dto.setGzylx("人像,停车场,卡口,手机");
        dto.setGeoPoints("");
        // 按活动时间分组
        dto.setGroupField("hdsj");
        return dto;
    }

    private Long getParentDictForTrack(String gzylx) {
        DictDto dict = dictService.getDictByTypeAndName(WARNING_SOURCE_TYPE, gzylx);
        if (dict == null) {
            return null;
        }
        if (dict.getPCode() != null && dict.getPCode() != 0) {
            return dictService.getDictById(dict.getPid()).getCode();
        }
        return dict.getCode();
    }

    /**
     * 按照字段统计人员的轨迹
     *
     * @param trajectoryQueryDTO 查询条件
     * @return 统计结果
     */
    @Override
    public List<TrackActivityTime> countTrackByColumn(TrajectoryQueryDTO trajectoryQueryDTO) {
        try {
            ArrayList<String> groupByColumns = new ArrayList<>();
            if (FUNCTION_HOUR.equals(trajectoryQueryDTO.getFunctionName())) {
                groupByColumns.add(YEAR);
                groupByColumns.add(MONTH);
                groupByColumns.add(DAY);
                groupByColumns.add(HOUR);
            }
            if (FUNCTION_DAY.equals(trajectoryQueryDTO.getFunctionName())) {
                groupByColumns.add(YEAR);
                groupByColumns.add(MONTH);
                groupByColumns.add(DAY);
            }
            List<Expression> conditions = new ArrayList<>(4);
            conditions.add(new Condition<>(HDSJ, Operator.GreaterThanOrEqual, trajectoryQueryDTO.getBeginStr()));
            conditions.add(new Condition<>(HDSJ, Operator.LessThanOrEqual, trajectoryQueryDTO.getEndStr()));
            conditions.add(new Condition<>(TZZHM, Operator.In, trajectoryQueryDTO.getIdentifiers()));
            SearchProperties searchProperties = new SearchProperties();
            searchProperties.addProperties("missing", MISSING_NUMBER);
            Either<TrsCrudException, List<CategoryResult>> either = warningTrackRepository.getRepository().categoryMultiFieldQuery(
                    warningTrackRepository.getTableName(),
                    ExpressionBuilder.And(conditions.toArray(new Expression[0])),
                    groupByColumns.toArray(new String[0]),
                    Long.MAX_VALUE,
                    searchProperties
            );
            List<TrackActivityTime> resultAgg = new ArrayList<>();
            if (either.isLeft()) {
                throw new RuntimeException("查询ES统计数据出错");
            }
            for (CategoryResult categoryResult : either.get()) {
                if (categoryResult.getCategoryMap() == null) {
                    continue;
                }
                String year = categoryResult.getCategoryMap().get(YEAR);
                String month = categoryResult.getCategoryMap().get(MONTH);
                String day = categoryResult.getCategoryMap().get(DAY);
                String hour = categoryResult.getCategoryMap().get(HOUR);
                if (MISSING_NUMBER.equals(year) || MISSING_NUMBER.equals(month) || MISSING_NUMBER.equals(day) || MISSING_NUMBER.equals(hour)) {
                    // 缺省的时间字段值不统计
                    continue;
                }
                TrackActivityTime trackActivityTime = new TrackActivityTime();
                trackActivityTime.setNodeName(getBuildValue(trajectoryQueryDTO.getFunctionName(), categoryResult));
                trackActivityTime.setNum(Integer.parseInt(String.valueOf(categoryResult.getIRecordNum())));
                resultAgg.add(trackActivityTime);
            }
            return resultAgg;
        } catch (Exception e) {
            log.error("es countTrackByTime error: ", e);
            throw new RuntimeException("es countTrackByTime error: ", e);
        }
    }

    private String getBuildValue(String functionName, CategoryResult categoryResult) {
        Map<String, String> categoryMap = categoryResult.getCategoryMap();
        String year = categoryMap.get(YEAR);
        String month = categoryMap.get(MONTH).length() == 1 ? "0" + categoryMap.get(MONTH) : categoryMap.get(MONTH);
        String day = categoryMap.get(DAY).length() == 1 ? "0" + categoryMap.get(DAY) : categoryMap.get(DAY);
        if (FUNCTION_HOUR.equals(functionName)) {
            String hour = categoryMap.get(HOUR).length() == 1 ? "0" + categoryMap.get(HOUR) : categoryMap.get(HOUR);
            return year + "-" + month + "-" + day + " " + hour + "时";
        }
        if (FUNCTION_DAY.equals(functionName)) {
            return year + "-" + month + "-" + day;
        }
        throw new IllegalArgumentException("不存在的统计方式" + functionName);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        TrajectoryQueryStrategyFactory.register(TrajectoryQueryStrategyFactory.ES, this);
    }
}
