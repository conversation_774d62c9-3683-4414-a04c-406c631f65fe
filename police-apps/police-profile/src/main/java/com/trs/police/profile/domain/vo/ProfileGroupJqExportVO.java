package com.trs.police.profile.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 群体相关警情导出
 *
 * @author: zuo.kaiyuan
 * @create: 2024-05-30
 */
@Data
public class ProfileGroupJqExportVO extends ProfileExportListVO{
    /**
     * 报警内容
     */
    private String bjnr;

    /**
     * 接警单位名称
     */
    private String jjdwmc;

    /**
     * 接警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date jjsj;
}
