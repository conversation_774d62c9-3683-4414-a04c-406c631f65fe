package com.trs.police.profile.factory.promotion;

import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 职级晋升规则策略工厂
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class PromotionRuleStrategyFactory {

    private final Map<String, PromotionRuleStrategy> strategyByRuleId = new HashMap<>();
    private final Map<String, PromotionRuleStrategy> strategyByName = new HashMap<>();
    private final Map<String, PromotionRuleStrategy> strategyByRuleType = new HashMap<>();

    @Autowired(required = false)
    private List<PromotionRuleStrategy> strategies;

    @PostConstruct
    public void init() {
        if (strategies != null) {
            strategies.forEach(this::registerStrategy);
        }
        log.info("职级晋升规则策略工厂初始化完成，共注册{}个策略", strategies != null ? strategies.size() : 0);
    }

    /**
     * 注册策略
     *
     * @param strategy 晋升规则策略
     */
    public void registerStrategy(PromotionRuleStrategy strategy) {
        if (strategy == null) {
            return;
        }

        String strategyName = strategy.getStrategyName();
        String ruleType = strategy.getSupportedRuleType();

        if (strategyName != null) {
            strategyByName.put(strategyName, strategy);
        }

        if (ruleType != null) {
            strategyByRuleType.put(ruleType, strategy);
        }

        log.debug("注册晋升规则策略：{}, 支持规则类型：{}", strategyName, ruleType);
    }

    /**
     * 根据规则ID获取策略
     *
     * @param ruleId 规则ID
     * @return 晋升规则策略
     */
    public PromotionRuleStrategy getStrategyByRuleId(String ruleId) {
        return strategyByRuleId.get(ruleId);
    }

    /**
     * 根据规则名称获取策略
     *
     * @param ruleName 规则名称
     * @return 晋升规则策略
     */
    public PromotionRuleStrategy getStrategyByRuleName(String ruleName) {
        return strategyByName.get(ruleName);
    }

    /**
     * 根据规则类型获取策略
     *
     * @param ruleType 规则类型
     * @return 晋升规则策略
     */
    public PromotionRuleStrategy getStrategyByRuleType(String ruleType) {
        return strategyByRuleType.get(ruleType);
    }

    /**
     * 获取策略（优先级：规则ID > 规则名称 > 规则类型）
     *
     * @param ruleId   规则ID
     * @param ruleName 规则名称
     * @param ruleType 规则类型
     * @return 晋升规则策略
     */
    public PromotionRuleStrategy getStrategy(String ruleId, String ruleName, String ruleType) {
        PromotionRuleStrategy strategy = null;

        // 1. 优先按规则ID查找
        if (ruleId != null) {
            strategy = getStrategyByRuleId(ruleId);
            if (strategy != null) {
                log.debug("根据规则ID找到策略：{} -> {}", ruleId, strategy.getStrategyName());
                return strategy;
            }
        }

        // 2. 按规则名称查找
        if (ruleName != null && !ruleName.trim().isEmpty()) {
            strategy = getStrategyByRuleName(ruleName);
            if (strategy != null) {
                log.debug("根据规则名称找到策略：{} -> {}", ruleName, strategy.getStrategyName());
                return strategy;
            }
        }

        // 3. 按规则类型查找
        if (ruleType != null && !ruleType.trim().isEmpty()) {
            strategy = getStrategyByRuleType(ruleType);
            if (strategy != null) {
                log.debug("根据规则类型找到策略：{} -> {}", ruleType, strategy.getStrategyName());
                return strategy;
            }
        }

        // 4. 遍历所有策略，使用supports方法判断
        for (PromotionRuleStrategy s : strategyByName.values()) {
            if (s.supports(ruleId, ruleName, ruleType)) {
                log.debug("通过supports方法找到策略：{},{},{} -> {}", ruleId, ruleName, ruleType, s.getStrategyName());
                return s;
            }
        }

        log.warn("未找到匹配的晋升规则策略，规则ID：{}，规则名称：{}，规则类型：{}", ruleId, ruleName, ruleType);
        return null;
    }

    /**
     * 获取所有已注册的策略
     *
     * @return 策略映射
     */
    public Map<String, PromotionRuleStrategy> getAllStrategies() {
        return new HashMap<>(strategyByName);
    }
}
