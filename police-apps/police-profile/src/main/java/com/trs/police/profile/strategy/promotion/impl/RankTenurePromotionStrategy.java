package com.trs.police.profile.strategy.promotion.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trs.police.profile.config.PromotionRankMappingConfig;
import com.trs.police.profile.constant.PromotionStatusEnum;
import com.trs.police.profile.domain.entity.zhzg.PoliceRankRelation;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.mapper.zhzg.PoliceRankRelationMapper;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Map;

/**
 * 职级任职年限晋升规则策略
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class RankTenurePromotionStrategy implements PromotionRuleStrategy {

    private static final String STRATEGY_NAME = "职级任职年限规则";
    private static final String RULE_TYPE = "RANK_TENURE";

    /**
     * 职级晋升任职年限要求配置
     * key: 目标职级代码, value: Map<前置职级代码, 要求年限>
     */
    private static final Map<Integer, Map<Integer, Integer>> RANK_TENURE_REQUIREMENTS = new HashMap<>();

    static {
        initTechnicalRankRequirements();
        initLawEnforcementRankRequirements();
    }

    /**
     * 初始化技术职务晋升要求
     */
    private static void initTechnicalRankRequirements() {
        // a. 晋升警务技术二级主任，应当任警务技术三级主任2年以上
        Map<Integer, Integer> tech2Requirements = new HashMap<>();
        tech2Requirements.put(13, 2); // 警务技术三级主任 -> 2年
        RANK_TENURE_REQUIREMENTS.put(12, tech2Requirements);

        // b. 晋升警务技术一级主任，应当任正处级领导职务或警务技术二级主任3年以上
        Map<Integer, Integer> tech1Requirements = new HashMap<>();
        tech1Requirements.put(12, 3); // 警务技术二级主任 -> 3年
        tech1Requirements.put(101, 3); // 正处级领导职务 -> 3年
        RANK_TENURE_REQUIREMENTS.put(11, tech1Requirements);

        // c. 晋升警务技术三级主任，应当任副处级领导职务或警务技术四级主任2年以上
        Map<Integer, Integer> tech3Requirements = new HashMap<>();
        tech3Requirements.put(14, 2); // 警务技术四级主任 -> 2年
        tech3Requirements.put(102, 2); // 副处级领导职务 -> 2年
        RANK_TENURE_REQUIREMENTS.put(13, tech3Requirements);

        // d. 晋升警务技术四级主任，应当任警务技术一级主管2年以上
        Map<Integer, Integer> tech4Requirements = new HashMap<>();
        tech4Requirements.put(21, 2); // 警务技术一级主管 -> 2年
        RANK_TENURE_REQUIREMENTS.put(14, tech4Requirements);

        // e. 晋升警务技术一级主管，应当任正科级领导职务或警务技术二级主管2年以上
        Map<Integer, Integer> techMgr1Requirements = new HashMap<>();
        techMgr1Requirements.put(22, 2); // 警务技术二级主管 -> 2年
        techMgr1Requirements.put(103, 2); // 正科级领导职务 -> 2年
        RANK_TENURE_REQUIREMENTS.put(21, techMgr1Requirements);

        // f. 晋升警务技术二级主管，应当任警务技术三级主管2年以上
        Map<Integer, Integer> techMgr2Requirements = new HashMap<>();
        techMgr2Requirements.put(23, 2); // 警务技术三级主管 -> 2年
        RANK_TENURE_REQUIREMENTS.put(22, techMgr2Requirements);

        // g. 晋升警务技术三级主管，应当任副科级领导职务或警务技术四级主管2年以上
        Map<Integer, Integer> techMgr3Requirements = new HashMap<>();
        techMgr3Requirements.put(24, 2); // 警务技术四级主管 -> 2年
        techMgr3Requirements.put(104, 2); // 副科级领导职务 -> 2年
        RANK_TENURE_REQUIREMENTS.put(23, techMgr3Requirements);

        // h. 晋升警务技术四级主管，应当任警务技术员2年以上
        Map<Integer, Integer> techMgr4Requirements = new HashMap<>();
        techMgr4Requirements.put(25, 2); // 警务技术员 -> 2年
        RANK_TENURE_REQUIREMENTS.put(24, techMgr4Requirements);
    }

    /**
     * 初始化执法勤务晋升要求
     */
    private static void initLawEnforcementRankRequirements() {
        // a. 晋升一级高级警长，应当任正处级领导职务或二级高级警长3年以上
        Map<Integer, Integer> senior1Requirements = new HashMap<>();
        senior1Requirements.put(29, 3); // 二级高级警长 -> 3年
        senior1Requirements.put(101, 3); // 正处级领导职务 -> 3年
        RANK_TENURE_REQUIREMENTS.put(28, senior1Requirements);

        // b. 晋升二级高级警长，应当任三级高级警长2年以上
        Map<Integer, Integer> senior2Requirements = new HashMap<>();
        senior2Requirements.put(30, 2); // 三级高级警长 -> 2年
        RANK_TENURE_REQUIREMENTS.put(29, senior2Requirements);

        // c. 晋升三级高级警长，应当任副处级领导职务或四级高级警长2年以上
        Map<Integer, Integer> senior3Requirements = new HashMap<>();
        senior3Requirements.put(31, 2); // 四级高级警长 -> 2年
        senior3Requirements.put(102, 2); // 副处级领导职务 -> 2年
        RANK_TENURE_REQUIREMENTS.put(30, senior3Requirements);

        // d. 晋升四级高级警长，应当任一级警长2年以上
        Map<Integer, Integer> senior4Requirements = new HashMap<>();
        senior4Requirements.put(41, 2); // 一级警长 -> 2年
        RANK_TENURE_REQUIREMENTS.put(31, senior4Requirements);

        // e. 晋升一级警长，应当正科级领导职务或二级警长2年以上
        Map<Integer, Integer> sergeant1Requirements = new HashMap<>();
        sergeant1Requirements.put(42, 2); // 二级警长 -> 2年
        sergeant1Requirements.put(103, 2); // 正科级领导职务 -> 2年
        RANK_TENURE_REQUIREMENTS.put(41, sergeant1Requirements);

        // f. 晋升二级警长，应当任三级警长2年以上
        Map<Integer, Integer> sergeant2Requirements = new HashMap<>();
        sergeant2Requirements.put(43, 2); // 三级警长 -> 2年
        RANK_TENURE_REQUIREMENTS.put(42, sergeant2Requirements);

        // g. 晋升三级警长，应当任副科级领导职务或四级警长2年以上
        Map<Integer, Integer> sergeant3Requirements = new HashMap<>();
        sergeant3Requirements.put(44, 2); // 四级警长 -> 2年
        sergeant3Requirements.put(104, 2); // 副科级领导职务 -> 2年
        RANK_TENURE_REQUIREMENTS.put(43, sergeant3Requirements);

        // h. 晋升四级警长，应当任一级警员2年以上
        Map<Integer, Integer> sergeant4Requirements = new HashMap<>();
        sergeant4Requirements.put(51, 2); // 一级警员 -> 2年
        RANK_TENURE_REQUIREMENTS.put(44, sergeant4Requirements);

        // i. 晋升一级警员，应当任二级警员2年以上
        Map<Integer, Integer> officer1Requirements = new HashMap<>();
        officer1Requirements.put(52, 2); // 二级警员 -> 2年
        RANK_TENURE_REQUIREMENTS.put(51, officer1Requirements);
    }

    @Override
    public PromotionRuleDetailVO evaluateRule(ProfilePolice police, Integer targetRankCode) {
        log.debug("开始评估职级任职年限规则，警员：{}，目标职级：{}", police.getName(), targetRankCode);

        PromotionRuleDetailVO.PromotionRuleDetailVOBuilder builder = PromotionRuleDetailVO.builder()
                .ruleName(getStrategyName())
                .description(getDescription())
                .ruleType(getSupportedRuleType())
                .required(true)  // 任职年限是必要条件
                .success(true);

        try {
            // 验证数据
            if (!validatePolice(police) || !validateTargetRank(targetRankCode)) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取任职年限要求
            Map<Integer, Integer> requirements = RANK_TENURE_REQUIREMENTS.get(targetRankCode);
            if (requirements == null || requirements.isEmpty()) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription("未找到该职级的任职年限要求配置")
                        .build();
            }

            // TODO: 获取警员当前职级和任职时间
            // 这里需要从职级变动记录表中查询警员的职级历史
            Integer currentRankCode = getCurrentRankCode(police);
            LocalDate currentRankStartDate = getCurrentRankStartDate(police);

            if (currentRankCode == null || currentRankStartDate == null) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription("缺少当前职级或任职时间信息")
                        .build();
            }

            // 检查是否满足任职年限要求
            Integer requiredYears = requirements.get(currentRankCode);
            if (requiredYears == null) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription(String.format("当前职级%d不符合晋升到职级%d的前置条件", 
                                currentRankCode, targetRankCode))
                        .build();
            }

            // 计算任职年限
            LocalDate currentDate = LocalDate.now();
            Period period = Period.between(currentRankStartDate, currentDate);
            int actualYears = period.getYears();
            int actualMonths = period.getMonths();

            boolean isQualified = actualYears >= requiredYears;
            PromotionStatusEnum result = isQualified ? PromotionStatusEnum.QUALIFIED : PromotionStatusEnum.NOT_QUALIFIED;

            String hitData = String.format("当前职级任职时间：%d年%d个月", actualYears, actualMonths);
            String calculateDescription;

            if (isQualified) {
                calculateDescription = String.format("当前职级任职%d年%d个月，满足要求（≥%d年），符合晋升条件",
                        actualYears, actualMonths, requiredYears);
            } else {
                calculateDescription = String.format("当前职级任职%d年%d个月，不满足要求（≥%d年），不符合晋升条件",
                        actualYears, actualMonths, requiredYears);
            }

            return builder
                    .isHit(!isQualified)
                    .ruleResult(result)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("评估职级任职年限规则失败，警员：{}，目标职级：{}，错误：{}", 
                    police.getName(), targetRankCode, e.getMessage(), e);
            return builder
                    .isHit(false)
                    .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                    .success(false)
                    .errorMessage("评估失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    /**
     * 获取警员当前职级代码
     * TODO: 需要从职级变动记录表中查询
     */
    private Integer getCurrentRankCode(ProfilePolice police) {
        // 这里需要查询警员的当前职级
        // 暂时返回模拟数据
        return 29; // 假设当前是三级高级警长
    }

    /**
     * 获取警员当前职级开始时间
     * TODO: 需要从职级变动记录表中查询
     */
    private LocalDate getCurrentRankStartDate(ProfilePolice police) {
        // 这里需要查询警员当前职级的开始时间
        // 暂时返回模拟数据
        return LocalDate.now().minusYears(3); // 假设3年前开始任现职
    }
}
