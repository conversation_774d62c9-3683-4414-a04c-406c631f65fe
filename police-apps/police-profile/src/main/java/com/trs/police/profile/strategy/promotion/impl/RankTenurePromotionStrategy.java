package com.trs.police.profile.strategy.promotion.impl;

import com.trs.police.profile.constant.PromotionStatusEnum;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * 职级任职年限晋升规则策略
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class RankTenurePromotionStrategy implements PromotionRuleStrategy {

    private static final String STRATEGY_NAME = "职级任职年限规则";
    private static final String RULE_TYPE = "RANK_TENURE";

    /**
     * 职级晋升任职年限要求配置
     * key: 目标职级代码, value: Map<前置职级代码, 要求年限>
     */
    private static final Map<Integer, Map<Integer, Integer>> RANK_TENURE_REQUIREMENTS = new HashMap<>();

    static {
        initTechnicalRankRequirements();
        initLawEnforcementRankRequirements();
    }

    /**
     * 初始化技术职务晋升要求
     */
    private static void initTechnicalRankRequirements() {
        // 警务技术二级主任
        Map<Integer, Integer> tech2Requirements = new HashMap<>();
        tech2Requirements.put(13, 2); // 任警务技术三级主任2年以上
        RANK_TENURE_REQUIREMENTS.put(12, tech2Requirements);

        // 警务技术一级主任
        Map<Integer, Integer> tech1Requirements = new HashMap<>();
        tech1Requirements.put(12, 3); // 任警务技术二级主任3年以上
        // tech1Requirements.put(正处级领导职务代码, 3); // 任正处级领导职务3年以上
        RANK_TENURE_REQUIREMENTS.put(11, tech1Requirements);

        // 警务技术三级主任
        Map<Integer, Integer> tech3Requirements = new HashMap<>();
        tech3Requirements.put(14, 2); // 任警务技术四级主任2年以上
        // tech3Requirements.put(副处级领导职务代码, 2); // 任副处级领导职务2年以上
        RANK_TENURE_REQUIREMENTS.put(13, tech3Requirements);

        // 警务技术四级主任
        Map<Integer, Integer> tech4Requirements = new HashMap<>();
        tech4Requirements.put(21, 2); // 任警务技术一级主管2年以上
        RANK_TENURE_REQUIREMENTS.put(14, tech4Requirements);

        // 警务技术一级主管
        Map<Integer, Integer> techMgr1Requirements = new HashMap<>();
        techMgr1Requirements.put(22, 2); // 任警务技术二级主管2年以上
        // techMgr1Requirements.put(正科级领导职务代码, 2); // 任正科级领导职务2年以上
        RANK_TENURE_REQUIREMENTS.put(21, techMgr1Requirements);

        // 警务技术二级主管
        Map<Integer, Integer> techMgr2Requirements = new HashMap<>();
        techMgr2Requirements.put(23, 2); // 任警务技术三级主管2年以上
        RANK_TENURE_REQUIREMENTS.put(22, techMgr2Requirements);

        // 警务技术三级主管
        Map<Integer, Integer> techMgr3Requirements = new HashMap<>();
        techMgr3Requirements.put(24, 2); // 任警务技术四级主管2年以上
        // techMgr3Requirements.put(副科级领导职务代码, 2); // 任副科级领导职务2年以上
        RANK_TENURE_REQUIREMENTS.put(23, techMgr3Requirements);

        // 警务技术四级主管
        Map<Integer, Integer> techMgr4Requirements = new HashMap<>();
        techMgr4Requirements.put(25, 2); // 任警务技术员2年以上
        RANK_TENURE_REQUIREMENTS.put(24, techMgr4Requirements);
    }

    /**
     * 初始化执法勤务晋升要求
     */
    private static void initLawEnforcementRankRequirements() {
        // 一级高级警长
        Map<Integer, Integer> senior1Requirements = new HashMap<>();
        senior1Requirements.put(28, 3); // 任二级高级警长3年以上
        // senior1Requirements.put(正处级领导职务代码, 3); // 任正处级领导职务3年以上
        RANK_TENURE_REQUIREMENTS.put(27, senior1Requirements);

        // 二级高级警长
        Map<Integer, Integer> senior2Requirements = new HashMap<>();
        senior2Requirements.put(29, 2); // 任三级高级警长2年以上
        RANK_TENURE_REQUIREMENTS.put(28, senior2Requirements);

        // 三级高级警长
        Map<Integer, Integer> senior3Requirements = new HashMap<>();
        senior3Requirements.put(30, 2); // 任四级高级警长2年以上
        // senior3Requirements.put(副处级领导职务代码, 2); // 任副处级领导职务2年以上
        RANK_TENURE_REQUIREMENTS.put(29, senior3Requirements);

        // 四级高级警长
        Map<Integer, Integer> senior4Requirements = new HashMap<>();
        senior4Requirements.put(31, 2); // 任一级警长2年以上
        RANK_TENURE_REQUIREMENTS.put(30, senior4Requirements);

        // 一级警长
        Map<Integer, Integer> officer1Requirements = new HashMap<>();
        officer1Requirements.put(32, 2); // 任二级警长2年以上
        // officer1Requirements.put(正科级领导职务代码, 2); // 任正科级领导职务2年以上
        RANK_TENURE_REQUIREMENTS.put(31, officer1Requirements);

        // 二级警长
        Map<Integer, Integer> officer2Requirements = new HashMap<>();
        officer2Requirements.put(33, 2); // 任三级警长2年以上
        RANK_TENURE_REQUIREMENTS.put(32, officer2Requirements);

        // 三级警长
        Map<Integer, Integer> officer3Requirements = new HashMap<>();
        officer3Requirements.put(34, 2); // 任四级警长2年以上
        // officer3Requirements.put(副科级领导职务代码, 2); // 任副科级领导职务2年以上
        RANK_TENURE_REQUIREMENTS.put(33, officer3Requirements);

        // 四级警长
        Map<Integer, Integer> officer4Requirements = new HashMap<>();
        officer4Requirements.put(35, 2); // 任一级警员2年以上
        RANK_TENURE_REQUIREMENTS.put(34, officer4Requirements);

        // 一级警员
        Map<Integer, Integer> constable1Requirements = new HashMap<>();
        constable1Requirements.put(36, 2); // 任二级警员2年以上
        RANK_TENURE_REQUIREMENTS.put(35, constable1Requirements);
    }

    @Override
    public PromotionRuleDetailVO evaluateRule(ProfilePolice police, Integer targetRankCode) {
        log.debug("开始评估职级任职年限规则，警员：{}，目标职级：{}", police.getName(), targetRankCode);

        PromotionRuleDetailVO.PromotionRuleDetailVOBuilder builder = PromotionRuleDetailVO.builder()
                .ruleName(getStrategyName())
                .description(getDescription())
                .ruleType(getSupportedRuleType())
                .required(true)  // 任职年限是必要条件
                .success(true);

        try {
            // 验证数据
            if (!validatePolice(police) || !validateTargetRank(targetRankCode)) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取任职年限要求
            Map<Integer, Integer> requirements = RANK_TENURE_REQUIREMENTS.get(targetRankCode);
            if (requirements == null || requirements.isEmpty()) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription("未找到该职级的任职年限要求配置")
                        .build();
            }

            // TODO: 获取警员当前职级和任职时间
            // 这里需要从职级变动记录表中查询警员的职级历史
            Integer currentRankCode = getCurrentRankCode(police);
            LocalDate currentRankStartDate = getCurrentRankStartDate(police);

            if (currentRankCode == null || currentRankStartDate == null) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription("缺少当前职级或任职时间信息")
                        .build();
            }

            // 检查是否满足任职年限要求
            Integer requiredYears = requirements.get(currentRankCode);
            if (requiredYears == null) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription(String.format("当前职级%d不符合晋升到职级%d的前置条件", 
                                currentRankCode, targetRankCode))
                        .build();
            }

            // 计算任职年限
            LocalDate currentDate = LocalDate.now();
            Period period = Period.between(currentRankStartDate, currentDate);
            int actualYears = period.getYears();
            int actualMonths = period.getMonths();

            boolean isQualified = actualYears >= requiredYears;
            PromotionStatusEnum result = isQualified ? PromotionStatusEnum.QUALIFIED : PromotionStatusEnum.NOT_QUALIFIED;

            String hitData = String.format("当前职级任职时间：%d年%d个月", actualYears, actualMonths);
            String calculateDescription;

            if (isQualified) {
                calculateDescription = String.format("当前职级任职%d年%d个月，满足要求（≥%d年），符合晋升条件",
                        actualYears, actualMonths, requiredYears);
            } else {
                calculateDescription = String.format("当前职级任职%d年%d个月，不满足要求（≥%d年），不符合晋升条件",
                        actualYears, actualMonths, requiredYears);
            }

            return builder
                    .isHit(!isQualified)
                    .ruleResult(result)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("评估职级任职年限规则失败，警员：{}，目标职级：{}，错误：{}", 
                    police.getName(), targetRankCode, e.getMessage(), e);
            return builder
                    .isHit(false)
                    .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                    .success(false)
                    .errorMessage("评估失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    /**
     * 获取警员当前职级代码
     * TODO: 需要从职级变动记录表中查询
     */
    private Integer getCurrentRankCode(ProfilePolice police) {
        // 这里需要查询警员的当前职级
        // 暂时返回模拟数据
        return 29; // 假设当前是三级高级警长
    }

    /**
     * 获取警员当前职级开始时间
     * TODO: 需要从职级变动记录表中查询
     */
    private LocalDate getCurrentRankStartDate(ProfilePolice police) {
        // 这里需要查询警员当前职级的开始时间
        // 暂时返回模拟数据
        return LocalDate.now().minusYears(3); // 假设3年前开始任现职
    }
}
