package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 智慧政工积分规则表
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_zhzg_score_rule", autoResultMap = true)
public class ZhzgScoreRuleEntity extends AbstractBaseEntity {
    /**
     * 规则名称
     */
    private String name;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 规则分数
     */
    private Integer score;

    /**
     * 满分
     */
    private Integer fullScore;

    /**
     * 是否是叶子结点
     *
     */
    private Boolean isLeaf;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 适用职级
     */
    private Integer applicableRank;
}
