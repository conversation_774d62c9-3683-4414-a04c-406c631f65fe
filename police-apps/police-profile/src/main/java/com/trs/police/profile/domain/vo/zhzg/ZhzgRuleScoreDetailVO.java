package com.trs.police.profile.domain.vo.zhzg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 智慧政工规则积分详情VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ZhzgRuleScoreDetailVO {

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDescription;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 是否为叶子节点
     */
    private Boolean isLeaf;

    /**
     * 父规则ID
     */
    private Long parentRuleId;

    /**
     * 获得的积分
     */
    private Double score;

    /**
     * 规则配置的最大分值
     */
    private Integer maxScore;

    /**
     * 是否命中规则
     */
    private Boolean isHit;

    /**
     * 命中的具体数据
     */
    private String hitData;

    /**
     * 计算说明
     */
    private String calculateDescription;

    /**
     * 是否计算成功
     */
    private Boolean success;

    /**
     * 错误信息（如果计算失败）
     */
    private String errorMessage;

}
