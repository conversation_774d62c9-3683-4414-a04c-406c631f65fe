package com.trs.police.profile.domain.vo.promotion;

import com.trs.police.profile.constant.PromotionStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 职级晋升规则详情VO
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionRuleDetailVO {

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 是否命中规则
     */
    private Boolean isHit;

    /**
     * 规则评估结果
     */
    private PromotionStatusEnum ruleResult;

    /**
     * 规则评估结果代码
     */
    private Integer ruleResultCode;

    /**
     * 规则评估结果名称
     */
    private String ruleResultName;

    /**
     * 命中的数据
     */
    private String hitData;

    /**
     * 计算描述
     */
    private String calculateDescription;

    /**
     * 是否成功
     */
    @Builder.Default
    private Boolean success = true;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 是否为必要条件
     */
    private Boolean required;

    /**
     * 获取规则评估结果代码
     */
    public Integer getRuleResultCode() {
        return ruleResult != null ? ruleResult.getCode() : null;
    }

    /**
     * 获取规则评估结果名称
     */
    public String getRuleResultName() {
        return ruleResult != null ? ruleResult.getName() : null;
    }
}
