package com.trs.police.profile.util;

import com.trs.police.profile.constant.PoliceRankEnum;
import com.trs.police.profile.constant.PoliceRankEnum.PoliceRankCategory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 警员职级工具类
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
public class PoliceRankUtil {

    /**
     * 职级代码到类别的映射缓存
     */
    private static final Map<Integer, PoliceRankCategory> CODE_TO_CATEGORY_CACHE = new HashMap<>();

    static {
        // 初始化缓存
        for (PoliceRankEnum rank : PoliceRankEnum.values()) {
            CODE_TO_CATEGORY_CACHE.put(rank.getCode(), rank.getCategory());
        }
    }

    /**
     * 根据职级代码获取职级类别
     *
     * @param rankCode 职级代码
     * @return 职级类别
     */
    public static PoliceRankCategory getCategoryByCode(Integer rankCode) {
        if (rankCode == null) {
            return null;
        }
        return CODE_TO_CATEGORY_CACHE.get(rankCode);
    }

    /**
     * 根据职级代码获取职级名称
     *
     * @param rankCode 职级代码
     * @return 职级名称
     */
    public static String getRankNameByCode(Integer rankCode) {
        if (rankCode == null) {
            return null;
        }
        
        List<PoliceRankEnum> ranks = PoliceRankEnum.getByCode(rankCode);
        if (ranks.isEmpty()) {
            return null;
        }
        
        // 如果有多个相同代码的职级，返回第一个的名称
        return ranks.get(0).getName();
    }

    /**
     * 根据职级代码和类别获取职级名称
     *
     * @param category 职级类别
     * @param rankCode 职级代码
     * @return 职级名称
     */
    public static String getRankName(PoliceRankCategory category, Integer rankCode) {
        PoliceRankEnum rank = PoliceRankEnum.getByCode(category, rankCode);
        return rank != null ? rank.getName() : null;
    }

    /**
     * 判断职级代码是否为执法勤务系列
     *
     * @param rankCode 职级代码
     * @return 是否为执法勤务系列
     */
    public static boolean isLawEnforcementRank(Integer rankCode) {
        PoliceRankCategory category = getCategoryByCode(rankCode);
        return category == PoliceRankCategory.LAW_ENFORCEMENT;
    }

    /**
     * 判断职级代码是否为技术职务系列
     *
     * @param rankCode 职级代码
     * @return 是否为技术职务系列
     */
    public static boolean isTechnicalRank(Integer rankCode) {
        PoliceRankCategory category = getCategoryByCode(rankCode);
        return category == PoliceRankCategory.TECHNICAL;
    }

    /**
     * 判断职级代码是否为高级警长系列（需要年满50周岁）
     *
     * @param rankCode 职级代码
     * @return 是否为高级警长系列
     */
    public static boolean isSeniorRank(Integer rankCode) {
        if (!isLawEnforcementRank(rankCode)) {
            return false;
        }
        PoliceRankEnum rank = PoliceRankEnum.getByCode(PoliceRankCategory.LAW_ENFORCEMENT, rankCode);
        return rank != null && rank.isSeniorRank();
    }

    /**
     * 判断职级代码是否为主任系列
     *
     * @param rankCode 职级代码
     * @return 是否为主任系列
     */
    public static boolean isDirectorRank(Integer rankCode) {
        if (!isTechnicalRank(rankCode)) {
            return false;
        }
        PoliceRankEnum rank = PoliceRankEnum.getByCode(PoliceRankCategory.TECHNICAL, rankCode);
        return rank != null && rank.isDirectorRank();
    }

    /**
     * 判断职级代码是否为主管系列
     *
     * @param rankCode 职级代码
     * @return 是否为主管系列
     */
    public static boolean isSupervisorRank(Integer rankCode) {
        if (!isTechnicalRank(rankCode)) {
            return false;
        }
        PoliceRankEnum rank = PoliceRankEnum.getByCode(PoliceRankCategory.TECHNICAL, rankCode);
        return rank != null && rank.isSupervisorRank();
    }

    /**
     * 获取职级的完整描述
     *
     * @param rankCode 职级代码
     * @return 完整描述
     */
    public static String getFullDescription(Integer rankCode) {
        if (rankCode == null) {
            return null;
        }
        
        List<PoliceRankEnum> ranks = PoliceRankEnum.getByCode(rankCode);
        if (ranks.isEmpty()) {
            return "未知职级(" + rankCode + ")";
        }
        
        // 如果有多个相同代码的职级，返回第一个的描述
        return ranks.get(0).getFullDescription();
    }

    /**
     * 获取职级类别的中文名称
     *
     * @param category 职级类别
     * @return 中文名称
     */
    public static String getCategoryName(PoliceRankCategory category) {
        return category != null ? category.getCategoryName() : null;
    }

    /**
     * 获取职级类别的代码
     *
     * @param category 职级类别
     * @return 类别代码
     */
    public static String getCategoryCode(PoliceRankCategory category) {
        return category != null ? category.getCategoryCode() : null;
    }

    /**
     * 根据类别代码获取职级类别
     *
     * @param categoryCode 类别代码
     * @return 职级类别
     */
    public static PoliceRankCategory getCategoryByCode(String categoryCode) {
        if (categoryCode == null || categoryCode.trim().isEmpty()) {
            return null;
        }
        
        for (PoliceRankCategory category : PoliceRankCategory.values()) {
            if (category.getCategoryCode().equals(categoryCode.trim())) {
                return category;
            }
        }
        return null;
    }

    /**
     * 验证职级代码是否有效
     *
     * @param rankCode 职级代码
     * @return 是否有效
     */
    public static boolean isValidRankCode(Integer rankCode) {
        return rankCode != null && CODE_TO_CATEGORY_CACHE.containsKey(rankCode);
    }

    /**
     * 获取所有有效的职级代码
     *
     * @return 职级代码列表
     */
    public static List<Integer> getAllValidRankCodes() {
        return PoliceRankEnum.getAllCodes();
    }

    /**
     * 获取指定类别的所有职级代码
     *
     * @param category 职级类别
     * @return 职级代码列表
     */
    public static List<Integer> getRankCodesByCategory(PoliceRankCategory category) {
        return PoliceRankEnum.getByCategory(category)
                .stream()
                .map(PoliceRankEnum::getCode)
                .distinct()
                .sorted()
                .toList();
    }
}
