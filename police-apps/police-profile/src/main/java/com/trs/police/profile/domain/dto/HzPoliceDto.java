package com.trs.police.profile.domain.dto;

import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 慧政 police dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HzPoliceDto extends BaseDTO {
    private Number zjxl; // 职级序列，码表，type = police_rz_xl，传code
    private Number zj; // 职级，码表，职级序列为执法勤务警员时，type=police_zj；职级序列为警务技术职务时，type = police_jwjs_zj
    private String jszgqk; // 晋升资格情况，码表，type = police_emergency_status，传code
    private Number deptId; // 部门id
    private String ageRange; // 年龄范围，码表，type = police_age_range，传code
    private String searchKey; // 姓名: name; 身份证: idCard; 手机号: tel
    private String searchValue;
}
