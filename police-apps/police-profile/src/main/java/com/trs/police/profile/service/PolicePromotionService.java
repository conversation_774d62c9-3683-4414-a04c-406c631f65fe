package com.trs.police.profile.service;

import com.trs.police.profile.domain.dto.promotion.PromotionEvaluationRequest;
import com.trs.police.profile.domain.vo.promotion.PromotionEvaluationResult;

import java.util.List;

/**
 * 警员职级晋升服务接口
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
public interface PolicePromotionService {

    /**
     * 评估单个警员的晋升资格
     *
     * @param request 评估请求
     * @return 评估结果
     */
    PromotionEvaluationResult evaluatePromotion(PromotionEvaluationRequest request);

    /**
     * 批量评估警员的晋升资格
     *
     * @param requests 评估请求列表
     * @return 评估结果列表
     */
    List<PromotionEvaluationResult> batchEvaluatePromotion(List<PromotionEvaluationRequest> requests);

    /**
     * 根据警员ID评估晋升资格
     *
     * @param policeId 警员档案ID
     * @return 评估结果
     */
    PromotionEvaluationResult evaluatePromotionById(Long policeId);

    /**
     * 根据身份证号评估晋升资格
     *
     * @param idNumber 身份证号
     * @return 评估结果
     */
    PromotionEvaluationResult evaluatePromotionByIdNumber(String idNumber);

    /**
     * 更新警员的晋升状态到数据库
     *
     * @param policeId       警员档案ID
     * @param promotionStatus 晋升状态代码
     * @return 是否更新成功
     */
    boolean updatePromotionStatus(Long policeId, Integer promotionStatus);

    /**
     * 批量更新警员的晋升状态
     *
     * @param policeIds       警员档案ID列表
     * @param promotionStatus 晋升状态代码
     * @return 更新成功的数量
     */
    int batchUpdatePromotionStatus(List<Long> policeIds, Integer promotionStatus);

    /**
     * 执行晋升状态批量计算任务
     * 计算所有符合条件的警员的晋升状态并更新到数据库
     *
     * @return 处理的警员数量
     */
    int executePromotionStatusCalculation();

    /**
     * 根据部门ID计算该部门所有警员的晋升状态
     *
     * @param deptId 部门ID
     * @return 处理的警员数量
     */
    int calculatePromotionStatusByDept(Long deptId);

    /**
     * 获取指定职级的目标晋升职级
     *
     * @param currentRankCode 当前职级代码
     * @return 目标晋升职级代码，如果无法晋升则返回null
     */
    Integer getTargetPromotionRank(Integer currentRankCode);

    /**
     * 检查指定职级是否支持晋升
     *
     * @param currentRankCode 当前职级代码
     * @return 是否支持晋升
     */
    boolean canPromote(Integer currentRankCode);
}
