package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 警员档案表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_profile_police", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class ProfilePolice extends AbstractBaseEntity {

    /**
     * 出生日期
     */
    @TableField("birthday")
    private Date birthday;

    /**
     * 证件号码
     */
    @TableField("id_number")
    private String idNumber;

    /**
     * 证件类型：1：身份证，2：护照
     */
    @TableField("id_type")
    private Integer idType;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;

    /**
     * 性别
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 晋升状态，码表，type = police_jszt,符合晋升：1；不符合晋升：2；不得晋升：3；暂缓晋升：4
     */
    @TableField("promotion_status")
    private Integer promotionStatus;

    /**
     * 曾用名
     */
    @TableField("former_name")
    private String formerName;

    /**
     * 民族
     */
    @TableField("nation")
    private Integer nation;

    /**
     * 政治面貌
     */
    @TableField("political_status")
    private Integer politicalStatus;

    /**
     * 婚姻状况
     */
    @TableField("martial_status")
    private Integer martialStatus;

    /**
     * 户籍地区域代码
     */
    @TableField("registered_residence")
    private String registeredResidence;

    /**
     * 户籍地详细地址
     */
    @TableField("registered_residence_detail")
    private String registeredResidenceDetail;

    /**
     * 现住址区域代码
     */
    @TableField("current_residence")
    private String currentResidence;

    /**
     * 现住址详细地址
     */
    @TableField("current_residence_detail")
    private String currentResidenceDetail;

    /**
     * 照片（JSON格式存储）
     */
    @TableField("photo")
    private String photo;

    /**
     * 所属部门（JSON格式存储）
     */
    @TableField("dept_ids")
    private String deptIds;

    /**
     * 联系方式（JSON格式存储）
     */
    @TableField("tel")
    private String tel;

    /**
     * 家庭关系id（JSON格式存储）
     */
    @TableField("family_relation_ids")
    private String familyRelationIds;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 警号（警员特有字段）
     */
    @TableField("police_number")
    private String policeNumber;

    /**
     * 籍贯（警员特有字段）
     */
    @TableField("native_place")
    private String nativePlace;

    /**
     * 参加工作日期（警员特有字段）
     */
    @TableField("join_work_date")
    private Date joinWorkDate;

    /**
     * 参加公安工作日期（警员特有字段）
     */
    @TableField("join_public_security_work_date")
    private Date joinPublicSecurityWorkDate;

    /**
     * 积分
     */
    @TableField("score")
    private Double score;
}
