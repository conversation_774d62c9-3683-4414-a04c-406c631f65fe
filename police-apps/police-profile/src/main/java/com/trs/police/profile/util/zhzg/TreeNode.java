package com.trs.police.profile.util.zhzg;

import java.util.List;

/**
 * 树节点接口，定义具有ID、父ID和子节点列表的基本行为
 *
 * @param <T> 节点类型
 * @param <ID> ID类型
 */
public interface TreeNode<T extends TreeNode<T, ID>, ID> {
    /**
     * 获取节点ID
     *
     * @return 节点ID
     */
    ID getId();

    /**
     * 获取父节点ID
     *
     * @return 父节点ID
     */
    ID getParentId();

    /**
     * 获取子节点列表
     *
     * @return 子节点列表
     */
    List<T> getChildren();

    /**
     * 设置子节点列表
     *
     * @param children 子节点列表
     */
    void setChildren(List<T> children);
}
