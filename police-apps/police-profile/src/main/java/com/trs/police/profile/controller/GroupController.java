package com.trs.police.profile.controller;

import com.trs.police.common.core.annotation.SkipResponseBodyAdvice;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.GroupClassificationParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.AreaStatisticsVO;
import com.trs.police.common.core.vo.ImportResultVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.profile.GroupStatisticVO;
import com.trs.police.common.core.vo.profile.GroupVO;
import com.trs.police.common.core.vo.profile.JqCommonVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.vo.GroupListVO;
import com.trs.police.profile.domain.dto.GroupEventDTO;
import com.trs.police.profile.domain.dto.GroupJqDTO;
import com.trs.police.profile.domain.dto.GroupPersonDTO;
import com.trs.police.profile.domain.dto.SaveGtDTO;
import com.trs.police.profile.domain.vo.*;
import com.trs.police.profile.excel.ImportService;
import com.trs.police.profile.excel.ImportVO;
import com.trs.police.profile.service.*;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 群体API接口
 *
 * <AUTHOR>
 * @date 2022/08/31
 */
@RestController
@RequestMapping("/group")
public class GroupController {

    @Resource
    private GroupService groupService;
    @Resource
    private GroupDetailService groupDetailService;
    @Resource
    ImportService importService;
    @Resource
    private FkGroupService fkGroupService;
    @Resource
    private GroupExportService  groupExportService;
    @Resource
    private JqService jqService;

    /**
     * 群体列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link GroupVO}
     */
    @PostMapping("/list")
    public PageResult<GroupVO> selectList(@RequestBody ListParamsRequest paramsRequest) {
        return groupService.getPage(paramsRequest);
    }

    /**
     * 获取群体列表 v2
     *
     * @param paramsRequest 列表请求参数
     * @return {@link GroupListVO}
     */
    @PostMapping("/groupList")
    public PageResult<GroupListVO> groupList(@RequestBody ListParamsRequest paramsRequest) {
        return groupService.groupList(paramsRequest);
    }

    /**
     * 删除群体
     *
     * @param idList       群体id列表
     * @param policeKind   归属警种
     */
    @GetMapping("delGroup")
    public void delGroup(@RequestParam("idList") String idList,@RequestParam("policeKind") Long policeKind) {
        groupService.delGroup(idList, policeKind);
    }

    /**
     * 根据id查询群体信息
     *
     * @param groupId 群体id
     * @return {@link GroupVO}
     */
    @GetMapping(value = {"/{groupId}", "/public/{groupId}"})
    public GroupVO getById(@PathVariable("groupId") Long groupId) {
        return groupService.getById(groupId);
    }

    /**
     * 根据id查询群体信息
     *
     * @param batchIds 群体ids
     * @return {@link GroupVO}
     */
    @PostMapping("/batch-ids")
    public List<GroupVO> getByIds(@RequestBody List<Long> batchIds) {
        return groupService.getByBatchIds(batchIds);
    }

    /**
     * 名称模糊查询
     *
     * @param groupName 群体名称
     * @return 模糊查询结果
     */
    @GetMapping("/query/fuzzy")
    public List<Long> fuzzyQueryByGroupName(@RequestParam("groupName") String groupName) {
        return groupService.fuzzyQueryByGroupName(groupName);
    }

    /**
     * 获取群体在控，失控等人数统计
     *
     * @param groupId 群体id
     * @return {@link GroupStatisticVO}
     */
    @GetMapping("/{groupId}/statistics")
    public GroupStatisticVO getGroupStatistic(@PathVariable("groupId") Long groupId) {
        return groupService.getGroupStatistic(groupId);
    }

    /**
     * 根据群体id，获取在控人信息
     *
     * @param groupId 群体id
     * @return {@link PersonVO}
     */
    @GetMapping(value = { "/{groupId}/in-control/person", "/public/{groupId}/in-control/person"})
    public List<PersonVO> getGroupInControlPerson(@PathVariable("groupId") Long groupId) {
        return groupService.getGroupInControlPerson(groupId);
    }

    /**
     * 查询群体人员总数
     *
     * @param groupIds 群体id
     * @return 数量
     */
    @PostMapping("/public/count-all")
    public Integer countGroupMembers(@RequestBody List<Long> groupIds) {
        return groupService.countGroupMembers(groupIds);
    }

    /**
     * 查询群体人员活跃程度
     *
     * @param personId 人员id
     * @param groupId  群体id
     * @return 活跃程度
     */
    @GetMapping("/activity-level")
    public String getActivityLevel(@RequestParam("personId") Long personId, @RequestParam("groupId") Long groupId) {
        return groupService.getActivityLevel(personId, groupId);
    }

    /**
     * 根据人员id查询群体信息
     *
     * @param personId 群体
     * @return {@link PersonVO}
     */
    @GetMapping(value = {"/person-id", "/public/person-id"})
    public List<GroupVO> getByPersonId(@RequestParam Long personId) {
        return groupService.getByPersonId(personId);
    }


    /**
     * 批量分类
     *
     * @param params 标签筛选条件等参数
     */
    @PostMapping("/batchClassification")
    public void batchClassification(@RequestBody GroupClassificationParams params) {
        groupService.batchClassification(params);
    }

    /**
     * 列表导出
     *
     * @param response HttpServletResponse
     * @param moduleId 动态列表id
     * @param params   参数
     */
    @PostMapping("/list/export/{moduleId}")
    @SkipResponseBodyAdvice
    public void groupExport(HttpServletResponse response, @PathVariable("moduleId") Long moduleId,
                            @RequestBody ExportParams params) throws IOException {
        groupExportService.groupExport(response, params, moduleId);
    }

    /**
     * 列表导出V2
     *
     * @param response HttpServletResponse
     * @param params   参数
     */
    @PostMapping("/list/exportV2")
    @SkipResponseBodyAdvice
    public void groupExportV2(HttpServletResponse response, @RequestBody ExportParams params) throws IOException {
        groupExportService.groupExportV2(response, params);
    }

    /**
     * 下载群里导入模版
     *
     * @param response 模版
     */
    @GetMapping("/download/template")
    @SkipResponseBodyAdvice
    void downloadTemplate(HttpServletResponse response) throws IOException {
        importService.downloadGroupTemplate(response);
    }

    /**
     * 批量导入
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    @PostMapping("/import")
    public ImportResultVO importGroup(ImportVO vo) {
        return importService.importGroup(vo);
    }

    /**
     * 下载群体档案
     *
     * @param response HttpServletResponse
     * @param groupId  群体档案id
     */
    @GetMapping("/download/groupRecord/{groupId}")
    @SkipResponseBodyAdvice
    void downloadPersonRecord(HttpServletResponse response, @PathVariable("groupId") Long groupId) {
        groupExportService.downloadGroupRecord(response, groupId);
    }

    /**
     * 下载群体档案
     *
     * @param response HttpServletResponse
     * @param policeKind policeKind
     * @param groupId  群体档案id
     */
    @GetMapping("/download/groupRecordV2/{groupId}")
    @SkipResponseBodyAdvice
    void downloadGroupRecordV2(HttpServletResponse response, @PathVariable("groupId") Long groupId,Integer policeKind) throws Exception {
        groupExportService.downloadGroup(response, groupId,policeKind);
    }

    /**
     * 下载fk群体档案
     *
     * @param response HttpServletResponse
     * @param groupId  群体档案id
     */
    @GetMapping("/download/groupRecordFk/{groupId}")
    @SkipResponseBodyAdvice
    void downloadPersonRecordFk(HttpServletResponse response, @PathVariable("groupId") Long groupId) throws Exception {
        fkGroupService.downloadGroup(response, groupId);
    }

    /**
     * 下载群体导入模版V2
     *
     * @param response 模版
     * @param policeKind policeKind
     */
    @GetMapping("/download/templateV2")
    @SkipResponseBodyAdvice
    void downloadTemplateV2(HttpServletResponse response,Integer policeKind) throws Exception {
        importService.downloadGroupTemplateV2(response,policeKind);
    }

    /**
     * 批量导入V2
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    @PostMapping("/importV2")
    public ImportResultVO importGroupV2(ImportVO vo) {
        return importService.importGroupV2(vo);
    }

    /**
     * 根据群体id获取所有相关人员信息
     *
     * @param groupId     群体id
     * @param personLevel 人员等级
     * @param policeKind 归属警种
     * @return 相关人员
     */
    @GetMapping("/relatedPersonInfo")
    List<RelatedPersonVO> getRelatedPersonByGroupId(@RequestParam("groupId") Long groupId, @RequestParam(value = "personLevel", required = false) String personLevel,
                                                    @RequestParam(value = "policeKind", required = false) Integer policeKind) throws IOException {
        return groupService.getRelatedPersonByGroupId(groupId, personLevel,policeKind);
    }

    /**
     * 根据群体id获取所有相关人员信息-分页
     *
     * @param dto dto
     * @return {@link PageResult}<{@link RelatedPersonVO}>
     */
    @GetMapping("/page/relatedPersonInfo")
    PageResult<RelatedPersonVO> getRelatedPerson(GroupPersonDTO dto) {
        return groupService.getRelatedPerson(dto);
    }

    /**
     * 根据群体id获取所有相关人员信息
     *
     * @param groupEventDTO 入参
     * @return 相关事件
     */
    @GetMapping("/relatedEvent")
    public PageResult<EventVO> getRelatedEventByGroupId(GroupEventDTO groupEventDTO) {
        return groupService.getRelatedEventByGroupId(groupEventDTO);
    }

    /**
     * 归档
     *
     * @param groupId 群体id
     */
    @PostMapping("/finishArchive")
    public void finishArchive(Long groupId) {
        groupService.finishArchive(groupId);
    }

    /**
     * 名称精确查询
     *
     * @param groupName 群体名称
     * @return 结果
     */
    @GetMapping("/public/query/byName")
    public Long queryByGroupName(@RequestParam("groupName") String groupName) {
        return groupService.queryByGroupName(groupName);
    }

    /**
     * 根据群体id查询所有相关人员id
     *
     * @param groupId 群体ID
     * @return 结果
     */
    @GetMapping("/public/query/byGroupId")
    public List<Long> queryByGroupId(@RequestParam("groupId") Long groupId){
        return groupService.queryByGroupId(groupId);
    }

    /**
     * 获取警种类型人员架构图分类美剧
     *
     * @param policeKind 警种类型，经侦：3；治安：4；    其他：99
     * @return 字典
     */
    @GetMapping("/groupDetail/relatedPersonType")
    public RestfulResultsV2<DictDto> relatedPersonType(@RequestParam(value = "policeKind",required = false) Integer policeKind){
        return RestfulResultsV2.ok(groupService.relatedPersonType(policeKind));
    }

    /**
     * 获取群体详情
     *
     * @param id ID
     * @param policeKind 警种
     * @return 群体详情
     */
    @GetMapping("/getGroupCard")
    public GroupCardVO getGroupCard(Long id, @Nullable Long policeKind) {
        return groupDetailService.getGroupCard(id, policeKind);
    }

    /**
     * 新增群体-人员关联关系
     *
     * @param userId 登录用户
     * @param deptId 登录用户部门
     * @param groupId 群体ID
     * @param personIds 人员ID集合
     */
    @GetMapping("/public/addPersonGroupRelation")
    public void addPersonGroupRelation(@RequestParam("userId") Long userId,
                                       @RequestParam("deptId") Long deptId,
                                       @RequestParam("groupId") Long groupId,
                                       @RequestParam("personIds") List<Long> personIds) {
        groupService.addPersonGroupRelation(userId, deptId, groupId, personIds);
    }

    /**
     * 获取群体关联预警人员
     *
     * @param id 群体id
     * @return 结果
     */
    @GetMapping("/relatedGtWarningPerson")
    public List<RelatedGtWarningPersonVO> getRelatedGtWarningPerson(@RequestParam("id") Long id) {
        return fkGroupService.getRelatedGtWarningPerson(id);
    }

    /**
     * fk列表导出
     *
     * @param response HttpServletResponse
     * @param params   参数
     */
    @PostMapping("/list/exportFk")
    @SkipResponseBodyAdvice
    public void exportFk(HttpServletResponse response, @RequestBody ExportParams params) throws IOException {
        fkGroupService.fkGroupExport(response, params);
    }

    /**
     * 获取群体关联杆体
     *
     * @param id 群体id
     * @return 结果
     */
    @GetMapping("/relatedGt")
    public List<RelatedGtVO> getRelatedGt(@RequestParam("id") Long id) {
        return fkGroupService.getRelatedGt(id);
    }

    /**
     * 保存群体关联杆体
     *
     * @param dto 保存参数
     */
    @PostMapping("/saveRelatedGt")
    public void saveRelatedGt(@RequestBody SaveGtDTO dto) {
        fkGroupService.saveRelatedGt(dto);
    }

    /**
     * 获取群体人员区域分布
     *
     * @param id 群体id
     * @return 结果
     */
    @GetMapping("/relatedPersonAreaStatistics")
    public List<AreaStatisticsVO> getRelatedPersonAreaStatistics(@RequestParam("id") Long id) {
        return groupService.getRelatedPersonAreaStatistics(id);
    }

    /**
     * 保存群体关联警情
     *
     * @param dto 保存参数
     */
    @PostMapping("/saveRelatedJq")
    public void saveRelatedJq(@RequestBody GroupJqDTO dto) {
        fkGroupService.saveRelatedJq(dto);
    }

    /**
     * 获取群体关联警情
     *
     * @param id 群体id
     * @param pageNum 页码
     * @param pageSize 分页大小
     * @return 结果
     */
    @GetMapping("/relatedJq")
    public RestfulResultsV2<JqCommonVO> relatedJq(Long id, Integer pageNum, Integer pageSize) {
        return jqService.findByGroupId(id, pageNum, pageSize);
    }
}
