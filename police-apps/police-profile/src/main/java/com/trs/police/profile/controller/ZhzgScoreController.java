package com.trs.police.profile.controller;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.profile.domain.dto.zhzg.*;
import com.trs.police.profile.domain.vo.zhzg.ZhzgScoreResultVO;
import com.trs.police.profile.service.zhzg.ZhzgScoreCalculateService;
import com.trs.police.profile.service.zhzg.ZhzgScoreRuleService;
import com.trs.police.profile.service.zhzg.ZhzgScoreService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 智慧政工积分相关controller
 */
@Slf4j
@RestController
@RequestMapping("/zhzg/score")
@Api(tags = "智慧政工积分管理")
public class ZhzgScoreController {

    @Resource
    private ZhzgScoreCalculateService scoreCalculateService;

    @Resource
    private ZhzgScoreRuleService scoreRuleService;

    @Resource
    private ZhzgScoreService zhzgScoreService;

    /**
     * 创建积分规则
     *
     * @param request 创建积分规则请求
     */
    @PostMapping("/rule")
    public void createScoreRule(@RequestBody CreateZhzgScoreRuleRequestDTO request) {
        zhzgScoreService.createScoreRule(request);
    }

    /**
     * 删除积分规则
     *
     * @param id 积分规则id
     */
    @DeleteMapping("/rule/{id}")
    public void deleteScoreRule(@PathVariable("id") Long id) {
        zhzgScoreService.deleteScoreRule(id);
    }

    /**
     * 获取非叶子节点的积分规则树
     *
     * @return 规则树
     */
    @GetMapping("/rule/tree/non-leaf")
    public List<GetZhzgScoreRuleTreeNonLeafDTO> getNonLeafScoreTree() {
        return zhzgScoreService.getNonLeafScoreTree();
    }

    /**
     * 获取叶子节点的积分规则树
     *
     * @param parentId 父节点id
     * @param pageParams 分页参数
     * @param applicableRank 适用职级
     * @param name 规则名称
     * @param desc 规则描述
     * @return 规则树
     */
    @GetMapping("/rule/tree/leaf/{parentId}")
    public RestfulResultsV2<GetZhzgScoreRuleTreeLeafDTO> getLeafScoreTree(
            @PathVariable("parentId") Long parentId,
            PageParams pageParams,
            Integer applicableRank,
            String name,
            String desc) {
        return zhzgScoreService.getLeafScoreTree(parentId, pageParams, applicableRank, name, desc);
    }

    /**
     * 启用/禁用积分规则
     *
     * @param id      规则id
     * @param enabled 是否启用
     */
    @PutMapping("/rule/enabled/{id}")
    public void changeScoreRuleEnabledStatus(@PathVariable("id") Long id, @RequestParam("enabled") Boolean enabled) {
        zhzgScoreService.changeScoreRuleEnabledStatus(id, enabled);
    }

    /**
     * 计算积分
     *
     * @param request 积分计算请求
     * @return 积分计算结果
     */
    @PostMapping("/calculate")
    @ApiOperation("计算积分")
    public RestfulResultsV2<ZhzgScoreResultVO> calculateScore(@Valid @RequestBody ZhzgScoreCalculateRequestDTO request) {
        log.info("收到积分计算请求，人员：{}", request.getPersonArchive().getName());

        try {
            ZhzgScoreResultVO result = scoreCalculateService.calculateScore(request);

            if (result.getSuccess()) {
                log.info("积分计算成功，人员：{}，总分：{}", result.getPersonName(), result.getTotalScore());
                return RestfulResultsV2.ok(result);
            } else {
                log.warn("积分计算失败，人员：{}，错误：{}", result.getPersonName(), result.getErrorMessage());
                return RestfulResultsV2.error(result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("积分计算异常，人员：{}，错误：{}", request.getPersonArchive().getName(), e.getMessage(), e);
            return RestfulResultsV2.error("积分计算异常：" + e.getMessage());
        }
    }

    /**
     * 获取积分规则树
     *
     * @return 规则树
     */
    @GetMapping("/rules/tree")
    @ApiOperation("获取积分规则树")
    public RestfulResultsV2<ZhzgScoreRuleDTO> getRuleTree() {
        try {
            List<ZhzgScoreRuleDTO> ruleTree = scoreRuleService.getRuleTree();
            return RestfulResultsV2.ok(ruleTree);
        } catch (Exception e) {
            log.error("获取积分规则树失败，错误：{}", e.getMessage(), e);
            return RestfulResultsV2.error("获取积分规则树失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有启用的积分规则
     *
     * @return 规则列表
     */
    @GetMapping("/rules")
    @ApiOperation("获取所有启用的积分规则")
    public RestfulResultsV2<ZhzgScoreRuleDTO> getAllEnabledRules() {
        try {
            List<ZhzgScoreRuleDTO> rules = scoreRuleService.getAllEnabledRules();
            return RestfulResultsV2.ok(rules);
        } catch (Exception e) {
            log.error("获取积分规则失败，错误：{}", e.getMessage(), e);
            return RestfulResultsV2.error("获取积分规则失败：" + e.getMessage());
        }
    }

}
