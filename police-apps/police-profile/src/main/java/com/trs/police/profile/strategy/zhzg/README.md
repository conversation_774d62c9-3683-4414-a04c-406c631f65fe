# 智慧政工积分计算系统

## 概述

智慧政工积分计算系统是一个基于策略模式的灵活积分计算框架，支持树形规则结构和动态策略扩展。

## 核心特性

1. **策略模式设计**：每个积分规则对应一个计算策略，易于扩展和维护
2. **树形规则结构**：支持父子节点关系，父节点可设置积分上限
3. **动态策略匹配**：根据规则ID、名称或类型自动匹配对应的计算策略
4. **详细计算记录**：记录每个规则的计算过程和结果
5. **异常处理机制**：完善的错误处理和日志记录

## 系统架构

### 核心组件

1. **ZhzgScoreStrategy**：积分计算策略接口
2. **ZhzgScoreStrategyFactory**：策略工厂，负责策略注册和获取
3. **ZhzgScoreCalculateService**：积分计算核心服务
4. **ZhzgScoreController**：REST API控制器

### 数据模型

1. **ZhzgScoreRuleDTO**：积分规则数据传输对象
2. **ZhzgPersonArchiveDTO**：人员档案数据传输对象
3. **ZhzgScoreResultVO**：积分计算结果视图对象
4. **ZhzgRuleScoreDetailVO**：规则积分详情视图对象

## 使用指南

### 1. 创建新的积分策略

实现 `ZhzgScoreStrategy` 接口：

```java
@Component
public class CustomScoreStrategy implements ZhzgScoreStrategy {
    
    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        // 实现具体的积分计算逻辑
        return ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .score(calculatedScore)
                .isHit(true)
                .success(true)
                .build();
    }
    
    @Override
    public String getStrategyName() {
        return "自定义策略";
    }
    
    @Override
    public String getSupportedRuleType() {
        return "CUSTOM";
    }
}
```

### 2. 调用积分计算API

```http
POST /zhzg/score/calculate
Content-Type: application/json

{
    "personArchive": {
        "id": 1,
        "name": "张三",
        "joinJobDate": "2020-01-01",
        "educations": ["本科学历"],
        "awards": ["三等功1次"],
        "violations": []
    },
    "rules": [
        {
            "id": 1,
            "name": "工作年限",
            "parentId": null,
            "score": 30,
            "isLeaf": true,
            "ruleType": "WORK_YEARS",
            "enabled": true
        }
    ],
    "includeDetails": true
}
```

### 3. 规则配置说明

#### 叶子节点规则
- `isLeaf: true`：表示这是一个叶子节点，需要执行具体的计算策略
- `score`：规则的最大得分，实际计算结果不会超过此值
- `ruleType`：规则类型，用于匹配对应的计算策略

#### 父节点规则
- `isLeaf: false`：表示这是一个父节点，不执行具体计算
- `score`：积分上限，所有子节点的积分总和不会超过此值
- `children`：子规则列表

## 已实现的积分策略

### 1. 工作年限策略 (WorkYearsScoreStrategy)
- **规则类型**：WORK_YEARS
- **计算逻辑**：工作年限每满1个月计0.1分
- **数据依赖**：人员档案中的参加工作日期

### 2. 教育经历策略 (EducationScoreStrategy)
- **规则类型**：EDUCATION
- **计算逻辑**：根据最高学历计分（博士20分，硕士15分，本科10分，专科5分）
- **数据依赖**：人员档案中的教育经历列表

### 3. 立功受奖策略 (AwardsScoreStrategy)
- **规则类型**：AWARDS
- **计算逻辑**：每个立功受奖记录计5分
- **数据依赖**：人员档案中的立功受奖记录

### 4. 违纪违规扣分策略 (ViolationDeductStrategy)
- **规则类型**：VIOLATION
- **计算逻辑**：每个违纪违规记录扣10分
- **数据依赖**：人员档案中的违纪违规记录

## 扩展指南

### 添加新的积分策略

1. 创建策略实现类，实现 `ZhzgScoreStrategy` 接口
2. 使用 `@Component` 注解，让Spring自动注册策略
3. 在策略中实现具体的计算逻辑
4. 定义策略名称和支持的规则类型

### 自定义积分等级

在 `ZhzgScoreCalculateService.calculateScoreLevel()` 方法中修改积分等级的判断逻辑。

### 添加新的数据字段

1. 在 `ZhzgPersonArchiveDTO` 中添加新的字段
2. 在相应的积分策略中使用新字段进行计算

## 注意事项

1. **策略注册**：所有策略类必须使用 `@Component` 注解，确保被Spring容器管理
2. **规则匹配**：策略工厂按照规则ID > 规则名称 > 规则类型的优先级进行匹配
3. **积分上限**：父节点的积分上限会限制子节点的积分总和
4. **异常处理**：策略计算失败时会返回0分，并记录错误信息
5. **数据验证**：在策略中要做好数据验证，避免空指针异常

## 测试

运行测试用例：
```bash
mvn test -Dtest=ZhzgScoreCalculateServiceTest
```

测试用例覆盖了正常计算、空规则、空档案等场景。
