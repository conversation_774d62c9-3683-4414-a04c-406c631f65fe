package com.trs.police.profile.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.PreConditionCheck;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.ExportExcelVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.profile.JqCommonVO;
import com.trs.police.common.openfeign.starter.vo.GroupListVO;
import com.trs.police.profile.domain.dto.GroupJqDTO;
import com.trs.police.profile.domain.dto.SaveGtDTO;
import com.trs.police.profile.domain.entity.Group;
import com.trs.police.profile.domain.vo.*;
import com.trs.police.profile.mapper.GroupJqRelationMapper;
import com.trs.police.profile.mapper.GroupMapper;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.mgr.ProfileExportMgr;
import com.trs.police.profile.service.FkGroupService;
import com.trs.police.profile.service.GroupService;
import com.trs.police.profile.service.JqService;
import com.trs.police.profile.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群体service实现类
 *
 * <AUTHOR>
 * @date 2022/08/31
 */
@Service
@Slf4j
public class FkGroupServiceImpl implements FkGroupService {

    @Resource
    private GroupMapper groupMapper;
    @Resource
    private PersonMapper personMapper;
    @Resource
    private GroupJqRelationMapper groupJqRelationMapper;

    @Resource
    private ProfileExportMgr exportMgr;
    @Resource
    private JqService jqService;

    @Override
    public List<RelatedGtWarningPersonVO> getRelatedGtWarningPerson(Long groupId) {
        PreConditionCheck.checkNotNull(groupId, "群体id不能为空");
        List<RelatedGtWarningPersonVO> relatedGtWarningPersons = groupMapper.getRelatedGtWarningPerson(groupId);
        if (CollectionUtils.isEmpty(relatedGtWarningPersons)) {
            return relatedGtWarningPersons;
        }
        List<Long> personIds = relatedGtWarningPersons.stream().map(RelatedGtWarningPersonVO::getId).collect(Collectors.toList());
        List<RelatedWarningPersonGtVO> relatedWarningPersonGts = personMapper.getRelatedWarningPersonGt(personIds);
        Map<Long, List<RelatedWarningPersonGtVO>> relatedWarningPersonGtMap = relatedWarningPersonGts.stream().collect(Collectors.groupingBy(RelatedWarningPersonGtVO::getId));
        relatedGtWarningPersons.forEach(p->{
            List<RelatedWarningPersonGtVO> myRelatedWarningPersonGts = relatedWarningPersonGtMap.get(p.getId());
            if (!CollectionUtils.isEmpty(myRelatedWarningPersonGts)) {
                p.setWarningGts(myRelatedWarningPersonGts.stream()
                        .filter(gt -> StringUtils.isNotBlank(gt.getDeviceCode()))
                        .distinct()
                        .map(RelatedWarningPersonGtVO::getDeviceCode).collect(Collectors.toList()));
                p.setWarningGtName(myRelatedWarningPersonGts.stream()
                        .filter(gt -> StringUtils.isNotBlank(gt.getName()))
                        .distinct()
                        .map(RelatedWarningPersonGtVO::getName).collect(Collectors.joining(",")));
                p.setLastWarningTime(myRelatedWarningPersonGts.stream().map(RelatedWarningPersonGtVO::getWarningTime).max(Comparator.naturalOrder()).get());
                p.setWarningCount(myRelatedWarningPersonGts.size());
            }
        });
        return relatedGtWarningPersons;
    }

    @Override
    public void fkGroupExport(HttpServletResponse response, ExportParams params) throws IOException {
        ListParamsRequest paramsRequest = params.getListParamsRequest();
        paramsRequest.nullCheck();
        paramsRequest.getPageParams().setPageNumber(1);
        paramsRequest.getPageParams().setPageSize(Integer.MAX_VALUE);
        if(Boolean.FALSE.equals(params.getIsAll()) || !CollectionUtils.isEmpty(params.getIds())){
            paramsRequest.getFilterParams().add(new KeyValueTypeVO("ids", params.getIds()));
        }
        List<GroupListVO> list = BeanUtil.getBean(GroupServiceImpl.class).groupList(params.getListParamsRequest()).getItems();
        List<FkGroupExportVO> exportVoList = new ArrayList<>();
        for (GroupListVO groupListVO : list) {
            FkGroupExportVO exportVO = new FkGroupExportVO();
            BeanUtils.copyProperties(groupListVO, exportVO);
            if(!CollectionUtils.isEmpty(groupListVO.getGroupLabel())){
                exportVO.setGroupLabel(groupListVO.getGroupLabel().stream().collect(Collectors.joining(",")));
            }
            if(!CollectionUtils.isEmpty(groupListVO.getControlBureauName())){
                exportVO.setControlBureauName(groupListVO.getControlBureauName().stream().collect(Collectors.joining(",")));
            }
            exportVoList.add(exportVO);
        }

        ExcelUtil.exportList(response, params.getFieldNames(), exportVoList, FkGroupExportVO.class, "群体列表");
    }

    @Override
    public List<RelatedGtVO> getRelatedGt(Long groupId) {
        PreConditionCheck.checkNotNull(groupId, "群体id不能为空");
        return groupMapper.getRelatedGt(groupId);
    }

    @Override
    public void saveRelatedGt(SaveGtDTO dto) {
        PreConditionCheck.checkNotNull(dto.getId(), "群体id不能为空");
        String relatedGt = dto.getUniqueKeys()==null?null: JSONObject.toJSONString(dto.getUniqueKeys());
        groupMapper.saveRelatedGt(dto.getId(), relatedGt);
    }

    @Override
    public void downloadGroup(HttpServletResponse response, Long groupId) throws Exception {
        Group group = groupMapper.selectByPoliceKind(groupId, null);
        if (group == null) {
            throw new TRSException("获取群体档案详情出错！");
        }
        ExportExcelVO vo = new ExportExcelVO();
        vo.setTemplateFilePath("/template/downloadGroupTemplateFk.xlsx");
        vo.setResponse(response);
        vo.setMergeRowIndex(3);
        Map<String, Object> data = groupRecordByPoliceKind(group, 12L, vo);
        vo.setData(data);
        vo.setExcelName("群体档案-" + group.getName() + ".xlsx");
        ExcelUtil.downloadRecordExcel(vo);
    }

    /**
     * 单个警种群体档案数据
     *
     * @param group 群体
     * @param policeKind 管控警种
     * @param vo vo
     *
     * @return sheet名称，sheet数据
     */
    public Map<String, Object> groupRecordByPoliceKind(Group group, Long policeKind, ExportExcelVO vo) {
        AuthHelper.getCurrentUser();
        try {
            GroupExportServiceImpl groupExportService = BeanUtil.getBean(GroupExportServiceImpl.class);
            ProfileGroupVO profileGroupVO = buildProfileGroupVO(group, policeKind);
            List<PersonGroupExportVO> personGroupExportList = groupExportService.getGroupPersonList(group, null);
            List<ProfileGroupJqExportVO> profileGroupJqExportVos = buildJqList(group);

            Map<String, Object> data = new HashMap<>();
            data.put("profileGroupVO", profileGroupVO);
            data.put("gPerson", personGroupExportList);
            data.put("gJq", profileGroupJqExportVos);
            return data;
        } catch (Exception e) {
            log.error("下载人员档案失败:[{}]！", e.getMessage(), e);
            throw new TRSException("下载人员档案失败，请重试！");
        }
    }

    /**
     * 构建ProfileGroupVO
     *
     * @param group      群体
     * @param policeKind policeKind
     * @return ProfileGroupVO
     */
    public ProfileGroupVO buildProfileGroupVO(Group group, Long policeKind) {
        ListParamsRequest listParamsRequest = new ListParamsRequest();
        listParamsRequest.setPageParams(new PageParams(1, 1));
        listParamsRequest.getFilterParams().add(new KeyValueTypeVO("ids", Arrays.asList(group.getId())));
        listParamsRequest.getFilterParams().add(new KeyValueTypeVO("policeKind", 12));
        GroupService groupService = BeanUtil.getBean(GroupService.class);
        GroupListVO groupListVO = groupService.groupList(listParamsRequest).getItems().get(0);
        GroupExportServiceImpl groupExportService = BeanUtil.getBean(GroupExportServiceImpl.class);
        ProfileGroupVO profileGroupVO = groupExportService.buildProfileGroupVO(group, policeKind);

        profileGroupVO.setBusinessCategory(groupListVO.getBusinessCategory());
        profileGroupVO.setLeaderName(groupListVO.getLeaderName());
        profileGroupVO.setLeaderGenderName(groupListVO.getLeaderGenderName());
        profileGroupVO.setLeaderIdNumber(groupListVO.getLeaderIdNumber());
        profileGroupVO.setLeaderRegisteredResidenceName(groupListVO.getLeaderRegisteredResidence());
        profileGroupVO.setLeaderWorkAddressName(groupListVO.getLeaderWorkAddressName());
        profileGroupVO.setLeaderBasicInvestigationAddressName(groupListVO.getLeaderBasicInvestigationAddressName());
        profileGroupVO.setLeaderIsSlrylbName(groupListVO.getLeaderIsSlrylbName());
        profileGroupVO.setLeaderTel(CollectionUtils.isEmpty(groupListVO.getLeaderTel()) ? "" : groupListVO.getLeaderTel().stream().collect(Collectors.joining(",")));
        profileGroupVO.setLeaderInflowTime(groupListVO.getLeaderInflowTime());

        return profileGroupVO;
    }

    private List<ProfileGroupJqExportVO> buildJqList(Group group) throws Exception {
        if (group == null) {
            return null;
        }
        List<JqCommonVO> datas = jqService.findByGroupId(group.getId(), 1, Integer.MAX_VALUE).getDatas();
        if (!CollectionUtils.isEmpty(datas)) {
            List<ProfileGroupJqExportVO> list = new ArrayList<>();
            for (int i = 0; i < datas.size(); i++) {
                JqCommonVO jq = datas.get(i);
                ProfileGroupJqExportVO vo = new ProfileGroupJqExportVO();
                vo.setCellHead("相关警情");
                vo.setNum(i + 1);
                vo.setBjnr(jq.getContent());
                vo.setJjdwmc(jq.getJjdwmc());
                vo.setJjsj(jq.getJjsj());
                list.add(vo);
            }
            return list;
        }

        return exportMgr.buildExportEmptyList(ProfileGroupJqExportVO.class, "相关警情");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRelatedJq(GroupJqDTO dto) {
        PreConditionCheck.checkNotNull(dto.getId(), "群体id不能为空");
        PreConditionCheck.checkNotEmpty(dto.getJjdbhs(), "接警单编号不能为空");
        for (String jjdbh : dto.getJjdbhs()) {
            groupJqRelationMapper.saveGroupJq(dto.getId(), jjdbh);
        }
    }
}