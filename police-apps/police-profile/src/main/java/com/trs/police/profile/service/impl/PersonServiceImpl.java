package com.trs.police.profile.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trs.police.common.core.constant.enums.IdentifierTypeEnum;
import com.trs.police.common.core.constant.enums.ListSourceTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorPersonStatusEnum;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.ProfilePersonPoliceControlEntity;
import com.trs.police.common.core.entity.User;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.mapper.CommonMapper;
import com.trs.police.common.core.mapper.GlobalUserMapper;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.*;
import com.trs.police.common.core.vo.*;
import com.trs.police.common.core.vo.control.MonitorListDto;
import com.trs.police.common.core.vo.control.RegularMonitorListVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.profile.ListSourceVO;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.core.vo.profile.PersonVirtualIdentityVO;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.vo.PersonListVO;
import com.trs.police.common.openfeign.starter.vo.UpdatePersonRequest;
import com.trs.police.profile.constant.CommonConstants;
import com.trs.police.profile.constant.ExportListInfo;
import com.trs.police.profile.domain.dto.ProfilePersonDto;
import com.trs.police.profile.domain.entity.*;
import com.trs.police.profile.domain.vo.*;
import com.trs.police.profile.mapper.*;
import com.trs.police.profile.mapper.yq.*;
import com.trs.police.profile.mgr.ProfileExportMgr;
import com.trs.police.profile.schema.mapper.SchemaMapper;
import com.trs.police.profile.schema.service.SchemaService;
import com.trs.police.profile.schema.service.approval.ApprovalServiceFactory;
import com.trs.police.profile.service.PersonArchiveService;
import com.trs.police.profile.service.PersonService;
import com.trs.police.profile.service.ProfilePersonService;
import com.trs.police.profile.util.EnvUtil;
import com.trs.police.profile.util.ExcelUtil;
import com.trs.police.profile.util.PersonVoHelper;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trs.police.common.core.constant.enums.IdentifierTypeEnum.CAR_NUMBER;
import static com.trs.police.common.core.constant.enums.IdentifierTypeEnum.PHONE_NUMBER;

/**
 * <AUTHOR> yanghy
 * @date : 2022/10/9 18:03
 */
@Service
@Slf4j
public class PersonServiceImpl extends ServiceImpl<PersonMapper, Person> implements PersonService {

    @Resource
    private PersonMapper personMapper;
    @Resource
    private ProfilePersonPoliceControlMapper profilePersonPoliceControlMapper;
    @Resource
    private ProfileVirtualIdentityMapper profileVirtualIdentityMapper;
    @Resource
    private ProfileVehicleMapper profileVehicleMapper;
    @Resource
    private SchemaService schemaService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private SchemaMapper schemaMapper;
    @Resource
    private LabelMapper labelMapper;

    @Value("${profile.person.openBackFill:false}")
    private String openBackFill;

    @Resource
    private ProfileExportMgr exportMgr;

    @Resource
    private ProfilePersonGovControlMapper profilePersonGovControlMapper;

    @Resource
    private PersonArchiveService personArchiveService;

    @Resource
    private PersonGroupRelationMapper personGroupRelationMapper;

    @Resource
    private PersonEventRelationMapper personEventRelationMapper;

    @Resource
    private PersonClueRelationMapper personClueRelationMapper;

    @Resource
    private ProfileFamilyRelationMapper profileFamilyRelationMapper;

    @Resource
    private ProfileSocialRelationMapper profileSocialRelationMapper;

    @Resource
    private GlobalUserMapper globalUserMapper;

    @Resource
    private YqControlEntityMapper yqControlEntityMapper;

    @Resource
    private YqPersonEntityMapper yqPersonEntityMapper;

    @Resource
    private YqPersonLabelRelationEntityMapper yqPersonLabelRelationEntityMapper;

    @Resource
    private YqVirtualIdentityEntityMapper yqVirtualIdentityEntityMapper;

    @Resource
    private YqLabelEntityMapper yqLabelEntityMapper;

    @Resource
    private YqVehicleEntityMapper yqVehicleEntityMapper;

    @Resource
    private OssService ossService;

    @Resource
    private CommonMapper commonMapper;

    @Autowired
    private ApprovalServiceFactory approvalServiceFactory;

    @Autowired
    private PersonVoHelper personVoHelper;

    @Override
    public List<PersonVO> getPersonListFuzzy(String certificateNumber, String certificateType) {
        return personMapper.getPersonListFuzzy(certificateNumber, certificateType);
    }

    @Override
    public PersonVO getPersonByIdNumber(String idNumber) {
        return personMapper.getPersonByIdNumber(idNumber);
    }

    @Override
    public PersonVO getPersonById(Long id) {
        return this.getById(id);
    }


    @Override
    public PersonCardVO getCardById(Long id) {
        PersonCardVO cardById = personMapper.getCardById(id);
        ProfilePersonPoliceControlEntity policeControl = profilePersonPoliceControlMapper.selectByPersonId(id);
        if (Objects.nonNull(policeControl)) {
            cardById.setDutyPoliceStation(policeControl.getControlStation());
        }
        return cardById;
    }

    @Override
    public PersonCardVO getCardByIdNumber(String idNumber) {
        PersonCardVO person = personMapper.getCardByIdNumber(idNumber);
        if (person != null) {
            person.setMonitorCount((long) personMapper.selectRelationMonitorId(idNumber).size());
            if (!CollectionUtils.isEmpty(person.getPoliceKind())) {
                person.setPoliceKindName(commonMapper.getDictNameList(person.getPoliceKind(), "police_kind"));
            }
            return person;
        } else {
            return new PersonCardVO();
        }
    }

    @Override
    public List<PersonVO> findByIdentifier(String identifierNumber, Integer identifierType) {
        List<String> identifierNumbers = new ArrayList<>();
        //当类型为车辆时
        if (Objects.equals(identifierType, CAR_NUMBER.getCode())) {
            List<Long> vehicleIds = getVehicleId(identifierNumber);
            if (vehicleIds == null) {
                return new ArrayList<>();
            }
            for (Long vehicleId : vehicleIds) {
                identifierNumbers.add(com.trs.common.utils.StringUtils.toStringValue(vehicleId));
            }
        } else if (Objects.equals(identifierType, PHONE_NUMBER.getCode())) {
            identifierNumbers.add(identifierNumber);
        } else if (IdentifierTypeEnum.identityTypeToVirtualType(identifierType) != null) {
            //当为虚拟身份时
            List<Long> virtualIds = getVirtualId(identifierNumber, identifierType);
            if (virtualIds == null) {
                return new ArrayList<>();
            }
            for (Long virtualId : virtualIds) {
                identifierNumbers.add(com.trs.common.utils.StringUtils.toStringValue(virtualId));
            }
        }
        String identifierNumbersStr = com.trs.common.utils.StringUtils.toStringValue(JSON.toJSONString(identifierNumbers));
        log.info("findByIdentifier: params {}", identifierNumbersStr);
        return personMapper.findByIdentifier(identifierNumber, identifierNumbersStr, identifierType);
    }

    /**
     * 获取车辆ID
     *
     * @param carNumber 车牌号码
     * @return ID
     */
    private List<Long> getVehicleId(String carNumber) {
        QueryWrapper<ProfileVehicleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("car_number", carNumber);
        List<ProfileVehicleEntity> profileVehicleEntities = profileVehicleMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(profileVehicleEntities)) {
            return null;
        }
        return profileVehicleEntities.stream().map(ProfileVehicleEntity::getId).collect(Collectors.toList());
    }

    /**
     * 获取虚拟信息
     *
     * @param virtualNumber  虚拟编号
     * @param identifierType 类型
     * @return ID
     */
    private List<Long> getVirtualId(String virtualNumber, Integer identifierType) {
        Integer virtualType = IdentifierTypeEnum.identityTypeToVirtualType(identifierType);
        QueryWrapper<ProfileVirtualIdentityEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("virtual_number", virtualNumber);
        queryWrapper.eq("type", virtualType);
        List<ProfileVirtualIdentityEntity> virtualIdentityEntities = profileVirtualIdentityMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(virtualIdentityEntities) ? null : virtualIdentityEntities.stream().map(ProfileVirtualIdentityEntity::getId).collect(Collectors.toList());
    }

    @Override
    public PageResult<PersonVO> getPage(ListParamsRequest request) {
        final Page<PersonVO> page = personMapper.selectPageList(request.getFilterParams(), request.getSearchParams(),
                request.getPageParams().toPage());
        return PageResult.of(page.getRecords(), request.getPageParams().getPageNumber(), page.getTotal(),
                request.getPageParams().getPageSize());
    }

    @Override
    public PageResult<PersonCardVO> getPageByGroup(ListParamsRequest request) {
        Long groupId = null;
        for (KeyValueTypeVO filterParam : request.getFilterParams()) {
            if ("groupId".equals(filterParam.getKey())) {
                groupId = Long.valueOf(filterParam.getValue().toString());
                break;
            }
        }
        if (Objects.isNull(groupId)) {
            throw new TRSException("群体id不能为空！");
        }
        Page<PersonCardVO> page = personMapper.selectGroupPageList(groupId, request.getFilterParams(),
                request.getSearchParams(),
                request.getPageParams().toPage());
        return PageResult.of(page.getRecords(), request.getPageParams().getPageNumber(), page.getTotal(),
                request.getPageParams().getPageSize());
    }

    @Override
    public List<PersonVO> getPersonByIds(List<Long> ids) {
        if (ids.isEmpty()) {
            return List.of();
        }
        return personMapper.getByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public List<Long> updatePersonList(UpdatePersonRequest request) {
        final List<PersonVO> personVOList = request.getPersonList();
        final CurrentUser currentUser = request.getUpdateUser();
        List<Long> personIds = new ArrayList<>();
        personVOList.forEach(personVO -> {
            final ListSourceVO listSourceVO = new ListSourceVO();
            listSourceVO.setType(ListSourceTypeEnum.MONITOR);
            listSourceVO.setRelatedId(request.getMonitorId());
            //虚拟身份
            final List<Long> virtualIdentityIds = getIdentityIds(personVO, listSourceVO);

            //车牌号
            final List<Long> vehicleIds = getVehicleIds(personVO, listSourceVO);

            Person person = personMapper.getPersonByCertificateNumberAndType(personVO.getCertificateNumber(),
                    personVO.getCertificateType());
            if (Objects.isNull(person)) {
                //新增人员
                person = new Person();
                //填入审计字段
                CommonUtils.fillAuditField(person, currentUser, Boolean.FALSE);

                person.setIdType(personVO.getCertificateType());
                person.setIdNumber(personVO.getCertificateNumber());
                person.setName(personVO.getName());
                person.setTel(personVO.getTel());
                person.setPersonLabel(personVO.getTargetType());
                if (!CollectionUtils.isEmpty(personVO.getImgs())) {
                    person.setPhoto(personVO.getImgs());
                }
                personMapper.insert(person);

                //增加公安管控信息
                final ProfilePersonPoliceControlEntity personPoliceControl = new ProfilePersonPoliceControlEntity();
                personPoliceControl.setControlBureau(currentUser.getDept().getCode());
                personPoliceControl.setPersonId(person.getId());
                personPoliceControl.setControlPerson(List.of(currentUser.getId()));
                personPoliceControl.setControlStation(currentUser.getDept().getCode());
                personPoliceControl.setControlPolice(currentUser.getDuty());
                profilePersonPoliceControlMapper.insert(personPoliceControl);
            } else {
                List<Long> personLabel = ListUtil.mergeListAndRemoveDuplicates(personVO.getTargetType(),
                        person.getPersonLabel());
                person.setPersonLabel(personLabel);
                List<String> tel = ListUtil.mergeListAndRemoveDuplicates(personVO.getTel(), person.getTel());
                if (!CollectionUtils.isEmpty(personVO.getImgs())) {
                    person.setPhoto(personVO.getImgs());
                }
                person.setTel(tel);
            }

            //填入更新审计字段
            CommonUtils.fillAuditField(person, currentUser, Boolean.TRUE);
            person.setMonitorStatus(MonitorPersonStatusEnum.IN_CONTROL.getCode());
            person.setVirtualIdentityIds(virtualIdentityIds);
            person.setVehicleIds(vehicleIds);
            personMapper.updateById(person);
            personIds.add(person.getId());
        });
        return personIds;
    }

    private List<Long> getIdentityIds(PersonVO personVO, ListSourceVO listSourceVO) {
        return personVO.getVirtualIdentity().stream().map(virtual -> {
            List<ProfileVirtualIdentityEntity> virtualIdentityList = profileVirtualIdentityMapper.getByVirtualNumberAndType(
                    virtual.getVirtualNumber(), virtual.getType());
            ProfileVirtualIdentityEntity virtualIdentity;
            if (virtualIdentityList.isEmpty()) {
                virtualIdentity = new ProfileVirtualIdentityEntity();
                virtualIdentity.setVirtualNumber(virtual.getVirtualNumber());
                virtualIdentity.setType(virtual.getType());
                virtualIdentity.setSource(listSourceVO);
                profileVirtualIdentityMapper.insert(virtualIdentity);
            } else {
                virtualIdentity = virtualIdentityList.get(0);
            }
            return virtualIdentity.getId();
        }).collect(Collectors.toList());
    }

    private List<Long> getVehicleIds(PersonVO personVO, ListSourceVO listSourceVO) {
        return personVO.getCarNumber().stream().map(carNumber -> {
            ProfileVehicleEntity vehicle = profileVehicleMapper.getByCarNumber(carNumber, personVO.getName());
            if (Objects.isNull(vehicle)) {
                vehicle = new ProfileVehicleEntity();
                vehicle.setOwner(personVO.getName());
                vehicle.setCarNumber(carNumber);
                vehicle.setSource(listSourceVO);
                profileVehicleMapper.insert(vehicle);
            }
            return vehicle.getId();
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Long updatePersonInfo(PersonVO personVO) {
        Person person = personMapper.getPersonByCertificateNumberAndType(personVO.getCertificateNumber(),
                personVO.getCertificateType());
        person = Objects.isNull(person) ? new Person() : person;
        person.setPersonLabel(personVO.getTargetType());
        person.setTel(personVO.getTel());
        person.setName(personVO.getName());
        Long id = person.getId();
        if ((Objects.nonNull(id) && id > 0L)) {
            final List<Long> identityIds = getIdentityIds(personVO, null);
            final List<Long> vehicleIds = getVehicleIds(personVO, null);
            person.setVehicleIds(vehicleIds);
            person.setVirtualIdentityIds(identityIds);
            personMapper.updateById(person);
        } else {
            if (Objects.isNull(personVO.getCurrentUser())) {
                log.error("补录人员[{}]失败：获取登录用户失败！", personVO.getCertificateNumber());
                return null;
            }
            person.setRiskLevel("关注");
            person.setIdType(1);
            person.setIdNumber(personVO.getCertificateNumber());
            person.setCreateTime(LocalDateTime.now());
            person.setCreateDeptId(personVO.getCurrentUser().getDept().getId());
            person.setCreateUserId(personVO.getCurrentUser().getId());
            person.setUpdateTime(LocalDateTime.now());
            person.setUpdateDeptId(personVO.getCurrentUser().getDept().getId());
            person.setUpdateUserId(personVO.getCurrentUser().getId());
            personMapper.insert(person);
            id = person.getId();
        }
        return id;
    }

    @Override
    public Integer setPersonsMonitorStatus(List<Long> personIds, Integer monitorStatus) {
        if (personIds.isEmpty()) {
            return 0;
        }
        return personMapper.setPersonMonitorStatus(personIds, monitorStatus);
    }

    @Override
    public Integer setPersonsControlStatus(List<Long> personIds, Integer controlStatus) {
        if (personIds.isEmpty()) {
            return 0;
        }
        return personMapper.setPersonControlStatus(personIds, controlStatus);
    }

    @Override
    public Integer setPersonsMonitorStatusByIdNumbers(List<String> idNumbers, Integer monitorStatus) {
        if (idNumbers.isEmpty()) {
            return 0;
        }
        return personMapper.setPersonMonitorStatusByIdNumbers(idNumbers, monitorStatus);
    }

    @Override
    public PersonVO getById(Long personId) {
        PersonVO personVO = personMapper.getPerson(personId);
        if (!CollectionUtils.isEmpty(personVO.getVirtualIdentityIds())) {
            List<PersonVirtualIdentityVO> virtualIdentitys = commonMapper.getVirtualIdentityV2(personVO.getVirtualIdentityIds());
            personVO.setVirtualIdentity(virtualIdentitys);
        } else {
            personVO.setVirtualIdentity(new ArrayList<>());
        }
        ProfilePersonPoliceControlEntity policeControl = profilePersonPoliceControlMapper.selectByPersonId(personId);
        if (Objects.nonNull(policeControl)) {
            personVO.setDutyPoliceStation(policeControl.getControlStation());
        }
        Person person = personMapper.selectById(personId);
        personVO.setXb(person.getGender());
        personVO.setControlLevel(person.getControlLevel());
        personVO.setPersonLabel(person.getPersonLabel());
        personVO.setRegisteredResidence(person.getRegisteredResidence());
        return personVO;
    }

    @Override
    public List<Long> getPersonIds(ListParamsRequest paramsRequest) {
        return personMapper.selectPersonIds(paramsRequest.getFilterParams(), paramsRequest.getSearchParams());
    }

    @Override
    public List<Long> selectGroupIdsByIdNumber(String idNumber) {
        return personMapper.selectGroupIdsByIdNumber(idNumber);
    }

    @Override
    public void setPersonUnControl(Long personId) {
        personMapper.setPersonUnControl(personId);
    }

    @Override
    public Long getPersonControlDeptIdByIdentifier(String identifierNumber, Integer identifierType) {
        return personMapper.getPersonControlDeptIdByIdentifier(identifierNumber, identifierType);
    }

    @Override
    public CheckResult checkTel(String tel) {
        List<Person> personList = personMapper.getPersonByTel(tel);
        if (personList.isEmpty()) {
            return CheckResult.notExist();
        }
        List<String> collect = personList.stream().map(Person::getName).collect(Collectors.toList());
        String names = StringUtils.join(collect, ",");
        return CheckResult.exist(String.format("该手机号码已与%s关联", names));
    }

    @Override
    public List<PersonVO> getPersonByTel(String tel) {
        return personMapper.getPersonVoByTel(tel);
    }

    @Override
    public void updateTel(Long id, List<String> tel) {
        Person person = personMapper.selectById(id);

        List<String> oldTel = person.getTel();
        if (CollectionUtils.isEmpty(oldTel)) {
            oldTel = new ArrayList<>();
        }
        if (Objects.nonNull(tel)) {
            oldTel.addAll(tel);
        }
        List<String> newTel = oldTel.stream().distinct().collect(Collectors.toList());
        person.setTel(newTel);

        personMapper.updateById(person);
    }

    @Override
    public void checkIdNumberExists(String idNumber) {
        PersonVO personByIdNumber = personMapper.getPersonByIdNumber(idNumber);
        if (Objects.nonNull(personByIdNumber)) {
            throw new TRSException("该身份证号已存在!");
        }
    }

    @Override
    public CodeNameVO getActivityLevel(Long personId, Long groupId) {
        return personMapper.getActivityLevel(personId, groupId);
    }

    @Override
    public void personExport(HttpServletResponse response, ExportParams params, Long moduleId) {
        try {
            final String fileName = URLEncoder.encode("人员列表导出", StandardCharsets.UTF_8);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            List<Long> ids;
            if (Boolean.FALSE.equals(params.getIsAll())) {
                if (params.getIds().isEmpty()) {
                    return;
                }
                ids = params.getIds();
                //查全部
            } else {
                ProfilePersonService profilePersonService = BeanFactoryHolder.getBean(ProfilePersonService.class).get();
                ids = profilePersonService.getIdsV1(params.getListParamsRequest(), false);
            }
            AtomicInteger index = new AtomicInteger(1);
            List<PersonExportVO> collect = ids.parallelStream().map(id -> {
                PersonExportVO personExport = personMapper.getPersonExport(id);
                List<User> users = StringUtils.isEmpty(personExport.getGkzrr()) ? new ArrayList<>()
                        : globalUserMapper.selectList(new QueryWrapper<User>().in("id", Arrays
                        .asList(personExport.getGkzrr().split(","))));
                personExport.setGkzrr(CollectionUtils.isEmpty(users) ? "" : users.stream()
                        .filter(e -> StringUtils.isNotEmpty(e.getRealName()))
                        .map(User::getRealName).collect(Collectors.joining(",")));
                personExport.setGkzrrdh(CollectionUtils.isEmpty(users) ? "" : users.stream()
                        .filter(e -> StringUtils.isNotEmpty(e.getMobile()) || StringUtils.isNotEmpty(e.getTelephone()))
                        .map(e -> StringUtils.isNotEmpty(e.getMobile()) ? e.getMobile() : e.getTelephone())
                        .collect(Collectors.joining(",")));
                if (StringUtils.isNotBlank(personExport.getPersonLabels()) && !JsonUtil.parseArray(personExport.getPersonLabels(), Long.class).isEmpty()) {
                    List<Long> labelIds = JsonUtil.parseArray(personExport.getPersonLabels(), Long.class);
                    personExport.setFxxl(labelMapper.getFxxl("person", labelIds));
                    personExport.setFxlb(labelMapper.getFxlb("person", labelIds));
                }
                log.info("人员为：{}", personExport.getName());
                return personExport;
            }).collect(Collectors.toList());
            collect.forEach(e -> e.setXh(index.getAndIncrement()));
            try {
                InputStream inputStream = new ClassPathResource("/template/personListExport.xlsx").getInputStream();
                ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(inputStream).build();
                writeTotal(excelWriter, collect, response);
                writeDate(excelWriter, collect, response);
                excelWriter.finish();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            /*EasyExcelFactory.write(response.getOutputStream(), PersonExportVO.class).sheet(fileName)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .doWrite(collect);*/
        } catch (Exception e) {
            throw new RuntimeException("批量导出人员失败", e);
        }

    }

    /**
     * xx
     *
     * @param excelWriter xx
     * @param collect     xx
     * @param response    xx
     */
    private void writeDate(ExcelWriter excelWriter, List<PersonExportVO> collect, HttpServletResponse response) {
        try {
            WriteSheet writeSheet = EasyExcel.writerSheet("重点人员").build();
            excelWriter.fill(collect, writeSheet);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * xx
     *
     * @param excelWriter xx
     * @param collect     xx
     * @param response    xx
     */
    private void writeTotal(ExcelWriter excelWriter, List<PersonExportVO> collect, HttpServletResponse response) {
        try {
            FillData fillData = setFillInfo(collect);
            WriteSheet writeSheet = EasyExcel.writerSheet("首页").build();
            excelWriter.fill(fillData, writeSheet);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private FillData setFillInfo(List<PersonExportVO> collect) {
        FillData fillData = new FillData();
        fillData.setDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        fillData.setCount(Long.valueOf(collect.stream().map(PersonExportVO::getId).distinct().collect(Collectors.toList()).size()));
        //统计管控警钟对应数量
        List<String> controlPolice = collect.stream().filter(e -> StringUtils.isNotEmpty(e.getControlPolice()))
                .map(PersonExportVO::getControlPolice).collect(Collectors.toList());
        Map<String, Long> controlPoliceMap = controlPolice.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        fillData.setControlPoliceInfo(controlPoliceMap.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .map(entry -> entry.getKey() + entry.getValue() + "人")
                .collect(Collectors.joining("、")));
        //统计地区对应数量
        List<String> area = collect.stream().filter(e -> StringUtils.isNotEmpty(e.getControlBureau()))
                .map(PersonExportVO::getControlBureau).collect(Collectors.toList());
        Map<String, Long> areaMap = area.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        fillData.setAreaCountInfo(areaMap.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .map(entry -> entry.getKey() + entry.getValue() + "人")
                .collect(Collectors.joining("、")));
        //统计群体类别前五
        List<String> groupList = Arrays.asList(collect.stream().filter(e -> StringUtils.isNotEmpty(e.getLbmc()))
                .map(PersonExportVO::getLbmc)
                .collect(Collectors.joining(",")).split(","));
        Map<String, Long> groupMap = groupList.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        fillData.setGroupInfo(groupMap.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> entry.getKey())
                .collect(Collectors.joining("、")));
        fillData.setInfo(MessageFormat.format(ExportListInfo.PERSON_INFO
                , fillData.getDate()
                , fillData.getCount().toString()
                , StringUtils.isEmpty(fillData.getControlPoliceInfo()) ? "" : ("其中" + fillData.getControlPoliceInfo())
                , StringUtils.isEmpty(fillData.getAreaCountInfo()) ? "" : ("从辖区看，" + fillData.getAreaCountInfo())
                , StringUtils.isEmpty(fillData.getGroupInfo()) ? "" : ("从人员类型来看，主要有" + fillData.getGroupInfo())));
        return fillData;
    }

    @Override
    public CheckResult openBackFill() {
        boolean isOpenBackFill = openBackFill.equalsIgnoreCase("true");
        String msg = isOpenBackFill ? "开启自动回填档案信息" : "关闭自动回填档案信息";
        return new CheckResult(isOpenBackFill, msg);
    }

    @Override
    public void downloadPersonRecord(HttpServletResponse response, Long personId) {
        Person person = personMapper.selectById(personId);
        if (person == null) {
            throw new TRSException("获取人员档案详情出错！");
        }
        try {
            ProfilePersonVO profilePersonVO = buildProfilePersonVO(person);
            List<ProfileVehicleVO> profileVehicleVOList = buildProfileVehicleVOList(person);
            List<ProfileVirtualIdentityVO> profileVirtualIdentityVOList = buildProfileVirtualIdentityVOList(person);
            List<MonitorListExportVO> personMonitor = getPersonMonitor(person);
            List<RegularMonitorListExportVO> personRegularMonitor = getPersonRegularMonitor(person);
            List<PersonArchiveWarningExportVO> personWarningRelationList = getPersonWarningRelationList(person);
            List<PersonGroupExportVO> personGroupExportList = getPersonGroupList(person);
            List<PersonEventExportVO> personEventExportList = getPersonEventList(person);
            List<PersonClueExportVO> personClueExportList = getPersonClueList(person);
            List<PersonRenwuVO> personRenwuList = getPersonRenwuList(person);
            List<ProfileFamilyRelationExportVO> familyRelationExportList = getFamilyRelationList(person);
            List<ProfileSocialRelationExportVO> socialRelationExportList = getSocialRelationList(person);
            List<TrackPointExportVO> personTrackList = getPersonTrackList(person);
            List<ProfileInspectorExportVO> inspectorExportList = exportMgr.getInspectorList("person", String.valueOf(person.getId()));
            Map<String, Object> data = new HashMap<>();
            data.put("profilePersonVO", profilePersonVO);
            data.put("vehicles", profileVehicleVOList);
            data.put("vIdentities", profileVirtualIdentityVOList);
            data.put("warnRels", personWarningRelationList);
            data.put("pMonitor", personMonitor);
            data.put("rMonitor", personRegularMonitor);
            data.put("pGroup", personGroupExportList);
            data.put("pEvent", personEventExportList);
            data.put("pClue", personClueExportList);
            data.put("pRenwu", personRenwuList);
            data.put("family", familyRelationExportList);
            data.put("social", socialRelationExportList);
            data.put("track", personTrackList);
            data.put("inspector", inspectorExportList);
            ExportExcelVO vo = new ExportExcelVO();
            String templateFilePath = "downloadPersonTemplate.xlsx";
            if (EnvUtil.isDy()) {
                templateFilePath = "downloadPersonTemplateDy.xlsx";
            }
            vo.setTemplateFilePath("/template/" + templateFilePath);
            vo.setExcelName("重点人员档案-" + profilePersonVO.getName() + ".xlsx");
            vo.setResponse(response);
            vo.setData(data);
            ExcelUtil.downloadRecordExcel(vo);
        } catch (Exception e) {
            log.error("下载人员档案失败:[{}]！", e.getMessage(), e);
            throw new TRSException("下载人员档案失败，请重试！");
        }
    }

    @Override
    public PageResult<PersonGroupVO> getProfilePersonList(ProfilePersonDto dto) {
        Page<PersonGroupVO> pager = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<PersonGroupVO> page = personMapper.getProfilePersonList(pager, dto);

        List<PersonGroupVO> result = page.getRecords().stream().map(this::dealSpecailProperty).collect(Collectors.toList());
        return PageResult.of(result, dto.getPageNum(), page.getTotal(), dto.getPageSize());
    }

    private PersonGroupVO dealSpecailProperty(PersonGroupVO person) {
        //处理标签
        person.setPersonLabel(exportMgr.listToString(exportMgr.labelIdArrayToName(person.getPersonLabelIds())));
        //处理责任派出所
        QueryWrapper<ProfilePersonPoliceControlEntity> policeQueryWrapper = new QueryWrapper<>();
        policeQueryWrapper.eq("person_id", person.getId());
        ProfilePersonPoliceControlEntity policeControlEntity = profilePersonPoliceControlMapper.selectOne(policeQueryWrapper);
        if (!Objects.isNull(policeControlEntity)) {
            person.setDutyPoliceStation(exportMgr.deptCodeToDeptName(policeControlEntity.getControlStation()));
            person.setDutyPolice(exportMgr.userIdArrayToUserName(policeControlEntity.getControlPerson()));
        }
        //录入部门
        person.setCreateDept(exportMgr.deptIdToDeptName(person.getCreateDeptId()));

        return person;
    }

    @Override
    public void reApproval(Long id, Long policeKind, Long moduleId) {
        approvalServiceFactory.of(moduleId)
                .ifPresent(s -> s.reApproval(id, policeKind));
    }

    @Override
    public void syncSwLabel(String subjectType) {
        String swPersonLabel = BeanFactoryHolder.getEnv().getProperty("sw.subject.person.label", "550758,550759,550763");
        List<Long> labelIds = Arrays.stream(swPersonLabel.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<PersonListVO> swPersonList = personMapper.selectAllByLabels(labelIds);
        // 基本信息
        personVoHelper.initVo(swPersonList);
        if (CollectionUtils.isEmpty(swPersonList)) {
            log.info("没有找到符合SW标签的人员数据");
            return;
        }

        log.info("根据SW标签查询到{}条人员数据，开始同步到tb_fx_person表", swPersonList.size());
        // 将查询到的人员数据转换为tb_fx_person表的实体对象
        List<com.trs.police.common.core.fx.entity.Person> fxPersonList = swPersonList.stream()
                .map(this::convertToFxPerson)
                .collect(Collectors.toList());
        try {
            personMapper.insertSwPerson(fxPersonList);
            log.info("同步SW人员数据到tb_fx_person表成功，共同步{}条数据", fxPersonList.size());
        } catch (Exception e) {
            log.error("同步SW人员数据到tb_fx_person表失败", e);
            throw new RuntimeException("同步SW人员数据失败", e);
        }
    }

    /**
     * 将t_profile_person表的Person对象转换为tb_fx_person表的Person对象
     *
     * @param profilePerson 档案人员对象
     * @return 反邪人员对象
     */
    private com.trs.police.common.core.fx.entity.Person convertToFxPerson(PersonListVO profilePerson) {
        com.trs.police.common.core.fx.entity.Person fxPerson = new com.trs.police.common.core.fx.entity.Person();
        fxPerson.fillAuditFields(AuthHelper.getCurrentUser());
        fxPerson.setPersonNumber(String.valueOf(profilePerson.getId()));
        fxPerson.setRealName(profilePerson.getName());
        fxPerson.setIdCard(profilePerson.getIdNumber());
        if (!CollectionUtils.isEmpty(profilePerson.getTel())) {
            fxPerson.setPhone(profilePerson.getTel().get(0));
        }
        fxPerson.setAreaCode(profilePerson.getRegisteredResidence());
        fxPerson.setAreaName(profilePerson.getRegisteredResidenceName());
        fxPerson.setControlPoliceStation(profilePerson.getDutyPoliceStation());
        fxPerson.setControlUnit(profilePerson.getControlBureauName());
        fxPerson.setControlAreaCode(profilePerson.getControlBureau());
        fxPerson.setSubjectType("sw");
        fxPerson.setPersonType(profilePerson.getPersonLabelName() != null
                ? JSON.toJSONString(profilePerson.getPersonLabel()) : null);

        return fxPerson;
    }

    /**
     * 构建ProfilePersonVO
     *
     * @param person person
     * @return ProfilePersonVO
     */
    private ProfilePersonVO buildProfilePersonVO(Person person) {
        if (person == null) {
            return null;
        }
        ProfilePersonVO vo = new ProfilePersonVO();
        vo.setId(person.getId());
        vo.setIdNumber(person.getIdNumber());
        vo.setName(person.getName());
        vo.setIdType(exportMgr.dictCodeToName(person.getIdType(), "id_type"));
        vo.setGender(exportMgr.dictCodeToName(person.getGender(), "gender"));
        vo.setFormerName(person.getFormerName());
        vo.setNickName(person.getNickName());
        vo.setNation(exportMgr.dictCodeToName(person.getNation(), "nation"));
        vo.setTel(exportMgr.listToString(person.getTel()));
        vo.setPoliticalStatus(exportMgr.dictCodeToName(person.getPoliticalStatus(), "profile_political_status"));
        vo.setMartialStatus(exportMgr.dictCodeToName(person.getMartialStatus(), "profile_martial_status"));
        vo.setCurrentJob(person.getCurrentJob());
        String currentPositionDict = BeanFactoryHolder.getEnv().getProperty("profile.person.dict.currentLocation", "profile_current_position");
        vo.setCurrentPosition(exportMgr.dictCodeToName(person.getCurrentPosition(), currentPositionDict));
        vo.setPersonLabel(exportMgr.listToString(exportMgr.labelIdArrayToName(person.getPersonLabel())));
        vo.setRegisteredResidence(exportMgr.districtCodeToName(person.getRegisteredResidence()));
        vo.setRegisteredResidenceDetail(person.getRegisteredResidenceDetail());
        vo.setCurrentResidence(exportMgr.districtCodeToName(person.getCurrentResidence()));
        vo.setCurrentResidenceDetail(person.getCurrentResidenceDetail());
        vo.setWorkTarget(exportMgr.dictCodeToName(person.getWorkTarget(), "profile_work_target"));
        vo.setControlLevel(exportMgr.dictCodeToName(person.getControlLevel(), "profile_person_control_level"));
        vo.setMainDemand(person.getMainDemand());
        vo.setWorkMeasures(person.getWorkMeasures());
        vo.setPetitionInfo(person.getPetitionInfo());
        vo.setPunishInfo(person.getPunishInfo());
        LocalDateTime createTime = person.getCreateTime();
        vo.setCreateTime(createTime == null ? "" : TimeUtil.getSimpleTime(createTime));
        LocalDateTime updateTime = person.getUpdateTime();
        vo.setUpdateTime(updateTime == null ? "" : TimeUtil.getSimpleTime(updateTime));

        //处理公安管控信息
        dealPersonControlInfo(person, vo);

        QueryWrapper<ProfilePersonGovControlEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("person_id", person.getId());
        ProfilePersonGovControlEntity govControlEntity = profilePersonGovControlMapper.selectOne(queryWrapper);
        if (!Objects.isNull(govControlEntity)) {
            vo.setControlGovernment(govControlEntity.getControlGovernment());
            vo.setControlGovernmentPerson(govControlEntity.getControlGovernmentPerson());
            vo.setControlGovernmentContact(govControlEntity.getControlGovernmentContact());
            vo.setControlCommunity(govControlEntity.getControlCommunity());
            vo.setControlCommunityPerson(govControlEntity.getControlCommunityPerson());
            vo.setControlCommunityContact(govControlEntity.getControlCommunityContact());
        }
        List<FileInfoVO> fileInfoVOList = person.getPhoto();
        if (!CollectionUtils.isEmpty(fileInfoVOList)) {
            List<String> urls = fileInfoVOList.stream().filter(v -> !StringUtils.isEmpty(v.getUrl())).map(FileInfoVO::getUrl).collect(Collectors.toList());
            WriteCellData<Void> photo = buildExportPhoto(urls);
            vo.setPhoto(photo);
        }

        vo.setPersonLabelIds(person.getPersonLabel());
        vo.setRiskScore(person.getRiskScore());
        vo.setRiskLevel(person.getRiskLevel());
        vo.setCreateDeptId(person.getCreateDeptId());
        vo.setCreateDept(exportMgr.deptIdToDeptName(person.getCreateDeptId()));
        vo.setResolveDifficultyName(exportMgr.dictCodeToName(person.getResolveDifficulty(), "profile_resolve_difficulty"));
        vo.setResolveDifficultyInfo(person.getResolveDifficultyInfo());

        return vo;
    }

    /**
     * 处理公安管控个信息
     *
     * @param person 人档
     * @param vo     vo
     */
    private void dealPersonControlInfo(Person person, ProfilePersonVO vo) {
        QueryWrapper<ProfilePersonPoliceControlEntity> policeQueryWrapper = new QueryWrapper<>();
        policeQueryWrapper.eq("person_id", person.getId());
        List<ProfilePersonPoliceControlEntity> profilePersonPoliceControlEntityList
                = profilePersonPoliceControlMapper.selectList(policeQueryWrapper);
        StringBuilder dutyPoliceStation = new StringBuilder();
        StringBuilder dutyPolice = new StringBuilder();
        StringBuilder controlBureau = new StringBuilder();
        StringBuilder controlBureauLeader = new StringBuilder();
        StringBuilder controlPolice = new StringBuilder();
        StringBuilder controlPoliceLeader = new StringBuilder();
        StringBuilder controlStation = new StringBuilder();
        StringBuilder controlStationLeader = new StringBuilder();
        for (ProfilePersonPoliceControlEntity policeControlEntity : profilePersonPoliceControlEntityList) {
            dutyPoliceStation.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlStation())).append(",");
            dutyPolice.append(exportMgr.userIdArrayToUserName(policeControlEntity.getControlPerson())).append(",");
            controlBureau.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlBureau())).append(",");
            controlBureauLeader.append(exportMgr.userIdToUserName(policeControlEntity.getControlBureauLeader()));
            controlPolice.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlPolice())).append(",");
            controlPoliceLeader.append(exportMgr.userIdToUserName(policeControlEntity.getControlPoliceLeader())).append(",");
            controlStation.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlStation())).append(",");
            controlStationLeader.append(exportMgr.userIdToUserName(policeControlEntity.getControlStationLeader())).append(",");
        }
        vo.setDutyPoliceStation(dutyPoliceStation.toString());
        vo.setDutyPolice(dutyPolice.toString());
        vo.setControlBureau(controlBureau.toString());
        vo.setControlBureauLeader(controlBureauLeader.toString());
        vo.setControlPolice(controlPolice.toString());
        vo.setControlPoliceLeader(controlPoliceLeader.toString());
        vo.setControlStation(controlStation.toString());
        vo.setControlStationLeader(controlStationLeader.toString());
    }

    /**
     * 构建excel图片实体
     *
     * @param urls 图片url集合
     * @return WriteCellData
     */
    private WriteCellData<Void> buildExportPhoto(List<String> urls) {
        if (CollectionUtils.isEmpty(urls)) {
            return null;
        }
        try {
            WriteCellData<Void> writeCellData = new WriteCellData<>();
            List<ImageData> imageDataList = new ArrayList<>();
            for (int i = 0; i < urls.size(); i++) {
                String url = urls.get(i);
                byte[] body;
                String fileName = url.substring(url.lastIndexOf("/") + 1);
                if (url.contains(CommonConstants.PERSON_PHOTO_CONTAIN_PATH)) {
                    body = ossService.getPhoto(fileName);
                } else {
                    ResponseEntity<byte[]> download = ossService.getFile(fileName);
                    body = download.getBody();
                }
                if (body == null || body.length == 0) {
                    continue;
                }
                ImageData imageData = imageCells(body);
                imageDataList.add(imageData);
            }
            writeCellData.setImageDataList(imageDataList);
            return writeCellData;
        } catch (Exception e) {
            log.error("人档导出头像失败:[{}]！", e.getMessage(), e);
        }
        return null;
    }

    /**
     * Excel图片设置
     *
     * @param bytes bytes
     * @return ImageData
     */
    public static ImageData imageCells(byte[] bytes) {

        ImageData imageData = new ImageData();
        imageData.setImage(bytes);
        // 上 右 下 左 需要留空，这个类似于 css 的 margin；这里实测 不能设置太大
        imageData.setTop(50);
        imageData.setRight(5);
        imageData.setBottom(50);
        imageData.setLeft(5);

        // 设置图片的位置。Relative表示相对于当前的单元格index。first是左上点，last是对角线的右下点，这样确定一个图片的位置和大小。
        imageData.setRelativeFirstRowIndex(1);
        imageData.setRelativeFirstColumnIndex(0);
        imageData.setRelativeLastRowIndex(7);
        imageData.setRelativeLastColumnIndex(1);
        return imageData;
    }

    /**
     * 构建ProfileVehicleVOList
     *
     * @param person person
     * @return ProfileVehicleVOList
     */
    private List<ProfileVehicleVO> buildProfileVehicleVOList(Person person) throws Exception {
        List<Long> vehicleIds = person.getVehicleIds();
        String cellHead = "车辆信息";
        if (person == null || CollectionUtils.isEmpty(vehicleIds)) {
            return exportMgr.buildExportEmptyList(ProfileVehicleVO.class, cellHead);
        }
        List<ProfileVehicleEntity> vehicleEntities = profileVehicleMapper.selectBatchIds(vehicleIds);
        if (CollectionUtils.isEmpty(vehicleEntities)) {
            return exportMgr.buildExportEmptyList(ProfileVehicleVO.class, cellHead);
        }
        List<ProfileVehicleVO> profileVehicleVOList = new ArrayList<>();
        for (int i = 0; i < vehicleEntities.size(); i++) {
            ProfileVehicleEntity vehicle = vehicleEntities.get(i);
            ProfileVehicleVO vehicleVO = new ProfileVehicleVO();
            vehicleVO.setNum(i + 1);
            vehicleVO.setCellHead(cellHead);
            vehicleVO.setCarNumber(vehicle.getCarNumber());
            vehicleVO.setOwner(vehicle.getOwner());
            vehicleVO.setType(exportMgr.dictCodeToName(vehicle.getType(), "profile_vehicle_type"));
            vehicleVO.setSource(exportMgr.sourceToName(vehicle.getSource()));
            profileVehicleVOList.add(vehicleVO);
        }
        return profileVehicleVOList;
    }

    /**
     * 构建ProfileVirtualIdentityVOLIST
     *
     * @param person person
     * @return ProfileVirtualIdentityVOLIST
     */
    private List<ProfileVirtualIdentityVO> buildProfileVirtualIdentityVOList(Person person) throws Exception {
        List<Long> virtualIdentityIds = person.getVirtualIdentityIds();
        String cellHead = "虚拟身份";
        if (person == null || CollectionUtils.isEmpty(virtualIdentityIds)) {
            return exportMgr.buildExportEmptyList(ProfileVirtualIdentityVO.class, cellHead);
        }
        List<ProfileVirtualIdentityEntity> virtualIdentityEntities = profileVirtualIdentityMapper.selectBatchIds(virtualIdentityIds);
        if (CollectionUtils.isEmpty(virtualIdentityEntities)) {
            return exportMgr.buildExportEmptyList(ProfileVirtualIdentityVO.class, cellHead);
        }
        List<ProfileVirtualIdentityVO> voList = new ArrayList<>();
        for (int i = 0; i < virtualIdentityEntities.size(); i++) {
            ProfileVirtualIdentityEntity entity = virtualIdentityEntities.get(i);
            ProfileVirtualIdentityVO vo = new ProfileVirtualIdentityVO();
            vo.setNum(i + 1);
            vo.setCellHead(cellHead);
            vo.setVirtualNumber(entity.getVirtualNumber());
            vo.setType(exportMgr.dictCodeToName(entity.getType(), "virtual_identity_type"));
            vo.setSource(exportMgr.sourceToName(entity.getSource()));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取相关布控
     *
     * @param person person
     * @return 相关布控
     * @throws Exception Exception
     */
    private List<MonitorListExportVO> getPersonMonitor(Person person) throws Exception {
        ListParamsRequest request = buildNoPageRequest();
        PageResult<MonitorListDto> pageResult = personArchiveService.getPersonMonitor(person.getId(), request);
        List<MonitorListDto> items = pageResult.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return exportMgr.buildExportEmptyList(MonitorListExportVO.class, "相关布控");
        }
        List<MonitorListExportVO> voList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            MonitorListDto dto = items.get(i);
            MonitorListExportVO vo = new MonitorListExportVO();
            vo.setCellHead("相关布控");
            vo.setNum(i + 1);
            BeanUtils.copyProperties(dto, vo);
            vo.setWarningModelStr(exportMgr.listToString(dto.getWarningModel()));
            vo.setExpirationDateStr(MonitorTimeUtil.calculateDaysAndHours(vo.getDeadLine() == null ? 0 : vo.getDeadLine().intValue()));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 构建不分页的检索参数
     *
     * @return request
     */
    private ListParamsRequest buildNoPageRequest() {
        ListParamsRequest request = new ListParamsRequest();
        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(0);
        pageParams.setPageSize(-1);
        request.setPageParams(pageParams);
        return request;
    }

    /**
     * 构建分页的检索参数
     *
     * @param pageNumber 当前页
     * @param pageSize   分页参数
     * @return request
     */
    private ListParamsRequest buildPageRequest(int pageNumber, int pageSize) {
        ListParamsRequest request = new ListParamsRequest();
        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(pageNumber);
        pageParams.setPageSize(pageSize);
        request.setPageParams(pageParams);
        return request;
    }

    /**
     * 获取常控列表
     *
     * @param person person
     * @return 常控列表
     * @throws Exception Exception
     */
    private List<RegularMonitorListExportVO> getPersonRegularMonitor(Person person) throws Exception {
        ListParamsRequest request = buildNoPageRequest();
        PageResult<RegularMonitorListVO> pageResult = personArchiveService.getPersonRegularMonitor(person.getId(), request);
        List<RegularMonitorListVO> items = pageResult.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return exportMgr.buildExportEmptyList(RegularMonitorListExportVO.class, "相关常控");
        }
        List<RegularMonitorListExportVO> voList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            RegularMonitorListVO dto = items.get(i);
            RegularMonitorListExportVO vo = new RegularMonitorListExportVO();
            vo.setCellHead("相关常控");
            vo.setNum(i + 1);
            vo.setCreateTimeStr(TimeUtil.getSimpleTime(dto.getCreateTime()));
            BeanUtils.copyProperties(dto, vo);
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取相关预警
     *
     * @param person person
     * @return 获取相关预警
     * @throws Exception Exception
     */
    private List<PersonArchiveWarningExportVO> getPersonWarningRelationList(Person person) throws Exception {
        ListParamsRequest request = buildPageRequest(1, 10);
        PageResult<PersonArchiveWarningVO> pageResult = personArchiveService.getPersonWarningRelationList(person.getId(), request.getPageParams());
        List<PersonArchiveWarningVO> items = pageResult.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return exportMgr.buildExportEmptyList(PersonArchiveWarningExportVO.class, "相关预警");
        }
        List<PersonArchiveWarningExportVO> voList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            PersonArchiveWarningVO dto = items.get(i);
            PersonArchiveWarningExportVO vo = new PersonArchiveWarningExportVO();
            vo.setCellHead("相关预警");
            vo.setNum(i + 1);
            BeanUtils.copyProperties(dto, vo);
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取相关群体
     *
     * @param person person
     * @return 相关群体
     */
    private List<PersonGroupExportVO> getPersonGroupList(Person person) throws Exception {
        List<PersonGroupVO> voList = personGroupRelationMapper.getPersonGroupList(person.getId(), null);
        if (CollectionUtils.isEmpty(voList)) {
            return exportMgr.buildExportEmptyList(PersonGroupExportVO.class, "相关群体");
        }
        List<PersonGroupExportVO> list = new ArrayList<>();
        for (int i = 0; i < voList.size(); i++) {
            PersonGroupVO groupVO = voList.get(i);
            PersonGroupExportVO vo = new PersonGroupExportVO();
            vo.setCellHead("相关群体");
            vo.setNum(i + 1);
            vo.setName(groupVO.getName());
            vo.setGroupLabels(exportMgr.labelIdArrayToName(groupVO.getGroupLabel()));
            vo.setActivityLevel(exportMgr.dictCodeToName(groupVO.getActivityLevel(), "profile_activity_level"));
            vo.setCreateDept(exportMgr.deptIdToDeptName(groupVO.getCreateDeptId()));
            list.add(vo);
        }
        return list;
    }

    /**
     * 获取相关事件
     *
     * @param person person
     * @return 相关事件
     */
    private List<PersonEventExportVO> getPersonEventList(Person person) throws Exception {
        List<PersonEventVO> voList = personEventRelationMapper.getPersonEventList(person.getId(), null);
        if (CollectionUtils.isEmpty(voList)) {
            return exportMgr.buildExportEmptyList(PersonEventExportVO.class, "相关事件");
        }
        List<PersonEventExportVO> list = new ArrayList<>();
        for (int i = 0; i < voList.size(); i++) {
            PersonEventVO eventVO = voList.get(i);
            PersonEventExportVO vo = new PersonEventExportVO();
            vo.setCellHead("相关事件");
            vo.setNum(i + 1);
            vo.setName(eventVO.getName());
            LocalDateTime createTime = eventVO.getCreateTime();
            vo.setCreateTime(createTime == null ? "--" : createTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            vo.setCreateDept(exportMgr.deptIdToDeptName(eventVO.getCreateDeptId()));
            vo.setRelatedTime(eventVO.getRelatedTime() == null ? "--" : eventVO.getRelatedTime().format(TimeUtil.DEFAULT_TIME_PATTERN));
            vo.setRelatedEndTime(eventVO.getRelatedEndTime() == null ? "--" : eventVO.getRelatedEndTime().format(TimeUtil.DEFAULT_TIME_PATTERN));
            list.add(vo);
        }
        return list;
    }

    /**
     * 获取相关线索
     *
     * @param person person
     * @return 相关线索
     */
    private List<PersonClueExportVO> getPersonClueList(Person person) throws Exception {
        List<Clue> clueList = personClueRelationMapper.getPersonClueList(person.getId(), null);
        if (CollectionUtils.isEmpty(clueList)) {
            return exportMgr.buildExportEmptyList(PersonClueExportVO.class, "相关线索");
        }
        List<PersonClueExportVO> list = new ArrayList<>();
        for (int i = 0; i < clueList.size(); i++) {
            Clue clue = clueList.get(i);
            PersonClueExportVO vo = new PersonClueExportVO();
            vo.setCellHead("相关线索");
            vo.setNum(i + 1);
            vo.setName(clue.getName());
            vo.setClueLabel(exportMgr.listToString(exportMgr.labelIdArrayToName(clue.getClueLabel())));
            vo.setEmergencyLevel(exportMgr.dictCodeToName(clue.getEmergencyLevel(), "profile_clue_emergency_level"));
            vo.setSource(exportMgr.dictCodeToName(clue.getSource(), "profile_clue_source"));
            vo.setCreateTime(TimeUtil.getSimpleTime(clue.getCreateTime()));
            vo.setDisposalStatus(exportMgr.dictCodeToName(clue.getDisposalStatus(), "profile_clue_disposal_status"));
            list.add(vo);
        }
        return list;
    }

    /**
     * 获取相关任务
     *
     * @param person person
     * @return 相关任务
     */
    private List<PersonRenwuVO> getPersonRenwuList(Person person) throws Exception {
        List<PersonRenwuVO> voList = personMapper.findRenWuBaseInfoByPersonId(person.getIdNumber());
        if (CollectionUtils.isEmpty(voList)) {
            return exportMgr.buildExportEmptyList(PersonRenwuVO.class, "相关任务");
        }
        for (int i = 0; i < voList.size(); i++) {
            PersonRenwuVO vo = voList.get(i);
            vo.setCellHead("相关任务");
            vo.setNum(i + 1);
            vo.setCrTimeStr(vo.getCrTime() == null ? "--" : TimeUtil.getSimpleTime(vo.getCrTime()));
        }
        return voList;
    }


    /**
     * 获取家庭关系
     *
     * @param person person
     * @return 家庭关系
     */
    private List<ProfileFamilyRelationExportVO> getFamilyRelationList(Person person) throws Exception {
        List<Long> ids = person.getFamilyRelationIds();
        if (CollectionUtils.isEmpty(ids)) {
            return exportMgr.buildExportEmptyList(ProfileFamilyRelationExportVO.class, "家庭关系");
        }
        QueryWrapper<ProfileFamilyRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids).orderByDesc("update_time").orderByDesc("id");
        List<ProfileFamilyRelation> familyRelations = profileFamilyRelationMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(familyRelations)) {
            return exportMgr.buildExportEmptyList(ProfileFamilyRelationExportVO.class, "家庭关系");
        }
        List<ProfileFamilyRelationExportVO> list = new ArrayList<>();
        for (int i = 0; i < familyRelations.size(); i++) {
            ProfileFamilyRelation relation = familyRelations.get(i);
            ProfileFamilyRelationExportVO vo = new ProfileFamilyRelationExportVO();
            vo.setCellHead("家庭关系");
            vo.setNum(i + 1);
            BeanUtils.copyProperties(relation, vo);
            list.add(vo);
        }
        return list;
    }

    /**
     * 获取社会关系
     *
     * @param person person
     * @return 社会关系
     */
    private List<ProfileSocialRelationExportVO> getSocialRelationList(Person person) throws Exception {
        List<Long> ids = person.getSocialRelationIds();
        if (CollectionUtils.isEmpty(ids)) {
            return exportMgr.buildExportEmptyList(ProfileSocialRelationExportVO.class, "社会关系");
        }
        QueryWrapper<ProfileSocialRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids).orderByDesc("update_time").orderByDesc("id");
        List<ProfileSocialRelation> relations = profileSocialRelationMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(relations)) {
            return exportMgr.buildExportEmptyList(ProfileSocialRelationExportVO.class, "社会关系");
        }
        List<ProfileSocialRelationExportVO> list = new ArrayList<>();
        for (int i = 0; i < relations.size(); i++) {
            ProfileSocialRelation relation = relations.get(i);
            ProfileSocialRelationExportVO vo = new ProfileSocialRelationExportVO();
            vo.setCellHead("社会关系");
            vo.setNum(i + 1);
            BeanUtils.copyProperties(relation, vo);
            list.add(vo);
        }
        return list;
    }

    /**
     * 获取人员轨迹
     *
     * @param person person
     * @return 常控列表
     * @throws Exception Exception
     */
    private List<TrackPointExportVO> getPersonTrackList(Person person) throws Exception {
        ListParamsRequest request = buildNoPageRequest();
        SortParams sortParams = new SortParams();
        sortParams.setSortField("time");
        sortParams.setSortDirection("");
        request.setSortParams(sortParams);
        KeyValueTypeVO keyValueTypeVO = new KeyValueTypeVO();
        keyValueTypeVO.setKey("activeTime");
        keyValueTypeVO.setValue("1");
        keyValueTypeVO.setType("timeParams");
        List<KeyValueTypeVO> typeVOList = new ArrayList<>();
        typeVOList.add(keyValueTypeVO);
        request.setFilterParams(typeVOList);
        PageResult<TrackPointVO> pageResult = personArchiveService.getPersonTrackList(person.getId(), request);
        List<TrackPointVO> items = pageResult.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return exportMgr.buildExportEmptyList(TrackPointExportVO.class, "人员轨迹");
        }
        List<TrackPointExportVO> voList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            TrackPointVO dto = items.get(i);
            TrackPointExportVO vo = new TrackPointExportVO();
            vo.setCellHead("人员轨迹");
            vo.setNum(i + 1);
            BeanUtils.copyProperties(dto, vo);
            vo.setTypeName(exportMgr.dictCodeToName(dto.getType(), "control_warning_source_type"));
            voList.add(vo);
        }
        return voList;
    }

}
