package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 家庭关系表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_police_family_relation", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class PoliceFamilyRelation extends AbstractBaseEntity {
    private static final long serialVersionUID = 1007482378100508960L;

    /**
     * 关联警员档案表（t_police_profile）的主键'
     */
    private Long profileId;
    /**
     * 关系
     */
    private String relation;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号码
     */
    private String idNumber;
    /**
     * 联系方式
     */
    private String phoneNumber;
    /**
     * 是否删除
     */
    private Boolean deleted;
    /**
     * 工作单位
     */
    private String workUnit;
}

