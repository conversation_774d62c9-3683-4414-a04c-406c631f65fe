package com.trs.police.profile.domain.dto.zhzg;

import com.trs.police.profile.util.zhzg.TreeNode;
import lombok.Data;

import java.util.List;

/**
 * 查询非叶子节点规则树参数
 */

@Data
public class GetZhzgScoreRuleTreeNonLeafDTO implements TreeNode<GetZhzgScoreRuleTreeNonLeafDTO, Long> {

    private Long id;

    private String name;

    private Integer fullScore;

    private Long parentId;

    private List<GetZhzgScoreRuleTreeNonLeafDTO> children;
}
