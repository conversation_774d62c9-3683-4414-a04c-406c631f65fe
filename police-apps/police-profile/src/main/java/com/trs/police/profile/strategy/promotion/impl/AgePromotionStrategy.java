package com.trs.police.profile.strategy.promotion.impl;

import com.trs.police.profile.constant.PromotionStatusEnum;
import com.trs.police.profile.domain.dto.promotion.PromotionRuleDTO;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Map;

/**
 * 年龄限制晋升规则策略
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class AgePromotionStrategy implements PromotionRuleStrategy {

    private static final String STRATEGY_NAME = "年龄限制规则";
    private static final String RULE_TYPE = "AGE_LIMIT";

    @Override
    public PromotionRuleDetailVO evaluateRule(ProfilePolice police, PromotionRuleDTO rule) {
        log.debug("开始评估年龄限制规则，警员：{}，规则：{}", police.getName(), rule.getRuleName());

        PromotionRuleDetailVO.PromotionRuleDetailVOBuilder builder = PromotionRuleDetailVO.builder()
                .ruleName(rule.getRuleName())
                .description(rule.getDescription())
                .ruleType(rule.getRuleType())
                .required(rule.getRequired())
                .success(true);

        try {
            // 验证数据
            if (!validatePolice(police) || !validateRule(rule)) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取出生日期
            if (police.getBirthday() == null) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription("缺少出生日期信息")
                        .build();
            }

            // 计算年龄
            LocalDate birthDate = police.getBirthday().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate currentDate = LocalDate.now();
            Period period = Period.between(birthDate, currentDate);
            int age = period.getYears();

            // 获取规则配置
            Map<String, Object> ruleConfig = rule.getRuleConfig();
            int minAge = getIntFromConfig(ruleConfig, "minAge", 0);
            int maxAge = getIntFromConfig(ruleConfig, "maxAge", 100);
            String resultType = getStringFromConfig(ruleConfig, "resultType", "NOT_QUALIFIED");

            boolean isQualified = age >= minAge && age <= maxAge;
            PromotionStatusEnum result;

            if (isQualified) {
                result = PromotionStatusEnum.QUALIFIED;
            } else {
                // 根据配置决定结果类型
                switch (resultType.toUpperCase()) {
                    case "FORBIDDEN":
                        result = PromotionStatusEnum.FORBIDDEN;
                        break;
                    case "DEFERRED":
                        result = PromotionStatusEnum.DEFERRED;
                        break;
                    case "NOT_QUALIFIED":
                    default:
                        result = PromotionStatusEnum.NOT_QUALIFIED;
                        break;
                }
            }

            String hitData = String.format("当前年龄：%d岁", age);
            String calculateDescription;

            if (isQualified) {
                calculateDescription = String.format("当前年龄%d岁，在允许范围内（%d-%d岁），符合晋升要求",
                        age, minAge, maxAge);
            } else {
                if (age < minAge) {
                    calculateDescription = String.format("当前年龄%d岁，低于最低年龄要求%d岁，%s",
                            age, minAge, result.getName());
                } else {
                    calculateDescription = String.format("当前年龄%d岁，超过最高年龄限制%d岁，%s",
                            age, maxAge, result.getName());
                }
            }

            return builder
                    .isHit(!isQualified)
                    .ruleResult(result)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("评估年龄限制规则失败，警员：{}，规则：{}，错误：{}", 
                    police.getName(), rule.getRuleName(), e.getMessage(), e);
            return builder
                    .isHit(false)
                    .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                    .success(false)
                    .errorMessage("评估失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    /**
     * 从配置中获取整数值
     */
    private int getIntFromConfig(Map<String, Object> config, String key, int defaultValue) {
        if (config == null || !config.containsKey(key)) {
            return defaultValue;
        }
        Object value = config.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("配置项{}的值{}无法转换为整数，使用默认值{}", key, value, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 从配置中获取字符串值
     */
    private String getStringFromConfig(Map<String, Object> config, String key, String defaultValue) {
        if (config == null || !config.containsKey(key)) {
            return defaultValue;
        }
        Object value = config.get(key);
        return value != null ? value.toString() : defaultValue;
    }
}
