package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 立功受奖积分计算策略
 * 规则描述：每个立功受奖记录计5分
 */
@Slf4j
@Component
public class AwardsScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "立功受奖";
    private static final String RULE_TYPE = "AWARDS";
    private static final double SCORE_PER_AWARD = 5.0;

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算立功受奖积分，人员：{}，规则：{}", personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取立功受奖记录
            List<String> awards = personArchive.getAwards();
            if (CollectionUtils.isEmpty(awards)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无立功受奖记录")
                        .build();
            }

            // 计算积分
            int awardCount = awards.size();
            double calculatedScore = awardCount * SCORE_PER_AWARD;

            // 应用规则配置的最大分值限制
            double finalScore = calculatedScore;
            if (rule.getScore() != null && calculatedScore > rule.getScore()) {
                finalScore = rule.getScore();
            }

            String hitData = String.format("立功受奖记录：%d条", awardCount);
            String calculateDescription = String.format("%d条立功受奖记录 × %.1f分/条 = %.1f分", 
                    awardCount, SCORE_PER_AWARD, calculatedScore);
            
            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.1f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算立功受奖积分失败，人员：{}，规则：{}，错误：{}", 
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public boolean supports(Long ruleId, String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
