package com.trs.police.profile.service.zhzg;

import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.entity.zhzg.ZhzgScoreRuleEntity;
import com.trs.police.profile.mapper.zhzg.ZhzgScoreRuleMapper;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 智慧政工积分规则管理服务
 */
@Slf4j
@Service
public class ZhzgScoreRuleService {

    @Resource
    private ZhzgScoreRuleMapper scoreRuleMapper;

    /**
     * 获取所有启用的积分规则
     *
     * @return 规则列表
     */
    public List<ZhzgScoreRuleDTO> getAllEnabledRules() {
        return scoreRuleMapper.selectList(null)
                .stream()
                .map(this::entityToDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据规则类型获取规则
     *
     * @param ruleType 规则类型
     * @return 规则列表
     */
    public List<ZhzgScoreRuleDTO> getRulesByType(String ruleType) {
        List<ZhzgScoreRuleDTO> allRules = getAllEnabledRules();
        return allRules.stream()
                .filter(rule -> ruleType.equals(rule.getRuleType()))
                .collect(Collectors.toList());
    }

    /**
     * 构建规则树
     *
     * @return 规则树
     */
    public List<ZhzgScoreRuleDTO> getRuleTree() {
        List<ZhzgScoreRuleDTO> allRules = getAllEnabledRules();
        return ZhzgScoreUtil.buildRuleTree(allRules);
    }

    /**
     * 验证规则配置
     *
     * @param rules 规则列表
     * @return 验证结果
     */
    public ZhzgScoreUtil.RuleValidationResult validateRules(List<ZhzgScoreRuleDTO> rules) {
        return ZhzgScoreUtil.validateRules(rules);
    }

    /**
     * 保存规则
     *
     * @param rule 规则信息
     * @return 保存后的规则
     */
    public ZhzgScoreRuleDTO saveRule(ZhzgScoreRuleDTO rule) {
        // TODO: 实现规则保存逻辑
        log.info("保存积分规则：{}", rule.getName());
        return rule;
    }

    /**
     * 更新规则
     *
     * @param rule 规则信息
     * @return 更新后的规则
     */
    public ZhzgScoreRuleDTO updateRule(ZhzgScoreRuleDTO rule) {
        // TODO: 实现规则更新逻辑
        log.info("更新积分规则：{}", rule.getName());
        return rule;
    }

    /**
     * 删除规则
     *
     * @param ruleId 规则ID
     */
    public void deleteRule(Long ruleId) {
        // TODO: 实现规则删除逻辑
        log.info("删除积分规则：{}", ruleId);
    }

    /**
     * 启用/禁用规则
     *
     * @param ruleId  规则ID
     * @param enabled 是否启用
     */
    public void toggleRule(Long ruleId, Boolean enabled) {
        // TODO: 实现规则启用/禁用逻辑
        log.info("{}积分规则：{}", enabled ? "启用" : "禁用", ruleId);
    }

    /**
     * 实体转DTO
     *
     * @param entity 实体对象
     * @return DTO对象
     */
    private ZhzgScoreRuleDTO entityToDto(ZhzgScoreRuleEntity entity) {
        if (entity == null) {
            return null;
        }

        ZhzgScoreRuleDTO dto = new ZhzgScoreRuleDTO();
        BeanUtils.copyProperties(entity, dto);
        dto.setEnabled(entity.getIsEnabled());
        dto.setRuleType(inferRuleType(entity.getName(), entity.getDescription()));
        return dto;
    }

    /**
     * DTO转实体
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    private ZhzgScoreRuleEntity dtoToEntity(ZhzgScoreRuleDTO dto) {
        if (dto == null) {
            return null;
        }

        ZhzgScoreRuleEntity entity = new ZhzgScoreRuleEntity();
        BeanUtils.copyProperties(dto, entity);
        entity.setIsEnabled(dto.getEnabled());
        entity.setDescription(dto.getDescription());
        return entity;
    }

    /**
     * 根据规则名称和描述推断规则类型
     *
     * @param name 规则名称
     * @param desc 规则描述
     * @return 规则类型
     */
    private String inferRuleType(String name, String desc) {
        if (name == null) {
            return null;
        }

        String lowerName = name.toLowerCase();
        if (lowerName.contains("工作年限") || lowerName.contains("工龄")) {
            return "WORK_YEARS";
        } else if (lowerName.contains("教育") || lowerName.contains("学历")) {
            return "EDUCATION";
        } else if (lowerName.contains("立功") || lowerName.contains("受奖")) {
            return "AWARDS";
        } else if (lowerName.contains("违纪") || lowerName.contains("违规")) {
            return "VIOLATION";
        } else if (lowerName.contains("专业") || lowerName.contains("技术")) {
            return "EXPERTISE";
        } else if (lowerName.contains("考核")) {
            return "ASSESSMENT";
        }

        return "CUSTOM";
    }

    /**
     * 获取示例规则数据
     *
     * @return 示例规则列表
     */
    private List<ZhzgScoreRuleDTO> getExampleRules() {
        List<ZhzgScoreRuleDTO> rules = new ArrayList<>();

        // 基础素质规则组
        rules.add(createRule(1L, "基础素质", null, 50, false, "BASE_QUALITY", true, "基础素质评价"));
        rules.add(createRule(11L, "工作年限", 1L, 30, true, "WORK_YEARS", true, "工作年限每满1个月计0.1分"));
        rules.add(createRule(12L, "教育经历", 1L, 20, true, "EDUCATION", true, "根据最高学历计分"));

        // 表现评价规则组
        rules.add(createRule(2L, "表现评价", null, 30, false, "PERFORMANCE", true, "工作表现评价"));
        rules.add(createRule(21L, "立功受奖", 2L, 25, true, "AWARDS", true, "每个立功受奖记录计5分"));
        rules.add(createRule(22L, "违纪违规", 2L, -50, true, "VIOLATION", true, "每个违纪违规记录扣10分"));

        return rules;
    }

    /**
     * 创建规则对象
     *
     * @param id          规则ID
     * @param name        规则名称
     * @param parentId    父规则ID
     * @param score       规则分数
     * @param isLeaf      是否是叶子节点
     * @param ruleType    规则类型
     * @param enabled     是否启用
     * @param description 描述
     * @return ZhzgScoreRuleDTO
     */
    private ZhzgScoreRuleDTO createRule(Long id, String name, Long parentId, Integer score,
                                       Boolean isLeaf, String ruleType, Boolean enabled, String description) {
        ZhzgScoreRuleDTO rule = new ZhzgScoreRuleDTO();
        rule.setId(id);
        rule.setName(name);
        rule.setParentId(parentId);
        rule.setScore(score);
        rule.setIsLeaf(isLeaf);
        rule.setRuleType(ruleType);
        rule.setEnabled(enabled);
        rule.setDescription(description);
        return rule;
    }

}
