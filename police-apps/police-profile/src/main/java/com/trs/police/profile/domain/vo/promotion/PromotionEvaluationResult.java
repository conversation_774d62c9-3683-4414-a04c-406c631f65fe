package com.trs.police.profile.domain.vo.promotion;

import com.trs.police.profile.constant.PromotionStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 职级晋升评估结果VO
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionEvaluationResult {

    /**
     * 警员档案ID
     */
    private Long policeId;

    /**
     * 警员姓名
     */
    private String policeName;

    /**
     * 警员身份证号
     */
    private String idNumber;

    /**
     * 当前职级代码
     */
    private Integer currentRankCode;

    /**
     * 当前职级名称
     */
    private String currentRankName;

    /**
     * 目标晋升职级代码
     */
    private Integer targetRankCode;

    /**
     * 目标晋升职级名称
     */
    private String targetRankName;

    /**
     * 晋升状态
     */
    private PromotionStatusEnum promotionStatus;

    /**
     * 晋升状态代码
     */
    private Integer promotionStatusCode;

    /**
     * 晋升状态名称
     */
    private String promotionStatusName;

    /**
     * 评估结果描述
     */
    private String resultDescription;

    /**
     * 规则评估详情列表
     */
    private List<PromotionRuleDetailVO> ruleDetails;

    /**
     * 评估时间
     */
    private LocalDateTime evaluationTime;

    /**
     * 是否成功
     */
    @Builder.Default
    private Boolean success = true;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 建议信息
     */
    private String suggestion;

    /**
     * 扩展信息
     */
    private Object extendInfo;

    /**
     * 获取晋升状态代码
     */
    public Integer getPromotionStatusCode() {
        return promotionStatus != null ? promotionStatus.getCode() : null;
    }

    /**
     * 获取晋升状态名称
     */
    public String getPromotionStatusName() {
        return promotionStatus != null ? promotionStatus.getName() : null;
    }
}
