package com.trs.police.profile.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.DeptTypeEnum;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.profile.JqCommonVO;
import com.trs.police.common.core.vo.profile.JqFkxxCommonVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.profile.converter.JqConverter;
import com.trs.police.profile.domain.dto.JqDTO;
import com.trs.police.profile.domain.entity.Dict;
import com.trs.police.profile.domain.entity.JQ;
import com.trs.police.profile.domain.entity.JqFkxx;
import com.trs.police.profile.domain.vo.JqIndexDetailVO;
import com.trs.police.profile.domain.vo.JqIndexVO;
import com.trs.police.profile.domain.vo.JqListVO;
import com.trs.police.profile.domain.vo.JqVO;
import com.trs.police.profile.mapper.JqFkxxMapper;
import com.trs.police.profile.mapper.NewJqMapper;
import com.trs.police.profile.mapper.OldSthyMapper;
import com.trs.police.profile.mapper.SthyLocationMapperFactory;
import com.trs.police.profile.schema.field.ListMapperFactory;
import com.trs.police.profile.service.JqService;
import com.trs.police.profile.service.ProfileDictService;
import com.trs.police.profile.util.ResultHelper;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.utils.TimeUtils.YYYYMMDD;

/**
 * <AUTHOR>
 * @date 2023/3/29 14:52
 */
@Service
@Slf4j
public class JqServiceImpl implements JqService {

    @Resource
    private NewJqMapper newJqMapper;

    @Resource
    private JqFkxxMapper jqFkxxMapper;

    @Resource
    private OldSthyMapper oldSthyMapper;

    @Value("${spring.datasource.dynamic.datasource.sthy.jj-table-name}")
    private String sthyJjTableName;

    @Value("${spring.datasource.dynamic.datasource.sthy.fk-table-name}")
    private String sthyfkTableName;

    @Value("${spring.datasource.dynamic.datasource.sthy.db-location}")
    private String dbLocation;

    @Value("${spring.datasource.dynamic.datasource.sthy.oldTimeZone:}")
    private String oldTimeZone;

    @Value("${spring.datasource.dynamic.datasource.sthy.currTimeZone:}")
    private String currTimeZone;

    @Resource
    private SthyLocationMapperFactory sthyLocationMapperFactory;

    @Resource
    private JqConverter jqConverter;

    @Resource
    private ProfileDictService dictService;
    @Resource
    private PermissionService permissionService;

    @Override
    public PageResult<JqListVO> getJqPageList(ListParamsRequest request) {
        return getJqPageList(request, null);
    }

    @Override
    public PageResult<JqListVO> getJqPageList(ListParamsRequest request, List<KeyValueTypeVO> operateFilterParams){
        PageParams page = request.getPageParams();
        //是否只能查看当地区域警情
        boolean localAreaPermission = BeanFactoryHolder.getEnv()
                .getProperty("com.trs.jq.localAreaPermission", Boolean.class, false);
        if (localAreaPermission){
            dealRequestParams(request);
        }
        //bjsj是否添加8小时
        boolean timeOffsetEnabled = BeanFactoryHolder.getEnv().getProperty("time.offset.enabled", Boolean.class, false);
        Page<JqListVO> result = newJqMapper.getJqPageList(request, page.toPage(), timeOffsetEnabled, operateFilterParams);
        ListMapperFactory.getMapper(ListMapperFactory.DEPT_CODE_TO_NAME).mappingDisplayValue(result.getRecords(), null, JqListVO::getGxdwdm, JqListVO::setHandleDept);
        ListMapperFactory.getMapper(ListMapperFactory.DISTRICT_TO_NAME).mappingDisplayValue(result.getRecords(), null, JqListVO::getXzqhdm, JqListVO::setDistrictName);
        ListMapperFactory.getMapper(ListMapperFactory.RELATED_RISK_COUNT).mappingDisplayValue(result.getRecords(), null, JqListVO::getJjdbh, JqListVO::setRelatedRisk);
        result.getRecords().forEach(this::initJqListVO);
        return PageResult.of(result.getRecords(), page.getPageNumber(), result.getTotal(), page.getPageSize());
    }

    private void dealRequestParams(ListParamsRequest request) {
        List<KeyValueTypeVO> filterParams = request.getFilterParams();
        if (Objects.isNull(filterParams)){
            return;
        }
        SimpleUserVO simpleUser = AuthHelper.getNotNullSimpleUser();
        KeyValueTypeVO vo = new KeyValueTypeVO();
        vo.setKey("userAreaCode");
        vo.setValue(AreaUtils.areaPrefix(simpleUser.getDistrictCode()));
        filterParams.add(vo);
        List<KeyValueTypeVO> permissionFilters = permissionService.buildParamsByPermission();
        if (!CollectionUtils.isEmpty(permissionFilters)){
            filterParams.addAll(permissionFilters);
        }
    }

    @Override
    public PageResult<JqListVO> getJqPageListWithFkxx(ListParamsRequest request) {
        PageParams page = request.getPageParams();
        //bjsj是否添加8小时
        boolean timeOffsetEnabled = BeanFactoryHolder.getEnv().getProperty("time.offset.enabled", Boolean.class, false);
        Page<JqListVO> result = newJqMapper.getJqPageListWithFxxx(request, page.toPage(), timeOffsetEnabled);
        ListMapperFactory.getMapper(ListMapperFactory.DEPT_CODE_TO_NAME).mappingDisplayValue(result.getRecords(), null, JqListVO::getGxdwdm, JqListVO::setHandleDept);
        ListMapperFactory.getMapper(ListMapperFactory.DISTRICT_TO_NAME).mappingDisplayValue(result.getRecords(), null, JqListVO::getXzqhdm, JqListVO::setDistrictName);
        ListMapperFactory.getMapper(ListMapperFactory.RELATED_RISK_COUNT).mappingDisplayValue(result.getRecords(), null, JqListVO::getJjdbh, JqListVO::setRelatedRisk);
        return PageResult.of(result.getRecords(), page.getPageNumber(), result.getTotal(), page.getPageSize());
    }

    @Override
    public Long getIdByCode(String code) {
        Long id = newJqMapper.getIdByCode(code);
        if (Objects.isNull(id)) {
            throw new RuntimeException(String.format("警情编号:%s,不存在", code));
        }
        return id;
    }

    @Override
    public void insertByCode(String code, String relationType) {
        JQ jq = sthyLocationMapperFactory.getMapper(dbLocation).selectByBh(code, sthyJjTableName);
        if (Objects.isNull(jq)) {
            throw new RuntimeException(String.format("警情编号:%s,不存在", code));
        }
        jq.setSourceType(2);
        jq.setRelationType(relationType);
        dealJqTimeZone(jq);
        buildJqlbmc(Collections.singletonList(jq));
        newJqMapper.insert(jq);

        // 完成记录
        List<JqFkxx> cjwcList = sthyLocationMapperFactory.getMapper(dbLocation).selectFkxx(code, "8", sthyfkTableName);
        buildJqFkxxJqlbmc(cjwcList);
        // 到达记录
        //List<JqFkxx> ddList = sthyLocationMapperFactory.getMapper(dbLocation).selectFkxx(code, "6", sthyfkTableName);
        // 签收记录
        List<JqFkxx> qsList = sthyLocationMapperFactory.getMapper(dbLocation).selectFkxx(code, null, sthyfkTableName);
        buildJqFkxxJqlbmc(qsList);
        Map<String, LocalDateTime> ddMap = qsList.stream()
                .filter((fixes) -> Objects.nonNull(fixes.getFksj()))
                .collect(Collectors.toMap(this::buildJjdbhGroupKey, JqFkxx::getFksj, (v1, v2) -> v1));
        cjwcList.forEach(fkxx -> {
            fkxx.setDdxcsj(ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "6")));
            fkxx.setQssj(ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "5")));
            fkxx.setCzfksj(ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "7")));
            fkxx.setCssj(ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "27")));
            fkxx.setJjsj(ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "0")));
            fkxx.setTssj(ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "1")));
            fkxx.setJasj(ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "11")));
            dealFkTimeZone(fkxx);
            jqFkxxMapper.insertNew(fkxx);
        });
    }

    /**
     * 处理警情的时区问题
     *
     * @param jq 警情
     */
    public void dealJqTimeZone(JQ jq) {
        if (jq == null || StringUtils.isEmpty(oldTimeZone) || StringUtils.isEmpty(currTimeZone)) {
            return;
        }
        jq.setBjsj(buildCurrTimeZoneTime(jq.getBjsj()));
        jq.setJjsj(buildCurrTimeZoneTime(jq.getJjsj()));
        jq.setJjwcsj(buildCurrTimeZoneTime(jq.getJjwcsj()));
    }

    /**
     * 处理反馈的时区问题
     *
     * @param jqFkxx 反馈
     */
    private void dealFkTimeZone(JqFkxx jqFkxx) {
        if (jqFkxx == null || StringUtils.isEmpty(oldTimeZone) || StringUtils.isEmpty(currTimeZone)) {
            return;
        }
        jqFkxx.setFksj(buildCurrTimeZoneTime(jqFkxx.getFksj()));
        jqFkxx.setCjsj01(buildCurrTimeZoneTime(jqFkxx.getCjsj01()));
        jqFkxx.setDdxcsj(buildCurrTimeZoneTime(jqFkxx.getDdxcsj()));
        jqFkxx.setXcclwbsj(buildCurrTimeZoneTime(jqFkxx.getXcclwbsj()));
        jqFkxx.setJqfssj(buildCurrTimeZoneTime(jqFkxx.getJqfssj()));
        jqFkxx.setCjsj(buildCurrTimeZoneTime(jqFkxx.getCjsj()));
        jqFkxx.setGxsj(buildCurrTimeZoneTime(jqFkxx.getGxsj()));
    }

    /**
     * 将指定时区时间转成当前时区时间
     *
     * @param localDateTime oldLocalDateTime
     * @return currLocalDateTime
     */
    private LocalDateTime buildCurrTimeZoneTime(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return localDateTime;
        }
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of(oldTimeZone));
        return zonedDateTime.withZoneSameInstant(ZoneId.of(currTimeZone)).toLocalDateTime();
    }

    @Override
    public void insertOldJqByCode(String code, String relationType) {
        JQ oldJq = oldSthyMapper.selectOldJqByBh(code);
        if (Objects.isNull(oldJq)) {
            throw new RuntimeException(String.format("警情编号:%s,不存在", code));
        }
        oldJq.setSourceType(4);
        oldJq.setRelationType(relationType);
        if ("电话".equals(oldJq.getJqlyfs())) {
            oldJq.setJqlyfs("01");
        } else if ("执勤".equals(oldJq.getJqlyfs())) {
            oldJq.setJqlyfs("03");
        } else {
            oldJq.setJqlyfs("99");
        }
        newJqMapper.insert(oldJq);
        log.info("成功插入旧警情{}", oldJq);
        List<JqFkxx> jqFkxxes = oldSthyMapper.selectFkxx(code);
        jqFkxxes.forEach(fkxx -> {
            jqFkxxMapper.insertNew(fkxx);
        });
    }

    @Override
    public void dealHistoryDdxcsj(String startTime, String endTime) {
        List<JqFkxx> wwList = jqFkxxMapper.selectList(new QueryWrapper<JqFkxx>()
                .ge(StringUtils.isNotEmpty(startTime), "fksj", startTime)
                .le(StringUtils.isNotEmpty(endTime), "fksj", endTime));
        Lists.partition(wwList, 500).forEach(list -> {
            // 到达记录
            String bhs = list.stream().map(JqFkxx::getJjdbh).collect(Collectors.joining(","));
            List<JqFkxx> ddList = sthyLocationMapperFactory.getMapper(dbLocation).selectFkxx(bhs, null, sthyfkTableName);
            buildJqFkxxJqlbmc(ddList);
            Map<String, LocalDateTime> ddMap = ddList.stream()
                    .filter((fixes) -> Objects.nonNull(fixes.getFksj()))
                    .collect(Collectors.toMap(this::buildJjdbhGroupKey, JqFkxx::getFksj, (v1, v2) -> v1));
            list.forEach(fkxx -> {
                try {
                    LocalDateTime localDateTime = ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "6"));
                    if (!StringUtils.isEmpty(oldTimeZone) && !StringUtils.isEmpty(currTimeZone)) {
                        fkxx.setDdxcsj(buildCurrTimeZoneTime(localDateTime));
                    } else {
                        fkxx.setDdxcsj(localDateTime);
                    }
                    //code=5标识签收
                    LocalDateTime qssj = ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "5"));
                    //code=7标识处置反馈
                    LocalDateTime czfksj = ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "7"));
                    //code=27标识抄送时间
                    LocalDateTime cssj = ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "27"));
                    //code=0标识接警时间
                    LocalDateTime jjsj = ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "0"));
                    //code=1标识推送时间
                    LocalDateTime tssj = ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "1"));
                    //code=11标识结案时间
                    LocalDateTime jasj = ddMap.get(buildJjdbhGroupKey(fkxx.getJjdbh(), "11"));
                    if (!StringUtils.isEmpty(oldTimeZone) && !StringUtils.isEmpty(currTimeZone)) {
                        fkxx.setQssj(buildCurrTimeZoneTime(qssj));
                        fkxx.setCzfksj(buildCurrTimeZoneTime(czfksj));
                        fkxx.setCssj(buildCurrTimeZoneTime(cssj));
                        fkxx.setJjsj(buildCurrTimeZoneTime(jjsj));
                        fkxx.setTssj(buildCurrTimeZoneTime(tssj));
                        fkxx.setJasj(buildCurrTimeZoneTime(jasj));
                    } else {
                        fkxx.setQssj(qssj);
                        fkxx.setCzfksj(czfksj);
                        fkxx.setCssj(cssj);
                        fkxx.setJjsj(jjsj);
                        fkxx.setTssj(tssj);
                        fkxx.setJasj(jasj);
                    }
                    jqFkxxMapper.updateById(fkxx);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        });
    }


    /**
     * 构建group key
     *
     * @param jjdbh  警情单编号
     * @param czlbbh 操作类别
     * @return key
     */
    private String buildJjdbhGroupKey(String jjdbh, String czlbbh) {
        return String.format("%s:%s", jjdbh, czlbbh);
    }

    /**
     * 构建group key
     *
     * @param jqFkxx 出警对象
     * @return key
     */
    private String buildJjdbhGroupKey(JqFkxx jqFkxx) {
        return String.format("%s:%s", jqFkxx.getJjdbh(), jqFkxx.getCzlbbh());
    }

    @Override
    public RestfulResultsV2<JqIndexVO> todayIndex(){
        JqDTO dto = new JqDTO();
        String currentDate = TimeUtils.getCurrentDate(YYYYMMDD);
        dto.setStartTime(currentDate);
        dto.setEndTime(currentDate);
        Map<String, Double> indexNamesAndScore = newJqMapper.jqIndex(dto).stream().collect(Collectors.toMap(JqIndexVO::getIndexName, JqIndexVO::getScore));
        String indexNames = BeanFactoryHolder.getEnv().getProperty("profile.zgdp.defaultTodayIndex", "安全指数,治安指数,稳定指数");
        List<JqIndexVO> jqIndexList = Arrays.stream(indexNames.split(",")).map(indexName -> {
            JqIndexVO vo = new JqIndexVO();
            vo.setIndexName(indexName);
            Double score = indexNamesAndScore.get(indexName);
            vo.setScore(score==null?10:score);
            return vo;
        }).collect(Collectors.toList());
        jqIndexList.forEach(vo -> vo.setScore(vo.getScore()<0?0:vo.getScore()));
        return RestfulResultsV2.ok(jqIndexList);
    }

    @Override
    public RestfulResultsV2<JqIndexDetailVO> todayIndexDetail(JqDTO dto){
        String currentDate = TimeUtils.getCurrentDate(YYYYMMDD);
        dto.setStartTime(currentDate);
        dto.setEndTime(currentDate);
        return RestfulResultsV2.ok(newJqMapper.jqIndexDetail(dto));
    }

    /**
     * 报警列表
     *
     * @param dto dto
     * @return 结果
     */
    @Override
    public RestfulResultsV2<JqVO> bjListV2(JqDTO dto) {
        IPage<JQ> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        QueryWrapper<JQ> jqQueryWrapper = buildQuery(dto);
        RestfulResultsV2<JqVO> result = queryForPage(page, jqQueryWrapper);
        buildFkxx(result.getDatas());
        return result;
    }

    /**
     * 构建反馈信息
     *
     * @param jqVos 报警列表
     */
    private void buildFkxx(List<JqVO> jqVos){
        if(!CollectionUtils.isEmpty(jqVos)){
            List<String> jjdbhs = jqVos.stream().map(JqVO::getJjdbh).collect(Collectors.toList());
            Map<String, JqFkxx> jjdbh = jqFkxxMapper.selectList(new QueryWrapper<JqFkxx>()
                    .in("jjdbh", jjdbhs)).stream()
                    .collect(Collectors.toMap(JqFkxx::getJjdbh, v -> v, (v1, v2) -> v1));
            jqVos.forEach(vo -> {
                JqFkxx jqFkxx = jjdbh.get(vo.getJjdbh());
                if (jqFkxx!=null) {
                    vo.setFkyxm(jqFkxx.getFkyxm());
                    vo.setCjczqk(jqFkxx.getCjczqk());
                }
            });
        }
    }

    private RestfulResultsV2<JqVO> queryForPage(IPage<JQ> page, QueryWrapper<JQ> queryWrapper) {
        IPage<JQ> result = newJqMapper.selectPage(page, queryWrapper);
        return ResultHelper.getIPageConverter().convert(jqConverter.toPageVO(result));
    }

    private QueryWrapper<JQ> buildQuery(JqDTO dto) {
        QueryWrapper<JQ> jqWrapper = new QueryWrapper<JQ>()
                .select("id", "jjdbh", "bjdh", "bjnr", "jqlbmc", "bjdz", "bjsj", "bjrmc")
                .ge(com.trs.common.utils.StringUtils.isNotEmpty(dto.getStartTime()), "bjsj", dto.getStartTime())
                .le(com.trs.common.utils.StringUtils.isNotEmpty(dto.getEndTime()), "bjsj", dto.getEndTime())
                .eq(com.trs.common.utils.StringUtils.isNotEmpty(dto.getBjdh()), "bjdh", dto.getBjdh())
                .orderByDesc("bjsj");
        return jqWrapper;
    }

    @Override
    public void dealHistoryJqbq() {
        int pageNum = 1;
        int pageSize = 1000;
        QueryWrapper<JQ> queryWrapper = new QueryWrapper<JQ>().likeLeft("jqbq", "[");
        while (true) {
            IPage<JQ> page = new Page<>(pageNum, pageSize);
            IPage<JQ> result = newJqMapper.selectPage(page, queryWrapper);
            if (result.getRecords().size() == 0) {
                return;
            }
            for (JQ jq : result.getRecords()) {
                if (com.trs.common.utils.StringUtils.isEmpty(jq.getJqbq())) {
                    jq.setJqbq(null);
                }
                List<JSONObject> jsonObjects = JSONObject.parseArray(jq.getJqbq(), JSONObject.class);
                if (jsonObjects != null && jsonObjects.size() > 0) {
                    String jqbq = jsonObjects.stream().map(jsonObject -> jsonObject.getString("name")).collect(Collectors.joining(","));
                    jq.setJqbq(jqbq);
                } else {
                    jq.setJqbq(null);
                }
                newJqMapper.updateById(jq);
            }
            pageNum++;
        }
    }

    /**
     * 构建jqlbmc
     *
     * @param jqs 数据
     */
    public void buildJqFkxxJqlbmc(List<JqFkxx> jqs){
        Boolean updateJqlbmc = BeanFactoryHolder.getEnv().getProperty("profile.downloadTask.updateJqlbmc", Boolean.class, false);
        if(!updateJqlbmc){
            return;
        }
        if(CollectionUtils.isEmpty(jqs)){
            return;
        }
        List<Dict> dicts = dictService.getDictListByTypeWithAll("sthy_jq_label");
        Map<String, String> codeNameMap = dicts.stream().filter(dict -> com.trs.common.utils.StringUtils.isNotEmpty(dict.getDictDesc()))
                .collect(Collectors.toMap(Dict::getDictDesc, Dict::getName, (v1, v2) -> v1));
        jqs.forEach(jq -> {
            if(com.trs.common.utils.StringUtils.isEmpty(jq.getJqlbmc())){
                jq.setJqlbmc(codeNameMap.get(jq.getJqlbdm()));
            }
            if(com.trs.common.utils.StringUtils.isEmpty(jq.getJqlxmc())){
                jq.setJqlxmc(codeNameMap.get(jq.getJqlxdm()));
            }
            if(com.trs.common.utils.StringUtils.isEmpty(jq.getJqxlmc())){
                jq.setJqxlmc(codeNameMap.get(jq.getJqxldm()));
            }
            if(com.trs.common.utils.StringUtils.isEmpty(jq.getJqzlmc())){
                jq.setJqzlmc(codeNameMap.get(jq.getJqzldm()));
            }
        });
    }

    /**
     * 构建jqlbmc
     *
     * @param jqs 数据
     */
    public void buildJqlbmc(List<JQ> jqs){
        Boolean updateJqlbmc = BeanFactoryHolder.getEnv().getProperty("profile.downloadTask.updateJqlbmc", Boolean.class, false);
        if(!updateJqlbmc){
            return;
        }
        if(CollectionUtils.isEmpty(jqs)){
            return;
        }
        List<Dict> dicts = dictService.getDictListByTypeWithAll("sthy_jq_label");
        Map<String, String> codeNameMap = dicts.stream().filter(dict -> com.trs.common.utils.StringUtils.isNotEmpty(dict.getDictDesc()))
                .collect(Collectors.toMap(Dict::getDictDesc, Dict::getName, (v1, v2) -> v1));
        jqs.forEach(jq -> {
            if(com.trs.common.utils.StringUtils.isEmpty(jq.getJqlbmc())){
                jq.setJqlbmc(codeNameMap.get(jq.getJqlbdm()));
            }
            if(com.trs.common.utils.StringUtils.isEmpty(jq.getJqlxmc())){
                jq.setJqlxmc(codeNameMap.get(jq.getJqlxdm()));
            }
            if(com.trs.common.utils.StringUtils.isEmpty(jq.getJqxlmc())){
                jq.setJqxlmc(codeNameMap.get(jq.getJqxldm()));
            }
            if(com.trs.common.utils.StringUtils.isEmpty(jq.getJqzlmc())){
                jq.setJqzlmc(codeNameMap.get(jq.getJqzldm()));
            }
        });
    }

    @Override
    public JqCommonVO findByBh(String bh) {
        return newJqMapper.findByBh(bh);
    }

    @Override
    public List<JqFkxxCommonVO> findFkxxByBh(String jjdbh) {
        if (StringUtils.isEmpty(jjdbh)) {
            return new ArrayList<>();
        }
        QueryWrapper<JqFkxx> jqFkxxQueryWrapper = new QueryWrapper<>();
        jqFkxxQueryWrapper.eq("jjdbh", jjdbh);
        List<JqFkxx> jqFkxxes = jqFkxxMapper.selectList(jqFkxxQueryWrapper);
        if (CollectionUtils.isEmpty(jqFkxxes)) {
            return new ArrayList<>();
        }
        return jqConverter.toJqFkxxCommonVOList(jqFkxxes);
    }

    @Override
    public JqFkxxCommonVO findNewFkxxByBh(String jjdbh) {
        if (StringUtils.isEmpty(jjdbh)) {
            return null;
        }
        QueryWrapper<JqFkxx> jqFkxxQueryWrapper = new QueryWrapper<>();
        jqFkxxQueryWrapper.eq("jjdbh", jjdbh);
        jqFkxxQueryWrapper.orderByDesc("fksj");
        List<JqFkxx> jqFkxxes = jqFkxxMapper.selectPage(new Page<>(0, 1), jqFkxxQueryWrapper).getRecords();
        if (CollectionUtils.isEmpty(jqFkxxes)) {
            return null;
        }
        return jqConverter.toJqFkxxCommonVO(jqFkxxes.get(0));
    }

    @Override
    public List<JqCommonVO> findById(String id) {
        if (StringUtils.isEmpty(id)) {
            return new ArrayList<>();
        }
        List<Long> ids = Stream.of(id.split(",|;"))
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<JqCommonVO> commonVoByIds = newJqMapper.findCommonVoByIds(ids);
        // 根据id 按照序号返回
        Map<Long, JqCommonVO> collect = commonVoByIds.stream().collect(Collectors.toMap(JqCommonVO::getId, j -> j));
        List<JqCommonVO> result = Stream.of(id.split(",|;"))
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .map(collect::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public List<JqCommonVO> findByTelAndIdCard(String idCard, String tel) {
        List<String> cards = StringUtil.isEmpty(idCard)
                ? Arrays.asList("-1")
                : Stream.of(idCard.split(";"))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        List<String> tels = StringUtil.isEmpty(tel)
                ? Arrays.asList("-1")
                : Stream.of(tel.split(";"))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        return newJqMapper.findByTelAndIdCard(cards, tels);
    }

    @Override
    public RestfulResultsV2<JqCommonVO> findByTelAndIdCardPage(String idCard, String tel, Integer pageNum, Integer pageSize) {
        List<String> cards = StringUtil.isEmpty(idCard)
                ? Arrays.asList("-1")
                : Stream.of(idCard.split(";"))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        List<String> tels = StringUtil.isEmpty(tel)
                ? Arrays.asList("-1")
                : Stream.of(tel.split(";"))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        Page<JqCommonVO> byTelAndIdCardPage = newJqMapper.findByTelAndIdCardPage(cards, tels, new Page<>(pageNum, pageSize));
        RestfulResultsV2<JqCommonVO> ok = RestfulResultsV2.ok(byTelAndIdCardPage.getRecords());
        ok.addTotalCount(byTelAndIdCardPage.getTotal());
        return ok;
    }

    private void initJqListVO(JqListVO vo) {
        if ("0".equals(vo.getBjrxbdm())) {
            vo.setBjrxbmc("未知");
        } else if ("1".equals(vo.getBjrxbdm())) {
            vo.setBjrxbmc("男");
        } else if ("2".equals(vo.getBjrxbdm())) {
            vo.setBjrxbmc("女");
        }
        if (StringUtils.isNotEmpty(vo.getJjlyh())){
            String env = BeanFactoryHolder.getEnv().getProperty("fight.forward.url", "");
            String newUrl = env + vo.getJjlyh();
            vo.setJjlyh(newUrl);
        }
        vo.buildShortCategoryCode();
    }

    @Override
    public String stqjdpJqType(){
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        Long type = currentUser.getDept().getType();
        if(type!=null && DeptTypeEnum.POLICE_STATION.getCode().equals(type.intValue())){
            return BeanFactoryHolder.getEnv().getProperty("profile.stqzdp.jqTypes.pcs","");
        }
        AreaUtils.Level level = AreaUtils.level(currentUser.getDept().getDistrictCode());
        if(AreaUtils.Level.PROVINCE.equals(level)){
            return BeanFactoryHolder.getEnv().getProperty("profile.stqzdp.jqTypes.province","010301,010101");
        }else if(AreaUtils.Level.CITY.equals(level)){
            return BeanFactoryHolder.getEnv().getProperty("profile.stqzdp.jqTypes.city","010301,010302,010303,010405,010105,010101,010104");
        }else if(AreaUtils.Level.COUNTY.equals(level)){
            return BeanFactoryHolder.getEnv().getProperty("profile.stqzdp.jqTypes.county","010301,010302,010303,010405,010105,010101,010104,01,02");
        }else {
            return "010301,010302,010303,010405,010105,010101,010104";
        }
    }

    @Override
    public RestfulResultsV2<JqCommonVO> findByGroupId(Long groupId, Integer pageNum, Integer pageSize) {
        Page<JqCommonVO> byTelAndIdCardPage = newJqMapper.findByGroupId(groupId, new Page<>(pageNum, pageSize));
        RestfulResultsV2<JqCommonVO> ok = RestfulResultsV2.ok(byTelAndIdCardPage.getRecords());
        ok.addTotalCount(byTelAndIdCardPage.getTotal());
        return ok;
    }
}
