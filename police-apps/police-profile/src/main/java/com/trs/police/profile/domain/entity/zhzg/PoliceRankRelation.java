package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 警员职级表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_police_rank_relation", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class PoliceRankRelation extends AbstractBaseEntity {

    /**
     * 关联警员档案表（t_police_profile）的主键
     */
    private Long profileId;
    /**
     * 任职开始时间
     */
    private Date startTime;
    /**
     * 任职结束时间
     */
    private Date endTime;
    /**
     * 任职序列，码表，type = police_rz_xl
     */
    private Integer rankSeries;
    /**
     * 职级，码表，type = police_zj
     */
    private Integer rank;
    /**
     * 是否删除
     */
    private Boolean deleted;
}

