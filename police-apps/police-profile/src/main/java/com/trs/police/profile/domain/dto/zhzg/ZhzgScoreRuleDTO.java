package com.trs.police.profile.domain.dto.zhzg;

import com.trs.police.profile.util.zhzg.TreeNode;
import lombok.Data;

import java.util.List;

/**
 * 智慧政工积分规则DTO
 */

@Data
public class ZhzgScoreRuleDTO implements TreeNode<ZhzgScoreRuleDTO, Long> {

    /**
     * 规则id，用来匹配具体积分规则实现
     */
    private Long id;

    /**
     * 规则名称，用来统计命中规则
     */
    private String name;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 规则分数，对于叶子结点是命中得分，对于非叶子结点是满分
     */
    private Integer score;

    /**
     * 是否是叶子结点
     */
    private Boolean isLeaf;

    /**
     * 子规则列表（用于构建树形结构）
     */
    private List<ZhzgScoreRuleDTO> children;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 规则类型（用于匹配策略）
     */
    private String ruleType;

}
