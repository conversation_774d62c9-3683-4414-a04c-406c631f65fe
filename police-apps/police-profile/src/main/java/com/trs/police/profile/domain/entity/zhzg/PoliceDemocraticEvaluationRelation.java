package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 民主测评表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_police_democratic_evaluation_relation")
public class PoliceDemocraticEvaluationRelation extends AbstractBaseEntity {
    /**
     * 测评年份（对应界面“测评年份”）
     */
    private int evaluationYear;
    /**
     * 关联警员档案表（t_police_profile）的主键
     */
    private Long profileId;
    /**
     * A票排名（对应界面“A票 - 排名”）
     */
    private String aTicketRank;
    /**
     * A票优秀票数（对应界面“A票 - 优秀的票（前一个框）”）
     */
    private Integer aTicketExcellentCount;
    /**
     * A票总票数（对应界面“A票 - 优秀的票（后一个框）”）
     */
    private Integer aTicketTotalCount;
    /**
     * B票排名（对应界面“B票 - 排名”）
     */
    private String bTicketRank;
    /**
     * B票优秀票数（对应界面“B票 - 优秀的票（前一个框）”）
     */
    private Integer bTicketExcellentCount;
    /**
     * B票总票数（对应界面“B票 - 优秀的票（后一个框）”）
     */
    private Integer bTicketTotalCount;
    /**
     *
     */
    private int deleted;
}

