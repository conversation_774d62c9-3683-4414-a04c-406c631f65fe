package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.profile.JqCommonVO;
import com.trs.police.profile.domain.dto.JqDTO;
import com.trs.police.profile.domain.entity.JQ;
import com.trs.police.profile.domain.entity.JqDsrxx;
import com.trs.police.profile.domain.vo.JqIndexDetailVO;
import com.trs.police.profile.domain.vo.JqIndexVO;
import com.trs.police.profile.domain.vo.JqListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 警情档案
 *
 * <AUTHOR>
 */
@Mapper
public interface NewJqMapper extends BaseMapper<JQ> {

    /**
     * 根据警情编号查询警情
     *
     * @param bh 警情编号
     * @return 警情
     */
    JqCommonVO findByBh(@Param("bh") String bh);

    /**
     * 根据警情编号查询警情
     *
     * @param ids ids
     * @return 警情
     */
    List<JqCommonVO> findCommonVoByIds(@Param("ids") List<Long> ids);

    /**
     * 根据身份证号码和电话查询
     *
     * @param idCard 身份证号码
     * @param tel 电话
     * @return 警情列表
     */
    List<JqCommonVO> findByTelAndIdCard(@Param("idCard") List<String> idCard, @Param("tel") List<String> tel);

    /**
     * 根据身份证号码和电话查询
     *
     * @param idCard 身份证号码
     * @param tel 电话
     * @param page 分页参数
     *
     * @return 警情列表
     */
    Page<JqCommonVO> findByTelAndIdCardPage(@Param("idCard") List<String> idCard, @Param("tel") List<String> tel, Page page);

    /**
     * 根据群体id查询
     *
     * @param groupId 群体id
     * @param page 分页参数
     *
     * @return 警情列表
     */
    Page<JqCommonVO> findByGroupId(@Param("groupId") Long groupId, Page page);

    /**
     * 新增
     *
     * @param jq 实体对象
     * @return 影响行数
     */
    @Override
    int insert(@Param("jq") JQ jq);

    /**
     * 获取警情列表
     *
     * @param params            参数
     * @param toPage            分页参数
     * @param timeOffsetEnabled 报警时间是否需要+8小时时区
     * @param operateFilterParams 操作过滤条件
     * @return 警情数据
     */
    Page<JqListVO> getJqPageList(@Param("params") ListParamsRequest params,
                                 Page<Object> toPage,
                                 @Param("timeOffsetEnabled") boolean timeOffsetEnabled,
                                 @Param("operateFilterParams") List<KeyValueTypeVO> operateFilterParams);

    /**
     * 获取警情列表
     *
     * @param params            参数
     * @param toPage            分页参数
     * @param timeOffsetEnabled 报警时间是否需要+8小时时区
     * @return 警情数据
     */
    Page<JqListVO> getJqPageListWithFxxx(@Param("params") ListParamsRequest params, Page<Object> toPage, @Param("timeOffsetEnabled") boolean timeOffsetEnabled);

    /**
     * 获取警情category
     *
     * @param jqlbdm 警情类别代码
     * @param jqlxdm 警情类型代码
     * @param jqxldm 警情细类代码
     * @param jqzldm 警情子类代码
     * @return 警情category
     */
    List<String> getCategory(@Param("jqlbdm") String jqlbdm, @Param("jqlxdm") String jqlxdm,
                             @Param("jqxldm") String jqxldm, @Param("jqzldm") String jqzldm);


    /**
     * 根据线索编号查询线索id
     *
     * @param code 线索编号
     * @return 线索id
     */
    @Select("select id from t_profile_sthy where JJDBH = #{code}")
    Long getIdByCode(@Param("code") String code);


    /**
     * 今日指数
     *
     * @param dto dto
     * @return 结果
     */
    List<JqIndexVO> jqIndex(@Param("dto") JqDTO dto);

    /**
     * 今日指数详情
     *
     * @param dto dto
     * @return 结果
     */
    List<JqIndexDetailVO> jqIndexDetail(@Param("dto") JqDTO dto);

    /**
     * 关联风险数量
     *
     * @param jjdbhList 接警单编号
     * @return 关联风险数量
     */
    List<IdNameCountVO> relatedRiskCount(@Param("jjdbhList") List<String> jjdbhList);

    /**
     * 更新同步的信息
     *
     * @param id 接警单编号
     * @param jq 警情
     */
    void updateSyncInfo(@Param("id") Long id, @Param("jq") JQ jq);

    /**
     * 插入警情当事人信息
     *
     * @param jqDsrxx 警情当事人信息
     */
    void insertDsr(@Param("data") JqDsrxx jqDsrxx);
}
