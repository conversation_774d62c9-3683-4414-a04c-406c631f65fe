package com.trs.police.profile.domain.dto.promotion;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 职级晋升规则DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionRuleDTO {

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 规则类型（如：work_years、education、awards、violations等）
     */
    private String ruleType;

    /**
     * 规则分类（如：basic、performance、discipline等）
     */
    private String category;

    /**
     * 父规则ID
     */
    private String parentRuleId;

    /**
     * 子规则列表
     */
    private List<PromotionRuleDTO> children;

    /**
     * 是否启用
     */
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 优先级（数值越小优先级越高）
     */
    @Builder.Default
    private Integer priority = 0;

    /**
     * 规则配置参数
     */
    private Map<String, Object> ruleConfig;

    /**
     * 适用的职级范围（职级代码列表）
     */
    private List<Integer> applicableRanks;

    /**
     * 规则执行结果类型（QUALIFIED、NOT_QUALIFIED、FORBIDDEN、DEFERRED）
     */
    private String resultType;

    /**
     * 规则权重（用于综合评估）
     */
    @Builder.Default
    private Double weight = 1.0;

    /**
     * 是否为必要条件（true表示不满足此条件直接判定为不符合）
     */
    @Builder.Default
    private Boolean required = false;
}
