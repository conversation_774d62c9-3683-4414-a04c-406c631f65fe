package com.trs.police.profile.domain.dto.promotion;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 职级晋升规则DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionRuleDTO {

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 规则类型（如：WORK_YEARS、VIOLATION、AGE_LIMIT等）
     */
    private String ruleType;

    /**
     * 规则配置参数
     */
    private Map<String, Object> ruleConfig;

    /**
     * 是否为必要条件（true表示不满足此条件直接判定为不符合）
     */
    @Builder.Default
    private Boolean required = false;
}
