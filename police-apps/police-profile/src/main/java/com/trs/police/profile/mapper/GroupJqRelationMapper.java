package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.profile.domain.entity.GroupJqRelation;
import org.apache.ibatis.annotations.Param;

/**
 * 群体jq关联mapper
 *
 * <AUTHOR>
 * @date 2025/6/3
 */
public interface GroupJqRelationMapper extends BaseMapper<GroupJqRelation> {

    /**
     * 保存群体相关警情
     *
     * @param groupId 群体id
     * @param jjdbh 接警单编号
     */
    void saveGroupJq(@Param("groupId") Long groupId, @Param("jjdbh") String jjdbh);
}
