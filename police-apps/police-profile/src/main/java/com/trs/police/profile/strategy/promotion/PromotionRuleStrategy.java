package com.trs.police.profile.strategy.promotion;

import com.trs.police.profile.domain.dto.promotion.PromotionRuleDTO;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;

/**
 * 职级晋升规则策略接口
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
public interface PromotionRuleStrategy {

    /**
     * 评估规则
     *
     * @param police 警员档案信息
     * @param rule   晋升规则
     * @return 规则评估详情
     */
    PromotionRuleDetailVO evaluateRule(ProfilePolice police, PromotionRuleDTO rule);

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 获取支持的规则类型
     *
     * @return 支持的规则类型
     */
    String getSupportedRuleType();

    /**
     * 检查是否支持指定的规则
     *
     * @param ruleName 规则名称
     * @param ruleType 规则类型
     * @return 是否支持
     */
    default boolean supports(String ruleName, String ruleType) {
        return getSupportedRuleType().equals(ruleType) || getStrategyName().equals(ruleName);
    }

    /**
     * 获取策略描述
     *
     * @return 策略描述
     */
    default String getDescription() {
        return "职级晋升规则策略：" + getStrategyName();
    }

    /**
     * 验证警员档案数据是否有效
     *
     * @param police 警员档案
     * @return 是否有效
     */
    default boolean validatePolice(ProfilePolice police) {
        return police != null && police.getId() != null;
    }

    /**
     * 验证规则是否有效
     *
     * @param rule 晋升规则
     * @return 是否有效
     */
    default boolean validateRule(PromotionRuleDTO rule) {
        return rule != null 
                && rule.getRuleId() != null 
                && (rule.getEnabled() == null || rule.getEnabled());
    }
}
