package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 援藏援疆表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_police_assist_tibet_xinjiang_relation", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class PoliceAssistTibetXinjiangRelation extends AbstractBaseEntity {

    /**
     * 关联警员档案表（t_police_profile）的主键
     */
    private Long profileId;

    /**
     * 服务开始时间
     */
    private Date serviceStartTime;

    /**
     * 服务结束时间
     */
    private Date serviceEndTime;

    /**
     * 服务地区，码表，type = police_czyj_fudq
     */
    private Integer serviceArea;


    private Boolean deleted;
}

