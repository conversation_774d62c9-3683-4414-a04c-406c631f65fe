package com.trs.police.profile.strategy.promotion.impl;

import com.trs.police.profile.constant.PromotionStatusEnum;
import com.trs.police.profile.domain.dto.promotion.PromotionRuleDTO;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 违纪违规晋升规则策略
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class ViolationPromotionStrategy implements PromotionRuleStrategy {

    private static final String STRATEGY_NAME = "违纪违规规则";
    private static final String RULE_TYPE = "VIOLATION";

    @Override
    public PromotionRuleDetailVO evaluateRule(ProfilePolice police, PromotionRuleDTO rule) {
        log.debug("开始评估违纪违规规则，警员：{}，规则：{}", police.getName(), rule.getRuleName());

        PromotionRuleDetailVO.PromotionRuleDetailVOBuilder builder = PromotionRuleDetailVO.builder()
                .ruleName(rule.getRuleName())
                .description(rule.getDescription())
                .ruleType(rule.getRuleType())
                .required(rule.getRequired())
                .success(true);

        try {
            // 验证数据
            if (!validatePolice(police) || !validateRule(rule)) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取规则配置
            Map<String, Object> ruleConfig = rule.getRuleConfig();
            
            // 获取违纪违规限制配置
            int maxViolations = getIntFromConfig(ruleConfig, "maxViolations", 0);
            String violationType = getStringFromConfig(ruleConfig, "violationType", "ALL");
            String resultType = getStringFromConfig(ruleConfig, "resultType", "FORBIDDEN");
            
            // TODO: 这里需要根据实际的违纪违规数据结构来实现
            // 目前ProfilePolice实体中没有违纪违规字段，需要从相关表中查询
            // 暂时模拟违纪违规记录数量
            int violationCount = getViolationCount(police, violationType);

            boolean hasViolation = violationCount > maxViolations;
            PromotionStatusEnum result;
            
            if (hasViolation) {
                // 根据配置决定结果类型
                switch (resultType.toUpperCase()) {
                    case "FORBIDDEN":
                        result = PromotionStatusEnum.FORBIDDEN;
                        break;
                    case "DEFERRED":
                        result = PromotionStatusEnum.DEFERRED;
                        break;
                    case "NOT_QUALIFIED":
                    default:
                        result = PromotionStatusEnum.NOT_QUALIFIED;
                        break;
                }
            } else {
                result = PromotionStatusEnum.QUALIFIED;
            }

            String hitData = String.format("违纪违规记录：%d条", violationCount);
            String calculateDescription;
            
            if (hasViolation) {
                calculateDescription = String.format("发现%d条违纪违规记录，超过允许的%d条上限，%s",
                        violationCount, maxViolations, result.getName());
            } else {
                calculateDescription = String.format("违纪违规记录%d条，在允许范围内（≤%d条），符合晋升要求",
                        violationCount, maxViolations);
            }

            return builder
                    .isHit(hasViolation)
                    .ruleResult(result)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("评估违纪违规规则失败，警员：{}，规则：{}，错误：{}", 
                    police.getName(), rule.getRuleName(), e.getMessage(), e);
            return builder
                    .isHit(false)
                    .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                    .success(false)
                    .errorMessage("评估失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    /**
     * 获取违纪违规记录数量
     * TODO: 需要根据实际的数据结构实现
     */
    private int getViolationCount(ProfilePolice police, String violationType) {
        // 这里需要查询相关的违纪违规表
        // 暂时返回0，实际实现时需要注入相应的Mapper或Service
        return 0;
    }

    /**
     * 从配置中获取整数值
     */
    private int getIntFromConfig(Map<String, Object> config, String key, int defaultValue) {
        if (config == null || !config.containsKey(key)) {
            return defaultValue;
        }
        Object value = config.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("配置项{}的值{}无法转换为整数，使用默认值{}", key, value, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 从配置中获取字符串值
     */
    private String getStringFromConfig(Map<String, Object> config, String key, String defaultValue) {
        if (config == null || !config.containsKey(key)) {
            return defaultValue;
        }
        Object value = config.get(key);
        return value != null ? value.toString() : defaultValue;
    }
}
