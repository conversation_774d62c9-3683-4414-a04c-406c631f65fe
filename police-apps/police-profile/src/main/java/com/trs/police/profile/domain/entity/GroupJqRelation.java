package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 群体jq关联信息
 *
 * <AUTHOR> zuo.kaiyuan
 * @date 创建时间：2025/06/03
 * @version 1.0
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_profile_group_jq_relation",autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class GroupJqRelation extends AbstractBaseEntity {
    private static final long serialVersionUID = 3350081240155942324L;

    /**
     * 群体id
     */
    private Long groupId;

    /**
     * 接警单编号
     */
    private String jjdbh;
}
