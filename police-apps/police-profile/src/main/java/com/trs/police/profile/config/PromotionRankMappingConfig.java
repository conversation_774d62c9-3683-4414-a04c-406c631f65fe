package com.trs.police.profile.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 职级晋升映射配置
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "police.promotion")
public class PromotionRankMappingConfig {

    /**
     * 职级晋升映射关系
     * key: 当前职级代码, value: 目标晋升职级代码
     */
    private Map<Integer, Integer> rankMapping = new HashMap<>();

    /**
     * 职级对应的策略配置
     * key: 职级类别, value: Map<目标职级代码, 策略类名列表>
     */
    private Map<String, Map<Integer, List<String>>> rankStrategies = new HashMap<>();

    /**
     * 职级任职年限要求配置
     * key: 目标职级代码, value: Map<前置职级代码, 要求年限>
     */
    private Map<Integer, Map<Integer, Integer>> rankTenureRequirements = new HashMap<>();

    /**
     * 默认的职级晋升映射关系
     */
    private static final Map<Integer, Integer> DEFAULT_RANK_MAPPING = new HashMap<>();

    /**
     * 默认的职级策略配置
     */
    private static final Map<String, Map<Integer, List<String>>> DEFAULT_RANK_STRATEGIES = new HashMap<>();

    /**
     * 默认的职级任职年限要求配置
     */
    private static final Map<Integer, Map<Integer, Integer>> DEFAULT_RANK_TENURE_REQUIREMENTS = new HashMap<>();

    static {
        // 这里配置默认的职级晋升映射关系
        // 示例：三级高级警长(30) -> 二级高级警长(29)
        DEFAULT_RANK_MAPPING.put(30, 29);  // 三级高级警长 -> 二级高级警长
        DEFAULT_RANK_MAPPING.put(29, 28);  // 二级高级警长 -> 一级高级警长
        DEFAULT_RANK_MAPPING.put(28, 27);  // 一级高级警长 -> 三级警务技术
        // 可以根据实际的职级体系继续添加映射关系

        // 执法勤务警员职务晋升策略
        Map<Integer, List<String>> lawEnforcementStrategies = new HashMap<>();

        // 高级警长系列（需要年满50周岁）
        List<String> seniorRankStrategies = new ArrayList<>();
        seniorRankStrategies.add("RankTenurePromotionStrategy");
        seniorRankStrategies.add("ViolationPromotionStrategy");
        seniorRankStrategies.add("SeniorRankAgePromotionStrategy");

        lawEnforcementStrategies.put(28, new ArrayList<>(seniorRankStrategies));  // 一级高级警长
        lawEnforcementStrategies.put(29, new ArrayList<>(seniorRankStrategies));  // 二级高级警长
        lawEnforcementStrategies.put(30, new ArrayList<>(seniorRankStrategies));  // 三级高级警长
        lawEnforcementStrategies.put(31, new ArrayList<>(seniorRankStrategies));  // 四级高级警长

        // 警长和警员系列（无特殊年龄要求）
        List<String> regularStrategies = new ArrayList<>();
        regularStrategies.add("RankTenurePromotionStrategy");
        regularStrategies.add("ViolationPromotionStrategy");

        lawEnforcementStrategies.put(41, new ArrayList<>(regularStrategies));  // 一级警长
        lawEnforcementStrategies.put(42, new ArrayList<>(regularStrategies));  // 二级警长
        lawEnforcementStrategies.put(43, new ArrayList<>(regularStrategies));  // 三级警长
        lawEnforcementStrategies.put(44, new ArrayList<>(regularStrategies));  // 四级警长
        lawEnforcementStrategies.put(51, new ArrayList<>(regularStrategies));  // 一级警员

        DEFAULT_RANK_STRATEGIES.put(CATEGORY_LAW_ENFORCEMENT, lawEnforcementStrategies);

        // 技术职务晋升策略
        Map<Integer, List<String>> technicalStrategies = new HashMap<>();
        List<String> techStrategies = new ArrayList<>();
        techStrategies.add("RankTenurePromotionStrategy");
        techStrategies.add("ViolationPromotionStrategy");

        // 警务技术主任系列
        technicalStrategies.put(11, new ArrayList<>(techStrategies));  // 警务技术一级主任
        technicalStrategies.put(12, new ArrayList<>(techStrategies));  // 警务技术二级主任
        technicalStrategies.put(13, new ArrayList<>(techStrategies));  // 警务技术三级主任
        technicalStrategies.put(14, new ArrayList<>(techStrategies));  // 警务技术四级主任

        // 警务技术主管系列
        technicalStrategies.put(21, new ArrayList<>(techStrategies));  // 警务技术一级主管
        technicalStrategies.put(22, new ArrayList<>(techStrategies));  // 警务技术二级主管
        technicalStrategies.put(23, new ArrayList<>(techStrategies));  // 警务技术三级主管
        technicalStrategies.put(24, new ArrayList<>(techStrategies));  // 警务技术四级主管

        DEFAULT_RANK_STRATEGIES.put(CATEGORY_TECHNICAL, technicalStrategies);
    }

    @PostConstruct
    public void init() {
        // 如果没有配置映射关系，使用默认配置
        if (rankMapping.isEmpty()) {
            rankMapping.putAll(DEFAULT_RANK_MAPPING);
            log.info("使用默认职级晋升映射配置");
        }

        // 如果没有配置策略关系，使用默认配置
        if (rankStrategies.isEmpty()) {
            rankStrategies.putAll(DEFAULT_RANK_STRATEGIES);
            log.info("使用默认职级策略配置");
        }

        // 如果没有配置任职年限要求，使用默认配置
        if (rankTenureRequirements.isEmpty()) {
            rankTenureRequirements.putAll(DEFAULT_RANK_TENURE_REQUIREMENTS);
            log.info("使用默认职级任职年限要求配置");
        }

        log.info("职级晋升映射配置初始化完成，共配置{}个映射关系，{}个策略配置，{}个任职年限要求",
                rankMapping.size(), rankStrategies.size(), rankTenureRequirements.size());
    }

    /**
     * 根据当前职级获取目标晋升职级
     *
     * @param currentRankCode 当前职级代码
     * @return 目标晋升职级代码，如果没有配置则返回null
     */
    public Integer getTargetRank(Integer currentRankCode) {
        if (currentRankCode == null) {
            return null;
        }
        return rankMapping.get(currentRankCode);
    }

    /**
     * 检查是否支持从当前职级晋升
     *
     * @param currentRankCode 当前职级代码
     * @return 是否支持晋升
     */
    public boolean canPromote(Integer currentRankCode) {
        return getTargetRank(currentRankCode) != null;
    }

    /**
     * 添加职级映射关系
     *
     * @param currentRank 当前职级代码
     * @param targetRank  目标职级代码
     */
    public void addRankMapping(Integer currentRank, Integer targetRank) {
        if (currentRank != null && targetRank != null) {
            rankMapping.put(currentRank, targetRank);
            log.debug("添加职级映射关系：{} -> {}", currentRank, targetRank);
        }
    }

    /**
     * 移除职级映射关系
     *
     * @param currentRank 当前职级代码
     */
    public void removeRankMapping(Integer currentRank) {
        if (currentRank != null) {
            rankMapping.remove(currentRank);
            log.debug("移除职级映射关系：{}", currentRank);
        }
    }

    /**
     * 根据职级类别和目标职级获取策略类名列表
     *
     * @param category       职级类别
     * @param targetRankCode 目标职级代码
     * @return 策略类名列表
     */
    public List<String> getStrategiesByTargetRank(String category, Integer targetRankCode) {
        if (category == null || targetRankCode == null) {
            return new ArrayList<>();
        }
        Map<Integer, List<String>> categoryStrategies = rankStrategies.get(category);
        if (categoryStrategies == null) {
            return new ArrayList<>();
        }
        return categoryStrategies.getOrDefault(targetRankCode, new ArrayList<>());
    }

    /**
     * 添加职级策略配置
     *
     * @param category   职级类别
     * @param targetRank 目标职级代码
     * @param strategies 策略类名列表
     */
    public void addRankStrategies(String category, Integer targetRank, List<String> strategies) {
        if (category != null && targetRank != null && strategies != null) {
            rankStrategies.computeIfAbsent(category, k -> new HashMap<>())
                    .put(targetRank, new ArrayList<>(strategies));
            log.debug("添加职级策略配置：{} - {} -> {}", category, targetRank, strategies);
        }
    }

    /**
     * 根据目标职级获取职级类别
     *
     * @param targetRankCode 目标职级代码
     * @return 职级类别
     */
    public String getCategoryByTargetRank(Integer targetRankCode) {
        if (targetRankCode == null) {
            return null;
        }

        // 根据职级代码范围判断类别
        // 这里需要根据实际的职级编码规则来实现
        if (targetRankCode >= 11 && targetRankCode <= 31) {
            return CATEGORY_TECHNICAL;  // 技术职务：11-31
        } else if (targetRankCode >= 28 && targetRankCode <= 52) {
            return CATEGORY_LAW_ENFORCEMENT;  // 执法勤务：28-52
        }

        // 默认返回执法勤务
        return CATEGORY_LAW_ENFORCEMENT;
    }

    /**
     * 获取所有映射关系
     *
     * @return 映射关系副本
     */
    public Map<Integer, Integer> getAllMappings() {
        return new HashMap<>(rankMapping);
    }

    /**
     * 获取所有策略配置
     *
     * @return 策略配置副本
     */
    public Map<String, Map<Integer, List<String>>> getAllStrategies() {
        return new HashMap<>(rankStrategies);
    }

    /**
     * 获取指定类别的策略配置
     *
     * @param category 职级类别
     * @return 该类别的策略配置
     */
    public Map<Integer, List<String>> getStrategiesByCategory(String category) {
        return rankStrategies.getOrDefault(category, new HashMap<>());
    }

    /**
     * 根据目标职级获取任职年限要求
     *
     * @param targetRankCode 目标职级代码
     * @return 任职年限要求映射（前置职级代码 -> 要求年限）
     */
    public Map<Integer, Integer> getTenureRequirements(Integer targetRankCode) {
        if (targetRankCode == null) {
            return new HashMap<>();
        }
        return rankTenureRequirements.getOrDefault(targetRankCode, new HashMap<>());
    }

    /**
     * 添加任职年限要求配置
     *
     * @param targetRank         目标职级代码
     * @param tenureRequirements 任职年限要求映射
     */
    public void addTenureRequirements(Integer targetRank, Map<Integer, Integer> tenureRequirements) {
        if (targetRank != null && tenureRequirements != null) {
            rankTenureRequirements.put(targetRank, new HashMap<>(tenureRequirements));
            log.debug("添加任职年限要求配置：{} -> {}", targetRank, tenureRequirements);
        }
    }

    /**
     * 获取所有任职年限要求配置
     *
     * @return 任职年限要求配置副本
     */
    public Map<Integer, Map<Integer, Integer>> getAllTenureRequirements() {
        return new HashMap<>(rankTenureRequirements);
    }
}
