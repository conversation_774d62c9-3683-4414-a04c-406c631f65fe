package com.trs.police.profile.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 职级晋升映射配置
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "police.promotion")
public class PromotionRankMappingConfig {

    /**
     * 职级晋升映射关系
     * key: 当前职级代码, value: 目标晋升职级代码
     */
    private Map<Integer, Integer> rankMapping = new HashMap<>();

    /**
     * 职级对应的策略配置
     * key: 职级类别, value: Map<目标职级代码, 策略类名列表>
     */
    private Map<String, Map<Integer, List<String>>> rankStrategies = new HashMap<>();

    /**
     * 默认的职级晋升映射关系
     */
    private static final Map<Integer, Integer> DEFAULT_RANK_MAPPING = new HashMap<>();

    /**
     * 默认的职级策略配置
     */
    private static final Map<Integer, List<String>> DEFAULT_RANK_STRATEGIES = new HashMap<>();

    static {
        // 这里配置默认的职级晋升映射关系
        // 示例：三级高级警长(30) -> 二级高级警长(29)
        DEFAULT_RANK_MAPPING.put(30, 29);  // 三级高级警长 -> 二级高级警长
        DEFAULT_RANK_MAPPING.put(29, 28);  // 二级高级警长 -> 一级高级警长
        DEFAULT_RANK_MAPPING.put(28, 27);  // 一级高级警长 -> 三级警务技术
        // 可以根据实际的职级体系继续添加映射关系

        // 配置默认的职级策略
        List<String> defaultStrategies = new ArrayList<>();
        defaultStrategies.add("WorkYearsPromotionStrategy");
        defaultStrategies.add("ViolationPromotionStrategy");
        defaultStrategies.add("AgePromotionStrategy");

        // 为所有职级配置默认策略
        DEFAULT_RANK_STRATEGIES.put(29, new ArrayList<>(defaultStrategies));  // 二级高级警长
        DEFAULT_RANK_STRATEGIES.put(28, new ArrayList<>(defaultStrategies));  // 一级高级警长
        DEFAULT_RANK_STRATEGIES.put(27, new ArrayList<>(defaultStrategies));  // 三级警务技术
    }

    @PostConstruct
    public void init() {
        // 如果没有配置映射关系，使用默认配置
        if (rankMapping.isEmpty()) {
            rankMapping.putAll(DEFAULT_RANK_MAPPING);
            log.info("使用默认职级晋升映射配置");
        }

        // 如果没有配置策略关系，使用默认配置
        if (rankStrategies.isEmpty()) {
            rankStrategies.putAll(DEFAULT_RANK_STRATEGIES);
            log.info("使用默认职级策略配置");
        }

        log.info("职级晋升映射配置初始化完成，共配置{}个映射关系，{}个策略配置",
                rankMapping.size(), rankStrategies.size());
    }

    /**
     * 根据当前职级获取目标晋升职级
     *
     * @param currentRankCode 当前职级代码
     * @return 目标晋升职级代码，如果没有配置则返回null
     */
    public Integer getTargetRank(Integer currentRankCode) {
        if (currentRankCode == null) {
            return null;
        }
        return rankMapping.get(currentRankCode);
    }

    /**
     * 检查是否支持从当前职级晋升
     *
     * @param currentRankCode 当前职级代码
     * @return 是否支持晋升
     */
    public boolean canPromote(Integer currentRankCode) {
        return getTargetRank(currentRankCode) != null;
    }

    /**
     * 添加职级映射关系
     *
     * @param currentRank 当前职级代码
     * @param targetRank  目标职级代码
     */
    public void addRankMapping(Integer currentRank, Integer targetRank) {
        if (currentRank != null && targetRank != null) {
            rankMapping.put(currentRank, targetRank);
            log.debug("添加职级映射关系：{} -> {}", currentRank, targetRank);
        }
    }

    /**
     * 移除职级映射关系
     *
     * @param currentRank 当前职级代码
     */
    public void removeRankMapping(Integer currentRank) {
        if (currentRank != null) {
            rankMapping.remove(currentRank);
            log.debug("移除职级映射关系：{}", currentRank);
        }
    }

    /**
     * 根据目标职级获取策略类名列表
     *
     * @param targetRankCode 目标职级代码
     * @return 策略类名列表
     */
    public List<String> getStrategiesByTargetRank(Integer targetRankCode) {
        if (targetRankCode == null) {
            return new ArrayList<>();
        }
        return rankStrategies.getOrDefault(targetRankCode, new ArrayList<>());
    }

    /**
     * 添加职级策略配置
     *
     * @param targetRank 目标职级代码
     * @param strategies 策略类名列表
     */
    public void addRankStrategies(Integer targetRank, List<String> strategies) {
        if (targetRank != null && strategies != null) {
            rankStrategies.put(targetRank, new ArrayList<>(strategies));
            log.debug("添加职级策略配置：{} -> {}", targetRank, strategies);
        }
    }

    /**
     * 获取所有映射关系
     *
     * @return 映射关系副本
     */
    public Map<Integer, Integer> getAllMappings() {
        return new HashMap<>(rankMapping);
    }

    /**
     * 获取所有策略配置
     *
     * @return 策略配置副本
     */
    public Map<Integer, List<String>> getAllStrategies() {
        return new HashMap<>(rankStrategies);
    }
}
