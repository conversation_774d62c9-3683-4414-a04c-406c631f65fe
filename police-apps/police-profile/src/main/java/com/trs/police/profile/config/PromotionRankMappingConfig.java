package com.trs.police.profile.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 职级晋升映射配置
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "police.promotion")
public class PromotionRankMappingConfig {

    /**
     * 职级晋升映射关系
     * key: 当前职级代码, value: 目标晋升职级代码
     */
    private Map<Integer, Integer> rankMapping = new HashMap<>();

    /**
     * 默认的职级晋升映射关系
     */
    private static final Map<Integer, Integer> DEFAULT_RANK_MAPPING = new HashMap<>();

    static {
        // 这里配置默认的职级晋升映射关系
        // 示例：三级高级警长(30) -> 二级高级警长(29)
        DEFAULT_RANK_MAPPING.put(30, 29);  // 三级高级警长 -> 二级高级警长
        DEFAULT_RANK_MAPPING.put(29, 28);  // 二级高级警长 -> 一级高级警长
        DEFAULT_RANK_MAPPING.put(28, 27);  // 一级高级警长 -> 三级警务技术
        // 可以根据实际的职级体系继续添加映射关系
    }

    @PostConstruct
    public void init() {
        // 如果没有配置映射关系，使用默认配置
        if (rankMapping.isEmpty()) {
            rankMapping.putAll(DEFAULT_RANK_MAPPING);
            log.info("使用默认职级晋升映射配置");
        }
        log.info("职级晋升映射配置初始化完成，共配置{}个映射关系", rankMapping.size());
    }

    /**
     * 根据当前职级获取目标晋升职级
     *
     * @param currentRankCode 当前职级代码
     * @return 目标晋升职级代码，如果没有配置则返回null
     */
    public Integer getTargetRank(Integer currentRankCode) {
        if (currentRankCode == null) {
            return null;
        }
        return rankMapping.get(currentRankCode);
    }

    /**
     * 检查是否支持从当前职级晋升
     *
     * @param currentRankCode 当前职级代码
     * @return 是否支持晋升
     */
    public boolean canPromote(Integer currentRankCode) {
        return getTargetRank(currentRankCode) != null;
    }

    /**
     * 添加职级映射关系
     *
     * @param currentRank 当前职级代码
     * @param targetRank  目标职级代码
     */
    public void addRankMapping(Integer currentRank, Integer targetRank) {
        if (currentRank != null && targetRank != null) {
            rankMapping.put(currentRank, targetRank);
            log.debug("添加职级映射关系：{} -> {}", currentRank, targetRank);
        }
    }

    /**
     * 移除职级映射关系
     *
     * @param currentRank 当前职级代码
     */
    public void removeRankMapping(Integer currentRank) {
        if (currentRank != null) {
            rankMapping.remove(currentRank);
            log.debug("移除职级映射关系：{}", currentRank);
        }
    }

    /**
     * 获取所有映射关系
     *
     * @return 映射关系副本
     */
    public Map<Integer, Integer> getAllMappings() {
        return new HashMap<>(rankMapping);
    }
}
