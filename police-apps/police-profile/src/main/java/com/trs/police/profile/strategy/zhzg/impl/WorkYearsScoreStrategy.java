package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;

/**
 * 工作年限积分计算策略
 * 规则描述：工作年限每满1个月计0.1分
 */
@Slf4j
@Component
public class WorkYearsScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "工作年限";
    private static final String RULE_TYPE = "WORK_YEARS";
    private static final double SCORE_PER_MONTH = 0.1;

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算工作年限积分，人员：{}，规则：{}", personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取参加工作日期
            LocalDate joinJobDate = personArchive.getJoinJobDate();
            if (joinJobDate == null) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("未找到参加工作日期")
                        .build();
            }

            // 计算工作月数
            LocalDate currentDate = LocalDate.now();
            if (joinJobDate.isAfter(currentDate)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("参加工作日期不能晚于当前日期")
                        .build();
            }

            Period period = Period.between(joinJobDate, currentDate);
            int totalMonths = period.getYears() * 12 + period.getMonths();

            // 计算积分
            double calculatedScore = totalMonths * SCORE_PER_MONTH;

            // 应用规则配置的最大分值限制
            double finalScore = calculatedScore;
            if (rule.getScore() != null && calculatedScore > rule.getScore()) {
                finalScore = rule.getScore();
            }

            String hitData = String.format("参加工作日期：%s，工作月数：%d个月", joinJobDate, totalMonths);
            String calculateDescription = String.format("工作%d个月 × %.1f分/月 = %.1f分", 
                    totalMonths, SCORE_PER_MONTH, calculatedScore);
            
            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.1f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算工作年限积分失败，人员：{}，规则：{}，错误：{}", 
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public boolean supports(Long ruleId, String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
