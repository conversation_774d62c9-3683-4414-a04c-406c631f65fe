package com.trs.police.profile.factory.zhzg;

import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 智慧政工积分策略工厂
 */
@Slf4j
@Component
public class ZhzgScoreStrategyFactory {

    private final Map<String, ZhzgScoreStrategy> strategyByName = new ConcurrentHashMap<>();
    private final Map<String, ZhzgScoreStrategy> strategyByRuleType = new ConcurrentHashMap<>();
    private final Map<Long, ZhzgScoreStrategy> strategyByRuleId = new ConcurrentHashMap<>();

    @Autowired
    public ZhzgScoreStrategyFactory(List<ZhzgScoreStrategy> strategies) {
        for (ZhzgScoreStrategy strategy : strategies) {
            registerStrategy(strategy);
        }
        log.info("智慧政工积分策略工厂初始化完成，共注册{}个策略", strategies.size());
    }

    /**
     * 注册策略
     *
     * @param strategy 积分策略
     */
    public void registerStrategy(ZhzgScoreStrategy strategy) {
        if (strategy == null) {
            return;
        }

        String strategyName = strategy.getStrategyName();
        String ruleType = strategy.getSupportedRuleType();

        if (strategyName != null) {
            strategyByName.put(strategyName, strategy);
        }

        if (ruleType != null) {
            strategyByRuleType.put(ruleType, strategy);
        }

        log.debug("注册积分策略：{}, 支持规则类型：{}", strategyName, ruleType);
    }

    /**
     * 根据规则ID获取策略
     *
     * @param ruleId 规则ID
     * @return 积分策略
     */
    public ZhzgScoreStrategy getStrategyByRuleId(Long ruleId) {
        return strategyByRuleId.get(ruleId);
    }

    /**
     * 根据规则名称获取策略
     *
     * @param ruleName 规则名称
     * @return 积分策略
     */
    public ZhzgScoreStrategy getStrategyByRuleName(String ruleName) {
        return strategyByName.get(ruleName);
    }

    /**
     * 根据规则类型获取策略
     *
     * @param ruleType 规则类型
     * @return 积分策略
     */
    public ZhzgScoreStrategy getStrategyByRuleType(String ruleType) {
        return strategyByRuleType.get(ruleType);
    }

    /**
     * 获取策略（优先级：规则ID > 规则名称 > 规则类型）
     *
     * @param ruleId   规则ID
     * @param ruleName 规则名称
     * @param ruleType 规则类型
     * @return 积分策略
     */
    public ZhzgScoreStrategy getStrategy(Long ruleId, String ruleName, String ruleType) {
        ZhzgScoreStrategy strategy = null;

        // 1. 优先按规则ID查找
        if (ruleId != null) {
            strategy = getStrategyByRuleId(ruleId);
            if (strategy != null) {
                log.debug("根据规则ID找到策略：{} -> {}", ruleId, strategy.getStrategyName());
                return strategy;
            }
        }

        // 2. 按规则名称查找
        if (ruleName != null && !ruleName.trim().isEmpty()) {
            strategy = getStrategyByRuleName(ruleName);
            if (strategy != null) {
                log.debug("根据规则名称找到策略：{} -> {}", ruleName, strategy.getStrategyName());
                return strategy;
            }
        }

        // 3. 按规则类型查找
        if (ruleType != null && !ruleType.trim().isEmpty()) {
            strategy = getStrategyByRuleType(ruleType);
            if (strategy != null) {
                log.debug("根据规则类型找到策略：{} -> {}", ruleType, strategy.getStrategyName());
                return strategy;
            }
        }

        // 4. 遍历所有策略，使用supports方法判断
        for (ZhzgScoreStrategy s : strategyByName.values()) {
            if (s.supports(ruleId, ruleName, ruleType)) {
                log.debug("通过supports方法找到策略：{},{},{} -> {}", ruleId, ruleName, ruleType, s.getStrategyName());
                return s;
            }
        }

        log.warn("未找到匹配的积分策略，规则ID：{}，规则名称：{}，规则类型：{}", ruleId, ruleName, ruleType);
        return null;
    }

    /**
     * 手动注册规则ID与策略的映射关系
     *
     * @param ruleId   规则ID
     * @param strategy 积分策略
     */
    public void registerRuleIdMapping(Long ruleId, ZhzgScoreStrategy strategy) {
        if (ruleId != null && strategy != null) {
            strategyByRuleId.put(ruleId, strategy);
            log.debug("注册规则ID映射：{} -> {}", ruleId, strategy.getStrategyName());
        }
    }

}
