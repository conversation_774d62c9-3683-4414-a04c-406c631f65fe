package com.trs.police.profile.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.profile.domain.dto.zhzg.CreateZhzgScoreRuleRequestDTO;
import com.trs.police.profile.domain.dto.zhzg.GetZhzgScoreRuleTreeLeafDTO;
import com.trs.police.profile.domain.dto.zhzg.GetZhzgScoreRuleTreeNonLeafDTO;
import com.trs.police.profile.domain.entity.zhzg.ZhzgScoreRuleEntity;
import com.trs.police.profile.mapper.zhzg.ZhzgScoreRuleMapper;
import com.trs.police.profile.service.zhzg.ZhzgScoreService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 积分规则服务实现
 */

@Slf4j
@Service
public class ZhzgScoreServiceImpl implements ZhzgScoreService {

    @Resource
    private ZhzgScoreRuleMapper zhzgScoreRuleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createScoreRule(CreateZhzgScoreRuleRequestDTO request) {
        ZhzgScoreRuleEntity entity = new ZhzgScoreRuleEntity();
        if (request.getId() != null) {
            entity = zhzgScoreRuleMapper.selectById(request.getId());
        }
        BeanUtils.copyProperties(request, entity);
        if (request.getId() != null) {
            zhzgScoreRuleMapper.updateById(entity);
        } else {
            // 新增规则，设置默认启用状态
            entity.setIsEnabled(true);
            zhzgScoreRuleMapper.insert(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteScoreRule(Long id) {
        if (zhzgScoreRuleMapper.selectCount(Wrappers.lambdaQuery(ZhzgScoreRuleEntity.class)
                .eq(ZhzgScoreRuleEntity::getParentId, id)) > 0) {
            throw new RuntimeException("存在子规则，无法删除");
        }
        zhzgScoreRuleMapper.deleteById(id);
    }

    @Override
    public List<GetZhzgScoreRuleTreeNonLeafDTO> getNonLeafScoreTree() {
        // 查询所有非叶子节点
        List<ZhzgScoreRuleEntity> ruleEntities = zhzgScoreRuleMapper.selectList(Wrappers.lambdaQuery(ZhzgScoreRuleEntity.class)
                .eq(ZhzgScoreRuleEntity::getIsLeaf, false)
                .orderByAsc(ZhzgScoreRuleEntity::getId));

        // 转换为DTO
        List<GetZhzgScoreRuleTreeNonLeafDTO> dtoList = ruleEntities.stream()
                .map(this::convertToNonLeafDTO)
                .collect(java.util.stream.Collectors.toList());

        // 使用泛型方法构建树形结构
        return com.trs.police.profile.util.zhzg.ZhzgScoreUtil.buildTree(dtoList);
    }

    /**
     * 将实体转换为非叶子节点DTO
     *
     * @param entity 实体对象
     * @return DTO对象
     */
    private GetZhzgScoreRuleTreeNonLeafDTO convertToNonLeafDTO(ZhzgScoreRuleEntity entity) {
        GetZhzgScoreRuleTreeNonLeafDTO dto = new GetZhzgScoreRuleTreeNonLeafDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setFullScore(entity.getFullScore());
        dto.setParentId(entity.getParentId());
        return dto;
    }

    @Override
    public RestfulResultsV2<GetZhzgScoreRuleTreeLeafDTO> getLeafScoreTree(Long parentId,
                                                                          PageParams pageParams,
                                                                          Integer applicableRank,
                                                                          String name,
                                                                          String desc) {
        Page<ZhzgScoreRuleEntity> page = zhzgScoreRuleMapper.selectPage(pageParams.toPage(), Wrappers.lambdaQuery(ZhzgScoreRuleEntity.class)
                .eq(ZhzgScoreRuleEntity::getIsLeaf, true)
                .eq(ZhzgScoreRuleEntity::getParentId, parentId)
                .eq(Objects.nonNull(applicableRank), ZhzgScoreRuleEntity::getApplicableRank, applicableRank)
                .likeRight(Objects.nonNull(name), ZhzgScoreRuleEntity::getName, name)
                .likeRight(Objects.nonNull(desc), ZhzgScoreRuleEntity::getDescription, desc));
        List<GetZhzgScoreRuleTreeLeafDTO> result = page.getRecords()
                .stream()
                .map(entity -> {
                    GetZhzgScoreRuleTreeLeafDTO dto = new GetZhzgScoreRuleTreeLeafDTO();
                    BeanUtils.copyProperties(entity, dto);
                    return dto;
                })
                .collect(Collectors.toList());
        return RestfulResultsV2.ok(result)
                .addPageNum(pageParams.getPageNumber())
                .addPageSize(pageParams.getPageSize())
                .addTotalCount(page.getTotal());
    }

    @Override
    public void changeScoreRuleEnabledStatus(Long id, Boolean enabled) {
        zhzgScoreRuleMapper.update(null, Wrappers.lambdaUpdate(ZhzgScoreRuleEntity.class)
                .eq(ZhzgScoreRuleEntity::getId, id)
                .set(ZhzgScoreRuleEntity::getIsEnabled, enabled));
    }
}
