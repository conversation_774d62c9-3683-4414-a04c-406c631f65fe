package com.trs.police.profile.service;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.profile.JqCommonVO;
import com.trs.police.common.core.vo.profile.JqFkxxCommonVO;
import com.trs.police.profile.domain.dto.JqDTO;
import com.trs.police.profile.domain.vo.JqIndexDetailVO;
import com.trs.police.profile.domain.vo.JqIndexVO;
import com.trs.police.profile.domain.vo.JqListVO;
import com.trs.police.profile.domain.vo.JqVO;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/29 14:52
 */
public interface JqService {

    /**
     * 警情列表
     *
     * @param request 请求参数
     * @return 警情信息
     */
    PageResult<JqListVO> getJqPageList(ListParamsRequest request);

    /**
     * 警情列表
     *
     * @param request 请求参数
     * @param operateFilterParams 操作过滤参数
     * @return 警情信息
     */
    PageResult<JqListVO> getJqPageList(ListParamsRequest request, List<KeyValueTypeVO> operateFilterParams);

    /**
     * 警情列表(关联反馈信息)
     *
     * @param request 请求参数
     * @return 警情信息
     */
    PageResult<JqListVO> getJqPageListWithFkxx(ListParamsRequest request);

    /**
     * 根据线索编号查询线索id
     *
     * @param code 线索编号
     * @return 线索id
     */
    Long getIdByCode(String code);

    /**
     * 根据编号拉取新的警情数据
     *
     * @param code 编号
     * @param relationType 关联方式
     */
    void insertByCode(String code, String relationType);

    /**
     * 根据编号拉取老的警情数据
     *
     * @param code 编号
     * @param relationType 关联方式
     */
    void insertOldJqByCode(String code, String relationType);

    /**
     * 处理历史出警到达时间
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void dealHistoryDdxcsj(String startTime, String endTime);

    /**
     * 今日指数
     *
     * @return {@link RestfulResultsV2}<{@link JqIndexVO}>
     */
    RestfulResultsV2<JqIndexVO> todayIndex();

    /**
     * 今日指数详情
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JqIndexDetailVO}>
     */
    RestfulResultsV2<JqIndexDetailVO> todayIndexDetail(JqDTO dto);

    /**
     * 报警列表
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<JqVO> bjListV2(JqDTO dto);

    /**
     * 处理历史警情标签
     *
     */
    void dealHistoryJqbq();

    /**
     * 根据警情编号查询警情
     *
     * @param bh bh
     * @return 警情
     */
    JqCommonVO findByBh(String bh);

    /**
     * 根据警情编号查下警情反馈信息
     *
     * @param jjdbh 警情编号
     * @return 反馈信息
     */
    List<JqFkxxCommonVO> findFkxxByBh(String jjdbh);

    /**
     * 根据警情编号查下最新的警情反馈信息
     *
     * @param jjdbh 接警单编号
     * @return 最新的反馈信息
     */
    JqFkxxCommonVO findNewFkxxByBh(String jjdbh);

    /**
     * 根据警情编号查询警情
     *
     * @param id id
     * @return 警情
     */
    List<JqCommonVO> findById(String id);

    /**
     * 根据电话号码和身份证查询
     *
     * @param idCard 身份证
     * @param tel 电话
     * @return 警情列表
     */
    List<JqCommonVO> findByTelAndIdCard(String idCard, String tel);

    /**
     * 根据电话号码和身份证查询
     *
     * @param idCard 身份证
     * @param tel 电话
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 警情列表
     */
    RestfulResultsV2<JqCommonVO> findByTelAndIdCardPage(String idCard, String tel, Integer pageNum, Integer pageSize);

    /**
     * stqz大屏，获取警情类型
     *
     * @return {@link String}
     */
    String stqjdpJqType();

    /**
     * 根据群体id查询警情
     *
     * @param groupId 群体id
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 警情列表
     */
    RestfulResultsV2<JqCommonVO> findByGroupId(Long groupId, Integer pageNum, Integer pageSize);
}
