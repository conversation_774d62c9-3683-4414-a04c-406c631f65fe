package com.trs.police.profile.strategy.promotion.impl;

import com.trs.police.profile.constant.PromotionStatusEnum;
import com.trs.police.profile.domain.dto.promotion.PromotionRuleDTO;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Map;

/**
 * 工作年限晋升规则策略
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class WorkYearsPromotionStrategy implements PromotionRuleStrategy {

    private static final String STRATEGY_NAME = "工作年限规则";
    private static final String RULE_TYPE = "WORK_YEARS";

    @Override
    public PromotionRuleDetailVO evaluateRule(ProfilePolice police, PromotionRuleDTO rule) {
        log.debug("开始评估工作年限规则，警员：{}，规则：{}", police.getName(), rule.getRuleName());

        PromotionRuleDetailVO.PromotionRuleDetailVOBuilder builder = PromotionRuleDetailVO.builder()
                .ruleId(rule.getRuleId())
                .ruleName(rule.getRuleName())
                .description(rule.getDescription())
                .ruleType(rule.getRuleType())
                .category(rule.getCategory())
                .parentRuleId(rule.getParentRuleId())
                .priority(rule.getPriority())
                .weight(rule.getWeight())
                .required(rule.getRequired())
                .success(true);

        try {
            // 验证数据
            if (!validatePolice(police) || !validateRule(rule)) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取参加工作日期
            if (police.getJoinWorkDate() == null) {
                return builder
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription("缺少参加工作日期信息")
                        .build();
            }

            // 计算工作年限
            LocalDate joinWorkDate = police.getJoinWorkDate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate currentDate = LocalDate.now();
            Period period = Period.between(joinWorkDate, currentDate);
            int workYears = period.getYears();
            int workMonths = period.getMonths();

            // 获取规则配置
            Map<String, Object> ruleConfig = rule.getRuleConfig();
            int requiredYears = getIntFromConfig(ruleConfig, "requiredYears", 5);
            int requiredMonths = getIntFromConfig(ruleConfig, "requiredMonths", 0);

            // 计算总月数进行比较
            int totalWorkMonths = workYears * 12 + workMonths;
            int requiredTotalMonths = requiredYears * 12 + requiredMonths;

            boolean isQualified = totalWorkMonths >= requiredTotalMonths;
            PromotionStatusEnum result = isQualified ? PromotionStatusEnum.QUALIFIED : PromotionStatusEnum.NOT_QUALIFIED;

            String hitData = String.format("工作年限：%d年%d个月", workYears, workMonths);
            String calculateDescription = String.format("参加工作日期：%s，当前工作年限：%d年%d个月，要求：%d年%d个月，%s",
                    joinWorkDate, workYears, workMonths, requiredYears, requiredMonths,
                    isQualified ? "符合要求" : "不符合要求");

            return builder
                    .isHit(isQualified)
                    .ruleResult(result)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("评估工作年限规则失败，警员：{}，规则：{}，错误：{}", 
                    police.getName(), rule.getRuleName(), e.getMessage(), e);
            return builder
                    .isHit(false)
                    .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                    .success(false)
                    .errorMessage("评估失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    /**
     * 从配置中获取整数值
     */
    private int getIntFromConfig(Map<String, Object> config, String key, int defaultValue) {
        if (config == null || !config.containsKey(key)) {
            return defaultValue;
        }
        Object value = config.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("配置项{}的值{}无法转换为整数，使用默认值{}", key, value, defaultValue);
            return defaultValue;
        }
    }
}
