package com.trs.police.profile.service.impl;

import com.trs.police.profile.domain.dto.zhzg.GetZhzgScoreRuleTreeNonLeafDTO;
import com.trs.police.profile.util.zhzg.GenericTreeUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * ZhzgScoreServiceImpl 测试类
 */
public class ZhzgScoreServiceImplTest {

    /**
     * 测试 getNonLeafScoreTree 方法的逻辑
     */
    public static void main(String[] args) {
        System.out.println("=== 测试 getNonLeafScoreTree 方法 ===\n");

        // 模拟从数据库查询出来的非叶子节点数据
        List<GetZhzgScoreRuleTreeNonLeafDTO> mockData = createMockNonLeafData();

        System.out.println("1. 原始数据（扁平结构）：");
        for (GetZhzgScoreRuleTreeNonLeafDTO dto : mockData) {
            System.out.println("  ID: " + dto.getId() + ", Name: " + dto.getName() + ", ParentID: " + dto.getParentId());
        }

        // 使用泛型方法构建树形结构
        List<GetZhzgScoreRuleTreeNonLeafDTO> tree = GenericTreeUtil.buildTree(mockData);

        System.out.println("\n2. 构建后的树形结构：");
        printTree(tree, 0);

        // 验证结果
        System.out.println("\n3. 验证结果：");
        validateTree(tree);

        System.out.println("\n=== 测试完成 ===");
    }

    /**
     * 创建模拟的非叶子节点数据
     */
    private static List<GetZhzgScoreRuleTreeNonLeafDTO> createMockNonLeafData() {
        List<GetZhzgScoreRuleTreeNonLeafDTO> data = new ArrayList<>();

        // 根节点
        GetZhzgScoreRuleTreeNonLeafDTO root1 = new GetZhzgScoreRuleTreeNonLeafDTO();
        root1.setId(1L);
        root1.setName("基础素质");
        root1.setParentId(null);
        data.add(root1);

        GetZhzgScoreRuleTreeNonLeafDTO root2 = new GetZhzgScoreRuleTreeNonLeafDTO();
        root2.setId(2L);
        root2.setName("表现评价");
        root2.setParentId(null);
        data.add(root2);

        // 二级节点
        GetZhzgScoreRuleTreeNonLeafDTO child11 = new GetZhzgScoreRuleTreeNonLeafDTO();
        child11.setId(11L);
        child11.setName("教育背景");
        child11.setParentId(1L);
        data.add(child11);

        GetZhzgScoreRuleTreeNonLeafDTO child12 = new GetZhzgScoreRuleTreeNonLeafDTO();
        child12.setId(12L);
        child12.setName("工作经历");
        child12.setParentId(1L);
        data.add(child12);

        GetZhzgScoreRuleTreeNonLeafDTO child21 = new GetZhzgScoreRuleTreeNonLeafDTO();
        child21.setId(21L);
        child21.setName("奖励记录");
        child21.setParentId(2L);
        data.add(child21);

        GetZhzgScoreRuleTreeNonLeafDTO child22 = new GetZhzgScoreRuleTreeNonLeafDTO();
        child22.setId(22L);
        child22.setName("违纪记录");
        child22.setParentId(2L);
        data.add(child22);

        // 三级节点
        GetZhzgScoreRuleTreeNonLeafDTO child111 = new GetZhzgScoreRuleTreeNonLeafDTO();
        child111.setId(111L);
        child111.setName("学历分类");
        child111.setParentId(11L);
        data.add(child111);

        GetZhzgScoreRuleTreeNonLeafDTO child211 = new GetZhzgScoreRuleTreeNonLeafDTO();
        child211.setId(211L);
        child211.setName("立功奖励");
        child211.setParentId(21L);
        data.add(child211);

        return data;
    }

    /**
     * 打印树形结构
     */
    private static void printTree(List<GetZhzgScoreRuleTreeNonLeafDTO> nodes, int level) {
        String indent = "  ".repeat(level);
        for (GetZhzgScoreRuleTreeNonLeafDTO node : nodes) {
            System.out.println(indent + "├─ " + node.getName() + " (ID: " + node.getId() + ")");
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                printTree(node.getChildren(), level + 1);
            }
        }
    }

    /**
     * 验证树形结构
     */
    private static void validateTree(List<GetZhzgScoreRuleTreeNonLeafDTO> tree) {
        if (tree.size() == 2) {
            System.out.println("✓ 根节点数量正确：" + tree.size());
        } else {
            System.out.println("✗ 根节点数量错误，期望2个，实际" + tree.size() + "个");
        }

        // 验证第一个根节点
        GetZhzgScoreRuleTreeNonLeafDTO firstRoot = tree.get(0);
        if ("基础素质".equals(firstRoot.getName()) && firstRoot.getChildren().size() == 2) {
            System.out.println("✓ 第一个根节点及其子节点正确");
        } else {
            System.out.println("✗ 第一个根节点或其子节点错误");
        }

        // 验证第二个根节点
        GetZhzgScoreRuleTreeNonLeafDTO secondRoot = tree.get(1);
        if ("表现评价".equals(secondRoot.getName()) && secondRoot.getChildren().size() == 2) {
            System.out.println("✓ 第二个根节点及其子节点正确");
        } else {
            System.out.println("✗ 第二个根节点或其子节点错误");
        }

        // 验证三级节点
        GetZhzgScoreRuleTreeNonLeafDTO educationNode = firstRoot.getChildren().get(0);
        if (educationNode.getChildren() != null && educationNode.getChildren().size() == 1) {
            System.out.println("✓ 三级节点构建正确");
        } else {
            System.out.println("✗ 三级节点构建错误");
        }

        System.out.println("✓ 泛型树构建方法在 GetZhzgScoreRuleTreeNonLeafDTO 上工作正常！");
    }
}
