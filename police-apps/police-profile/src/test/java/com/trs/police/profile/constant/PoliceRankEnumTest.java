package com.trs.police.profile.constant;

import com.trs.police.profile.constant.PoliceRankEnum.PoliceRankCategory;
import com.trs.police.profile.util.PoliceRankUtil;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 警员职级枚举测试类
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
public class PoliceRankEnumTest {

    @Test
    public void testGetByCode() {
        // 测试根据代码获取职级
        PoliceRankEnum rank = PoliceRankEnum.getByCode(PoliceRankCategory.LAW_ENFORCEMENT, 1);
        assertNotNull(rank);
        assertEquals("一级高级警长", rank.getName());
        assertEquals(PoliceRankCategory.LAW_ENFORCEMENT, rank.getCategory());

        // 测试技术职务
        PoliceRankEnum techRank = PoliceRankEnum.getByCode(PoliceRankCategory.TECHNICAL, 1);
        assertNotNull(techRank);
        assertEquals("一级主任", techRank.getName());
        assertEquals(PoliceRankCategory.TECHNICAL, techRank.getCategory());
    }

    @Test
    public void testGetByName() {
        // 测试根据名称获取职级
        PoliceRankEnum rank = PoliceRankEnum.getByName("一级高级警长");
        assertNotNull(rank);
        assertEquals(1, rank.getCode());
        assertEquals(PoliceRankCategory.LAW_ENFORCEMENT, rank.getCategory());
    }

    @Test
    public void testGetByCategory() {
        // 测试根据类别获取职级列表
        List<PoliceRankEnum> lawEnforcementRanks = PoliceRankEnum.getByCategory(PoliceRankCategory.LAW_ENFORCEMENT);
        assertFalse(lawEnforcementRanks.isEmpty());
        assertTrue(lawEnforcementRanks.stream().allMatch(rank -> rank.getCategory() == PoliceRankCategory.LAW_ENFORCEMENT));

        List<PoliceRankEnum> technicalRanks = PoliceRankEnum.getByCategory(PoliceRankCategory.TECHNICAL);
        assertFalse(technicalRanks.isEmpty());
        assertTrue(technicalRanks.stream().allMatch(rank -> rank.getCategory() == PoliceRankCategory.TECHNICAL));
    }

    @Test
    public void testIsSeniorRank() {
        // 测试高级警长判断
        PoliceRankEnum senior1 = PoliceRankEnum.getByCode(PoliceRankCategory.LAW_ENFORCEMENT, 1);
        assertTrue(senior1.isSeniorRank());

        PoliceRankEnum sergeant = PoliceRankEnum.getByCode(PoliceRankCategory.LAW_ENFORCEMENT, 5);
        assertFalse(sergeant.isSeniorRank());
    }

    @Test
    public void testPoliceRankUtil() {
        // 测试工具类
        assertTrue(PoliceRankUtil.isLawEnforcementRank(1));
        assertTrue(PoliceRankUtil.isTechnicalRank(1));
        assertTrue(PoliceRankUtil.isSeniorRank(1));
        assertFalse(PoliceRankUtil.isSeniorRank(5));

        assertEquals("一级高级警长", PoliceRankUtil.getRankNameByCode(1));
        assertEquals(PoliceRankCategory.LAW_ENFORCEMENT, PoliceRankUtil.getCategoryByCode(1));
    }

    @Test
    public void testCategoryMapping() {
        // 测试类别映射
        assertEquals("police_zj_group_one", PoliceRankCategory.LAW_ENFORCEMENT.getCategoryCode());
        assertEquals("police_zj_group_two", PoliceRankCategory.TECHNICAL.getCategoryCode());
        assertEquals("执法勤务警员职务晋升", PoliceRankCategory.LAW_ENFORCEMENT.getCategoryName());
        assertEquals("技术职务晋升", PoliceRankCategory.TECHNICAL.getCategoryName());
    }

    @Test
    public void testValidation() {
        // 测试职级代码验证
        assertTrue(PoliceRankUtil.isValidRankCode(1));
        assertTrue(PoliceRankUtil.isValidRankCode(100));
        assertTrue(PoliceRankUtil.isValidRankCode(101));
        assertFalse(PoliceRankUtil.isValidRankCode(999));
        assertFalse(PoliceRankUtil.isValidRankCode(null));
    }

    @Test
    public void testFullDescription() {
        // 测试完整描述
        PoliceRankEnum rank = PoliceRankEnum.getByCode(PoliceRankCategory.LAW_ENFORCEMENT, 1);
        String description = rank.getFullDescription();
        assertTrue(description.contains("执法勤务警员职务晋升"));
        assertTrue(description.contains("一级高级警长"));
        assertTrue(description.contains("(1)"));
    }

    @Test
    public void testGetAllCodes() {
        // 测试获取所有代码
        List<Integer> allCodes = PoliceRankEnum.getAllCodes();
        assertFalse(allCodes.isEmpty());
        assertTrue(allCodes.contains(1));
        assertTrue(allCodes.contains(100));
        assertTrue(allCodes.contains(101));
    }

    @Test
    public void testDirectorAndSupervisorRanks() {
        // 测试主任和主管系列
        assertTrue(PoliceRankUtil.isDirectorRank(1)); // 技术职务的1是一级主任
        assertTrue(PoliceRankUtil.isSupervisorRank(5)); // 技术职务的5是一级主管
        assertFalse(PoliceRankUtil.isDirectorRank(5)); // 5不是主任
        assertFalse(PoliceRankUtil.isSupervisorRank(1)); // 1不是主管
    }
}
