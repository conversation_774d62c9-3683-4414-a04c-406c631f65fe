package com.trs.police.profile.util.zhzg;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 泛型树构建方法使用示例
 */
public class GenericTreeExample {

    /**
     * 部门节点示例
     */
    public static class DepartmentNode implements TreeNode<DepartmentNode, Integer> {
        private Integer id;
        private Integer parentId;
        private List<DepartmentNode> children;
        private String name;
        private String code;

        public DepartmentNode(Integer id, Integer parentId, String name, String code) {
            this.id = id;
            this.parentId = parentId;
            this.name = name;
            this.code = code;
            this.children = new ArrayList<>();
        }

        @Override
        public Integer getId() {
            return id;
        }

        @Override
        public Integer getParentId() {
            return parentId;
        }

        @Override
        public List<DepartmentNode> getChildren() {
            return children;
        }

        @Override
        public void setChildren(List<DepartmentNode> children) {
            this.children = children;
        }

        public String getName() {
            return name;
        }

        public String getCode() {
            return code;
        }

        @Override
        public String toString() {
            return "Department{id=" + id + ", name='" + name + "', code='" + code + "', children=" + children.size() + "}";
        }
    }

    /**
     * 菜单节点示例
     */
    public static class MenuNode implements TreeNode<MenuNode, String> {
        private String id;
        private String parentId;
        private List<MenuNode> children;
        private String title;
        private String url;

        public MenuNode(String id, String parentId, String title, String url) {
            this.id = id;
            this.parentId = parentId;
            this.title = title;
            this.url = url;
            this.children = new ArrayList<>();
        }

        @Override
        public String getId() {
            return id;
        }

        @Override
        public String getParentId() {
            return parentId;
        }

        @Override
        public List<MenuNode> getChildren() {
            return children;
        }

        @Override
        public void setChildren(List<MenuNode> children) {
            this.children = children;
        }

        public String getTitle() {
            return title;
        }

        public String getUrl() {
            return url;
        }

        @Override
        public String toString() {
            return "Menu{id='" + id + "', title='" + title + "', url='" + url + "', children=" + children.size() + "}";
        }
    }

    public static void main(String[] args) {
        System.out.println("=== 泛型树构建方法使用示例 ===\n");

        // 示例1：构建部门树（Integer ID）
        System.out.println("1. 部门树示例（Integer ID）：");
        List<DepartmentNode> departments = Arrays.asList(
            new DepartmentNode(1, null, "公安局", "GA"),
            new DepartmentNode(2, null, "交警支队", "JJ"),
            new DepartmentNode(11, 1, "刑侦大队", "XZ"),
            new DepartmentNode(12, 1, "治安大队", "ZA"),
            new DepartmentNode(21, 2, "一大队", "YDD"),
            new DepartmentNode(22, 2, "二大队", "EDD"),
            new DepartmentNode(111, 11, "重案组", "ZAZ")
        );

        List<DepartmentNode> departmentTree = GenericTreeUtil.buildTree(departments);
        printDepartmentTree(departmentTree, 0);

        System.out.println("\n2. 菜单树示例（String ID）：");
        List<MenuNode> menus = Arrays.asList(
            new MenuNode("system", null, "系统管理", "/system"),
            new MenuNode("profile", null, "档案管理", "/profile"),
            new MenuNode("user", "system", "用户管理", "/system/user"),
            new MenuNode("role", "system", "角色管理", "/system/role"),
            new MenuNode("person", "profile", "人员档案", "/profile/person"),
            new MenuNode("score", "profile", "积分管理", "/profile/score"),
            new MenuNode("user-add", "user", "添加用户", "/system/user/add"),
            new MenuNode("user-edit", "user", "编辑用户", "/system/user/edit")
        );

        List<MenuNode> menuTree = GenericTreeUtil.buildTree(menus);
        printMenuTree(menuTree, 0);

        System.out.println("\n=== 使用说明 ===");
        System.out.println("1. 任何类只要实现 TreeNode<T, ID> 接口，就可以使用 buildTree() 方法构建树形结构");
        System.out.println("2. ID 类型可以是任意类型：Long、Integer、String 等");
        System.out.println("3. 方法会自动处理父子关系，构建完整的树形结构");
        System.out.println("4. 如果找不到父节点，会将该节点作为根节点处理");
        System.out.println("5. 保持了向后兼容性，原有的 buildRuleTree() 方法仍然可用");
    }

    private static void printDepartmentTree(List<DepartmentNode> nodes, int level) {
        String indent = "  ".repeat(level);
        for (DepartmentNode node : nodes) {
            System.out.println(indent + node);
            if (!node.getChildren().isEmpty()) {
                printDepartmentTree(node.getChildren(), level + 1);
            }
        }
    }

    private static void printMenuTree(List<MenuNode> nodes, int level) {
        String indent = "  ".repeat(level);
        for (MenuNode node : nodes) {
            System.out.println(indent + node);
            if (!node.getChildren().isEmpty()) {
                printMenuTree(node.getChildren(), level + 1);
            }
        }
    }
}
