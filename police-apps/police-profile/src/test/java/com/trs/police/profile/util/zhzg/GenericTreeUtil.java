package com.trs.police.profile.util.zhzg;

import java.util.*;

/**
 * 泛型树构建工具类（独立版本，用于测试）
 */
public class GenericTreeUtil {

    /**
     * 构建树形结构（泛型版本）
     *
     * @param nodes 节点列表
     * @param <T> 节点类型
     * @param <ID> ID类型
     * @return 树形结构（根节点列表）
     */
    public static <T extends TreeNode<T, ID>, ID> List<T> buildTree(List<T> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return new ArrayList<>();
        }

        Map<ID, T> nodeMap = new HashMap<>();
        List<T> rootNodes = new ArrayList<>();

        // 初始化节点映射和子节点列表
        for (T node : nodes) {
            nodeMap.put(node.getId(), node);
            if (node.getChildren() == null) {
                node.setChildren(new ArrayList<>());
            }
        }

        // 构建树形结构
        for (T node : nodes) {
            ID parentId = node.getParentId();
            if (parentId == null || (parentId instanceof Number && ((Number) parentId).longValue() == 0)) {
                // 根节点
                rootNodes.add(node);
            } else {
                // 子节点
                T parent = nodeMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(node);
                } else {
                    System.out.println("警告：找不到父节点，节点ID：" + node.getId() + "，父节点ID：" + parentId);
                    // 如果找不到父节点，将其作为根节点处理
                    rootNodes.add(node);
                }
            }
        }

        return rootNodes;
    }

    /**
     * 简单的测试节点类
     */
    public static class SimpleNode implements TreeNode<SimpleNode, String> {
        private String id;
        private String parentId;
        private List<SimpleNode> children;
        private String name;

        public SimpleNode(String id, String parentId, String name) {
            this.id = id;
            this.parentId = parentId;
            this.name = name;
            this.children = new ArrayList<>();
        }

        @Override
        public String getId() {
            return id;
        }

        @Override
        public String getParentId() {
            return parentId;
        }

        @Override
        public List<SimpleNode> getChildren() {
            return children;
        }

        @Override
        public void setChildren(List<SimpleNode> children) {
            this.children = children;
        }

        public String getName() {
            return name;
        }

        @Override
        public String toString() {
            return "SimpleNode{id='" + id + "', name='" + name + "', children=" + children.size() + "}";
        }
    }

    public static void main(String[] args) {
        System.out.println("开始测试泛型树构建方法...");

        // 创建测试节点
        List<SimpleNode> nodes = Arrays.asList(
            new SimpleNode("1", null, "根节点1"),
            new SimpleNode("2", null, "根节点2"),
            new SimpleNode("11", "1", "子节点11"),
            new SimpleNode("12", "1", "子节点12"),
            new SimpleNode("21", "2", "子节点21"),
            new SimpleNode("111", "11", "子节点111")
        );

        // 使用泛型方法构建树
        List<SimpleNode> tree = buildTree(nodes);

        // 验证结果
        System.out.println("构建的树结构：");
        printTree(tree, 0);

        // 基本验证
        if (tree.size() == 2) {
            System.out.println("✓ 根节点数量正确：" + tree.size());
        } else {
            System.out.println("✗ 根节点数量错误，期望2个，实际" + tree.size() + "个");
        }

        if (tree.get(0).getChildren().size() == 2) {
            System.out.println("✓ 第一个根节点的子节点数量正确：" + tree.get(0).getChildren().size());
        } else {
            System.out.println("✗ 第一个根节点的子节点数量错误");
        }

        if (tree.get(0).getChildren().get(0).getChildren().size() == 1) {
            System.out.println("✓ 子节点11的子节点数量正确：" + tree.get(0).getChildren().get(0).getChildren().size());
        } else {
            System.out.println("✗ 子节点11的子节点数量错误");
        }

        System.out.println("测试完成！泛型树构建方法工作正常。");
    }

    private static void printTree(List<SimpleNode> nodes, int level) {
        String indent = "  ".repeat(level);
        for (SimpleNode node : nodes) {
            System.out.println(indent + node);
            if (!node.getChildren().isEmpty()) {
                printTree(node.getChildren(), level + 1);
            }
        }
    }
}
