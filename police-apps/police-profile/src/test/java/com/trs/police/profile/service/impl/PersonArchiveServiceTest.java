package com.trs.police.profile.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.JsonUtils;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.profile.ProfileApp;
import com.trs.police.profile.domain.vo.TrackActivityTime;
import com.trs.police.profile.domain.vo.TrackHeatMap;
import com.trs.police.profile.domain.vo.TrackPointVO;
import com.trs.police.profile.domain.vo.TrackPointWithWarningIdVO;
import com.trs.police.profile.factory.TrajectoryQueryStrategyFactory;
import com.trs.police.profile.factory.strategy.trajectory.TrajectoryQueryStrategy;
import com.trs.police.profile.factory.strategy.trajectory.impl.MySqlTrajectoryQueryStrategyImpl;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.service.PersonArchiveService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/7 15:01
 */
@Slf4j
@SpringBootTest(classes = ProfileApp.class)
public class PersonArchiveServiceTest {
        @Resource
        private PersonArchiveService personArchiveService;

        @Resource
        private MySqlTrajectoryQueryStrategyImpl mysqlTrajectoryQueryStrategy;

        @Resource
        private PersonMapper personMapper;

        @Test
        public void testGetPersonTrackHeatMap() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"searchParams\":{\"searchField\":\"fullText\",\"searchValue\":\"\"},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"15\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"desc\"}}",
                                ListParamsRequest.class);
                List<TrackHeatMap> personTrackHeatMap = personArchiveService.getPersonTrackHeatMap(153L,
                                listParamsRequest);
                log.info("轨迹热力图查询结果为: {}", JsonUtils.toOptionalJson(personTrackHeatMap).get());
        }

        @Test
        public void testGetPersonTrackActivityTime() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"searchParams\":{\"searchField\":\"fullText\",\"searchValue\":null},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"1\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"desc\"}}",
                                ListParamsRequest.class);
                List<TrackActivityTime> personTrackActivityTime = personArchiveService.getPersonTrackActivityTime(127L,
                                listParamsRequest);
                log.info("查询活跃时段: {}", JsonUtils.toOptionalJson(personTrackActivityTime).get());
        }

        /**
         * 人员轨迹-轨迹趋势图表 最近7天
         */
        @Test
        public void testGetPersonTrendChartRecent7Day() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                        "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"7\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                        ListParamsRequest.class);
                List<TrackActivityTime> personTrendChart = personArchiveService.getPersonTrendChart(127L,
                        listParamsRequest);
                log.info("人员轨迹-轨迹趋势图表: {}", JsonUtils.toOptionalJson(personTrendChart).get());
        }

        /**
         * 人员轨迹-轨迹趋势图表 最近30天
         */
        @Test
        public void testGetPersonTrendChartRecent3Day() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"3\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                                ListParamsRequest.class);
                List<TrackActivityTime> personTrendChart = personArchiveService.getPersonTrendChart(127L,
                                listParamsRequest);
                log.info("人员轨迹-轨迹趋势图表: {}", JsonUtils.toOptionalJson(personTrendChart).get());
        }

        /**
         * 人员轨迹-轨迹趋势图表 最近一年
         */
        @Test
        public void testGetPersonTrendChartRecent1Year() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"3\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                                ListParamsRequest.class);
                List<TrackActivityTime> personTrendChart = personArchiveService.getPersonTrendChart(127L,
                                listParamsRequest);
                log.info("人员轨迹-轨迹趋势图表: {}", JsonUtils.toOptionalJson(personTrendChart).get());
        }

        /**
         * 人员轨迹-轨迹趋势图表 按天
         */
        @Test
        public void testGetPersonTrendChart() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"1\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                                ListParamsRequest.class);
                List<TrackActivityTime> personTrendChart = personArchiveService.getPersonTrendChart(127L,
                                listParamsRequest);
                log.info("人员轨迹-轨迹趋势图表: {}", JsonUtils.toOptionalJson(personTrendChart).get());
        }

        /**
         * 人员轨迹-轨迹趋势图表 最近一年
         */
        @Test
        public void testGetPersonTrendChartByMonth() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                        "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"5\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                        ListParamsRequest.class);
                List<TrackActivityTime> personTrendChart = personArchiveService.getPersonTrendChart(127L,
                        listParamsRequest);
                log.info("人员轨迹-轨迹趋势图表: {}", JsonUtils.toOptionalJson(personTrendChart).get());
        }

        /**
         * 人员轨迹-轨迹趋势图表 最近30天
         */
        @Test
        public void testGetPersonTrendChartRecent30Day() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                        "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"8\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                        ListParamsRequest.class);
                List<TrackActivityTime> personTrendChart = personArchiveService.getPersonTrendChart(127L,
                        listParamsRequest);
                log.info("人员轨迹-轨迹趋势图表: {}", JsonUtils.toOptionalJson(personTrendChart).get());
        }


        /**
         * 人员轨迹-轨迹趋势图表 今天
         */
        @Test
        public void testGetPersonTrendChartToday() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                        "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"1\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                        ListParamsRequest.class);
                List<TrackActivityTime> personTrendChart = personArchiveService.getPersonTrendChart(180L,
                        listParamsRequest);
                log.info("人员轨迹-轨迹趋势图表: {}", JsonUtils.toOptionalJson(personTrendChart).get());
        }
        @Test
        public void testESGetPersonTrackPage() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"searchParams\":{\"searchField\":\"fullText\",\"searchValue\":\"\"},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"1\"}}],\"sortParams\":{\"sortField\":\"hdsj\",\"sortDirection\":\"desc\"}}",
                                ListParamsRequest.class);
                TrajectoryQueryStrategy strategy = TrajectoryQueryStrategyFactory
                                .getStrategy(TrajectoryQueryStrategyFactory.ES);
                PageResult<TrackPointVO> personTrackPage = strategy.getPersonTrackPage(153L, listParamsRequest);
                log.info("ES-人员轨迹: {}", JsonUtils.toOptionalJson(personTrackPage).get());
        }

        /**
         * 人员轨迹-轨迹趋势列表
         */
        @Test
        public void testGetPersonTrendList() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"1\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                                ListParamsRequest.class);
                List<TrackActivityTime> personTrendChart = personArchiveService.getPersonTrendChart(153L,
                                listParamsRequest);
                log.info("人员轨迹-轨迹趋势图表: {}", JsonUtils.toOptionalJson(personTrendChart).get());
        }

        /**
         * 查询列表
         */
        @Test
        public void testSelectTrackPage() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":2,\"pageSize\":20},\"searchParams\":{\"searchField\":\"fullText\",\"searchValue\":\"\"},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"15\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"desc\"}}",
                                ListParamsRequest.class);
                Page<TrackPointWithWarningIdVO> trackPointWithWarningIdVOPage = personMapper.selectTrackPage(153L,
                                listParamsRequest.getFilterParams(), listParamsRequest.getSearchParams(),
                                listParamsRequest.getSortParams(), listParamsRequest.getPageParams().toPage());
                log.info("查询人员轨迹列表数据: {}", JsonUtils.toOptionalJson(trackPointWithWarningIdVOPage).get());
        }

        /**
         * 查询列表
         */
        @Test
        public void testMySqlQueryTrackPage() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":2,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"1\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                                ListParamsRequest.class);
                PageResult<TrackPointVO> personTrackPage = mysqlTrajectoryQueryStrategy.getPersonTrackPage(218L,
                                listParamsRequest);
                log.info("查询人员轨迹列表数据: {}", JsonUtils.toOptionalJson(personTrackPage).get());
        }

        /**
         * 查询人员轨迹列表
         */
        @Test
        public void testQueryTrackPage() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":2,\"pageSize\":20},\"searchParams\":{\"searchField\":\"fullText\",\"searchValue\":\"\"},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"1\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"desc\"}}",
                                ListParamsRequest.class);
                PageResult<TrackPointVO> personTrackList = personArchiveService.getPersonTrackList(218L,
                                listParamsRequest);
                ;
                log.info("查询人员轨迹列表数据: {}", JsonUtils.toOptionalJson(personTrackList).get());
        }

        /**
         * 查询人员轨迹列表
         */
        @Test
        public void testQueryTrackPageRecent30Day() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                                "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"3\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                                ListParamsRequest.class);
                PageResult<TrackPointVO> personTrackList = personArchiveService.getPersonTrackList(127L,
                                listParamsRequest);
                ;
                log.info("查询人员轨迹列表数据: {}", JsonUtils.toOptionalJson(personTrackList).get());
        }

        /**
         * 查询人员轨迹列表
         */
        @Test
        public void testQueryTrackPageRecent7Day() {
                ListParamsRequest listParamsRequest = JSONObject.parseObject(
                        "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"2\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                        ListParamsRequest.class);
                PageResult<TrackPointVO> personTrackList = personArchiveService.getPersonTrackList(127L,
                        listParamsRequest);
                ;
                log.info("查询人员轨迹列表数据: {}", JsonUtils.toOptionalJson(personTrackList).get());
        }

        @Test
        public void testGetRecent7DayScope() {
                ListParamsRequest request = JSONObject.parseObject(
                        "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"7\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                        ListParamsRequest.class);
                final TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(), "activeTime",
                        TimeParams.class);
                final String beginStr = TimeUtil.localDateTimeToString(timeParams.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
                final String endStr = TimeUtil.localDateTimeToString(timeParams.getEndTime(), "yyyy-MM-dd HH:mm:ss");
                log.info("beginStr is {} and endStr is {}", beginStr, endStr);
        }

        @Test
        public void testGetRecent1yearScope() {
                ListParamsRequest request = JSONObject.parseObject(
                        "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"filterParams\":[{\"key\":\"activeTime\",\"type\":\"timeParams\",\"value\":{\"range\":\"10\"}}],\"sortParams\":{\"sortField\":\"time\",\"sortDirection\":\"\"}}",
                        ListParamsRequest.class);
                final TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(), "activeTime",
                        TimeParams.class);
                final String beginStr = TimeUtil.localDateTimeToString(timeParams.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
                final String endStr = TimeUtil.localDateTimeToString(timeParams.getEndTime(), "yyyy-MM-dd HH:mm:ss");
                log.info("beginStr is {} and endStr is {}", beginStr, endStr);
        }

}
