package com.trs.police.profile.service.zhzg;

import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreCalculateRequestDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgScoreResultVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 智慧政工积分计算服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class ZhzgScoreCalculateServiceTest {

    @Resource
    private ZhzgScoreCalculateService scoreCalculateService;

    private ZhzgPersonArchiveDTO personArchive;
    private List<ZhzgScoreRuleDTO> rules;

    @BeforeEach
    void setUp() {
        // 准备测试数据 - 人员档案
        personArchive = new ZhzgPersonArchiveDTO();
        personArchive.setId(1L);
        personArchive.setName("张三");
        personArchive.setJoinJobDate(LocalDate.of(2020, 1, 1)); // 工作4年
        personArchive.setEducations(Arrays.asList("本科学历", "计算机专业"));
        personArchive.setAwards(Arrays.asList("三等功1次", "嘉奖2次"));
        personArchive.setViolations(Arrays.asList("迟到1次"));

        // 准备测试数据 - 积分规则（树形结构）
        rules = Arrays.asList(
                // 根规则1：基础素质（上限50分）
                createRule(1L, "基础分值", null, 100, false, "BASE_QUALITY", true),
                // 子规则1.1：工作年限（叶子节点）
                createRule(11L, "工作年限", 1L, 10, true, "WORK_YEARS", true),
                // 子规则1.2：教育经历（叶子节点）
                createRule(12L, "学历学位", 1L, 3, true, "EDUCATION", true),
                
                // 根规则2：表现评价（上限30分）
                createRule(2L, "表现评价", null, 30, false, "PERFORMANCE", true),
                // 子规则2.1：立功受奖（叶子节点）
                createRule(21L, "立功受奖", 2L, 25, true, "AWARDS", true),
                // 子规则2.2：违纪违规（叶子节点）
                createRule(22L, "违纪违规", 2L, -50, true, "VIOLATION", true)
        );
    }

    @Test
    void testCalculateScore_Success() {
        // 准备请求
        ZhzgScoreCalculateRequestDTO request = new ZhzgScoreCalculateRequestDTO();
        request.setPersonArchive(personArchive);
        request.setRules(rules);
        request.setIncludeDetails(true);

        // 执行计算
        ZhzgScoreResultVO result = scoreCalculateService.calculateScore(request);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertNotNull(result.getTotalScore());
        assertTrue(result.getTotalScore() > 0);
        assertEquals("张三", result.getPersonName());
        assertEquals(1L, result.getPersonId());
        assertNotNull(result.getScoreLevel());
        assertNotNull(result.getRuleScoreDetails());
        assertFalse(result.getRuleScoreDetails().isEmpty());

        // 验证详细计算结果
        List<ZhzgRuleScoreDetailVO> details = result.getRuleScoreDetails();
        
        // 应该包含所有规则的计算结果
        assertTrue(details.size() >= 6); // 6个规则（2个父规则 + 4个子规则）
        
        // 验证工作年限计算
        ZhzgRuleScoreDetailVO workYearsDetail = findDetailByRuleName(details, "工作年限");
        assertNotNull(workYearsDetail);
        assertTrue(workYearsDetail.getSuccess());
        assertTrue(workYearsDetail.getIsHit());
        assertTrue(workYearsDetail.getScore() > 0);
        
        // 验证教育经历计算
        ZhzgRuleScoreDetailVO educationDetail = findDetailByRuleName(details, "教育经历");
        assertNotNull(educationDetail);
        assertTrue(educationDetail.getSuccess());
        assertTrue(educationDetail.getIsHit());
        assertEquals(10.0, educationDetail.getScore()); // 本科10分
        
        // 验证立功受奖计算
        ZhzgRuleScoreDetailVO awardsDetail = findDetailByRuleName(details, "立功受奖");
        assertNotNull(awardsDetail);
        assertTrue(awardsDetail.getSuccess());
        assertTrue(awardsDetail.getIsHit());
        assertEquals(10.0, awardsDetail.getScore()); // 2条记录 × 5分 = 10分
        
        // 验证违纪违规计算
        ZhzgRuleScoreDetailVO violationDetail = findDetailByRuleName(details, "违纪违规");
        assertNotNull(violationDetail);
        assertTrue(violationDetail.getSuccess());
        assertTrue(violationDetail.getIsHit());
        assertEquals(-10.0, violationDetail.getScore()); // 1条记录 × -10分 = -10分

        System.out.println("积分计算测试通过！");
        System.out.println("总分：" + result.getTotalScore());
        System.out.println("积分等级：" + result.getScoreLevel());
        System.out.println("计算说明：" + result.getDescription());
    }

    @Test
    void testCalculateScore_EmptyRules() {
        ZhzgScoreCalculateRequestDTO request = new ZhzgScoreCalculateRequestDTO();
        request.setPersonArchive(personArchive);
        request.setRules(Arrays.asList());

        ZhzgScoreResultVO result = scoreCalculateService.calculateScore(request);

        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getErrorMessage());
    }

    @Test
    void testCalculateScore_NullPersonArchive() {
        ZhzgScoreCalculateRequestDTO request = new ZhzgScoreCalculateRequestDTO();
        request.setPersonArchive(null);
        request.setRules(rules);

        ZhzgScoreResultVO result = scoreCalculateService.calculateScore(request);

        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getErrorMessage());
    }

    /**
     * 创建积分规则
     */
    private ZhzgScoreRuleDTO createRule(Long id, String name, Long parentId, Integer score, 
                                       Boolean isLeaf, String ruleType, Boolean enabled) {
        ZhzgScoreRuleDTO rule = new ZhzgScoreRuleDTO();
        rule.setId(id);
        rule.setName(name);
        rule.setParentId(parentId);
        rule.setScore(score);
        rule.setIsLeaf(isLeaf);
        rule.setRuleType(ruleType);
        rule.setEnabled(enabled);
        rule.setDescription(name + "积分规则");
        return rule;
    }

    /**
     * 根据规则名称查找详细结果
     */
    private ZhzgRuleScoreDetailVO findDetailByRuleName(List<ZhzgRuleScoreDetailVO> details, String ruleName) {
        return details.stream()
                .filter(detail -> ruleName.equals(detail.getRuleName()))
                .findFirst()
                .orElse(null);
    }

}
