package com.trs.police.control.test;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.expression.Expression;
import com.trs.police.control.ControlApp;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjDTO;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEsEntity;
import com.trs.police.control.mapper.WarningFkrxyjMapper;
import com.trs.police.control.repository.FkWarningRepository;
import com.trs.police.control.service.WarningProcessService;
import com.trs.police.control.service.warning.FkWarningUpdater;
import com.trs.police.control.service.warning.JjWarningService;
import com.trs.police.control.service.warning.MgqyWarningService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@SpringBootTest(classes = ControlApp.class)
public class FkrxTest {

    String json = "{\n" +
            "    \"age\": 0,\n" +
            "    \"alertType\": \"alert\",\n" +
            "    \"captureAddress\": \"captureAddress\",\n" +
            "    \"captureTime\": \"2025-04-26 07:23:41\",\n" +
            "    \"date\": \"2025-04-26 07:23:41\",\n" +
            "    \"deviceCode\": \"50000000000000000000\",\n" +
            "    \"facePhoto\": \"http://baidu.com/ngx\",\n" +
            "    \"facePosition\": \"{\\\"end\\\":{\\\"x\\\":1085,\\\"y\\\":999},\\\"height\\\":1440,\\\"start\\\":{\\\"x\\\":847,\\\"y\\\":767},\\\"width\\\":2560}\",\n" +
            "    \"group\": \"中和pcs\",\n" +
            "    \"idCard\": \"511323199708150743\",\n" +
            "    \"latitude\": \"30.552973\",\n" +
            "    \"libName\": \"拓尔思bk\",\n" +
            "    \"longitude\": \"104.087204\",\n" +
            "    \"photo\": \"http://baidu.com/ngx\",\n" +
            "    \"sex\": \"男\",\n" +
            "    \"similarity\": \"0.96845376\",\n" +
            "    \"suspectBirth\": \"1972-11-25\",\n" +
            "    \"suspectName\": \"8a5d8bb343400d4a90ac5a086d5c2b6c\",\n" +
            "    \"suspectPhoto\": \"http://baidu.com/ngx\",\n" +
            "    \"suspectSex\": \"男\",\n" +
            "    \"tarLibName\": \"拓尔思bk\"\n" +
            "}";


    @Autowired
    private WarningProcessService warningProcessService;

    @SpyBean
    private WarningFkrxyjMapper warningFkrxyjMapper;

    @Autowired
    private FkWarningUpdater fkWarningUpdater;

    @Autowired
    private JjWarningService jjWarningService;

    @SpyBean
    private FkWarningRepository fkWarningRepository;

    @Autowired
    private MgqyWarningService mgqyWarningService;

    @Test
    public void scrqTest() throws Exception {
        Mockito.when(warningFkrxyjMapper.getCountDayOfWeeks(Mockito.any(LocalDateTime.class), Mockito.anyString()))
                .thenReturn(5L);
        WarningFkrxyjDTO dto = JSON.parseObject(json, WarningFkrxyjDTO.class);
        warningProcessService.receiveFkrxyjWarningMessage(dto);
    }


    @Test
    public void rqntTest() throws Exception {
        WarningFkrxyjDTO dto = JSON.parseObject(json, WarningFkrxyjDTO.class);
        warningProcessService.receiveFkrxyjWarningMessage(dto);
    }

    // 责任pcs测试
    @Test
    public void zrpacTest() {
        WarningFkrxyjEntity entity = warningFkrxyjMapper.selectById(460L);
         fkWarningUpdater.updatePerson(entity);
    }

    // 聚集测试
    @Test
    public void jjTest() {
        String[] s = null;
        List<WarningFkrxyjEsEntity> list = new ArrayList<>();
        list.add(newEntity("1"));
        list.add(newEntity("1"));
        list.add(newEntity("1"));
        list.add(newEntity("2"));
        list.add(newEntity("2"));
        list.add(newEntity("2"));
        list.add(newEntity("3"));
        list.add(newEntity("3"));
        list.add(newEntity("3"));
        list.add(newEntity("511323199708150743"));
        list.add(newEntity("511323199708150743"));
        list.add(newEntity("511323199708150743"));
        WarningFkrxyjEntity entity = warningFkrxyjMapper.selectById(460L);
        Mockito.when(fkWarningRepository.findList(Mockito.any(Expression.class), Mockito.any(String[].class)))
                        .thenReturn(list);
        List<WarningFkrxyjEntity> jjList = jjWarningService.jjList(Arrays.asList(entity));
        jjList.forEach(fkWarningUpdater::update);
        jjList.forEach(warningFkrxyjMapper::insert);

    }

    @Test
    public void mgqyTest() {
        WarningFkrxyjEntity entity = warningFkrxyjMapper.selectById(460L);
        Boolean mgqy = mgqyWarningService.isMgqy(entity);

    }

    private WarningFkrxyjEsEntity newEntity(String idCard) {
        WarningFkrxyjEsEntity en = new WarningFkrxyjEsEntity();
        en.setIdCard(idCard);
        return en;
    }

}
