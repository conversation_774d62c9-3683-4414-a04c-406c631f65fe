package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.control.domain.entity.warning.WarningNotifyEntity;
import com.trs.police.control.helper.WarningListVoHelper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 预警表(Warning)持久层
 *
 * <AUTHOR>
 * @date 2022-08-11 14:04:37
 */
@Mapper
public interface WarningNotifyMapper extends BaseMapper<WarningNotifyEntity> {

    /**
     * 查询用户和预警的关联
     *
     * @param warningId 预警id
     * @param userId    用户id
     * @param deptId    部门id
     * @return 关联
     */
    @Select("select * from t_warning_notify where warning_id = #{warningId} and user_id = #{userId} and dept_id = #{deptId}")
    WarningNotifyEntity selectByWarningIdAndUser(@Param("warningId") Long warningId, @Param("userId") Long userId,
        @Param("deptId") Long deptId);

    /**
     * 查询用户和预警的关联
     *
     * @param warningIds 预警id
     * @param userId     用户id
     * @param deptId     部门id
     * @return 关联
     */
    @Select({"<script>",
            "select * from t_warning_notify where warning_id in ",
            "<foreach collection='warningIds' item='warningId' open='(' separator=',' close=')'>",
            "#{warningId}",
            "</foreach>",
            " and user_id = #{userId} and dept_id = #{deptId}",
            "</script>"})
    List<WarningNotifyEntity> selectByWarningIdListAndUser(@Param("warningIds") List<Long> warningIds, @Param("userId") Long userId,
                                                 @Param("deptId") Long deptId);

    /**
     * 已读用户和预警的关联
     *
     * @param warningId 预警id
     * @param userId    用户id
     * @param deptId    部门id
     */
    @Update("update t_warning_notify set is_read = 1 where warning_id = #{warningId} and user_id = #{userId} and dept_id = #{deptId}")
    void updateByWarningIdAndUser(@Param("warningId") Long warningId, @Param("userId") Long userId,
        @Param("deptId") Long deptId);

    /**
     * 统计未读预警
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return 预警id
     */
    @Select("select distinct warning_id from t_warning_notify where user_id = #{userId} and dept_id = #{deptId} and is_read = 0")
    List<Long> selectUnreadWarning(@Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 根据预警id 获取所有通知人员
     *
     * @param warningId 预警id
     * @return {@link  WarningNotifyEntity}
     */
    @Select("select * from t_warning_notify where warning_id=#{warningId}")
    List<WarningNotifyEntity> selectByWarningId(@Param("warningId") Long warningId);
    /**
     * 根据预警id 获取所有通知人员name
     *
     * @param warningId 预警id
     * @return {@link  WarningNotifyEntity}
     */
    @Select("select real_name from t_user where id in (select user_id from t_warning_notify where warning_id=#{warningId} and notify_type=1)")
    List<String> getNotifyPersonByWarningId(@Param("warningId") Long warningId);
    /**
     * 根据预警id 获取所有通知人员name
     *
     * @param warningIds 预警id
     * @return {@link  WarningListVoHelper.NotifyPerson}
     */
    @Select({
            "<script>",
            "select wn.warning_id as warningId, real_name as notifyPerson",
            "from t_user u left join t_warning_notify wn on u.id = wn.user_id ",
            "<where>",
            "<if test='warningIds != null and warningIds.size() != 0'>",
            "AND wn.warning_id in ",
            "<foreach collection='warningIds' item='warningId' open='(' separator=',' close=')'>",
            "#{warningId}",
            "</foreach>",
            "</if>",
            " and wn.notify_type=1",
            "</where>",
            "</script>"
    })
    List<WarningListVoHelper.NotifyPerson> getNotifyPersonByWarningIds(@Param("warningIds") List<Long> warningIds);

    /**
     * 根据预警id 获取所有通知部门name
     *
     * @param warningId 预警id
     * @return {@link  WarningNotifyEntity}
     */
    @Select("select distinct short_name from t_dept where id in (select DISTINCT(dept_id) from t_warning_notify where warning_id=#{warningId} and notify_type!=1)")
    List<String> getNotifyDeptByWarningId(@Param("warningId") Long warningId);
    /**
     * 根据预警id 获取所有通知部门name
     *
     * @param warningIds 预警id
     * @return {@link  WarningListVoHelper.NotifyDept}
     */
    @Select({
            "<script>",
            "select distinct wn.warning_id as warningId, short_name as notifyDept ",
            "from t_dept d left join t_warning_notify wn on d.id = wn.dept_id ",
            "where wn.warning_id in ",
            "<foreach item='warningId' collection='warningIds' open='(' separator=',' close=')'>",
            "#{warningId}",
            "</foreach>",
            "and wn.notify_type != 1",
            "</script>"
    })
    List<WarningListVoHelper.NotifyDept> getNotifyDeptByWarningIds(@Param("warningIds") List<Long> warningIds);
}