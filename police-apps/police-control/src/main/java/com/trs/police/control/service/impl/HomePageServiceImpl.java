package com.trs.police.control.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.constant.enums.WarningStatusEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.dto.WarningModelVO;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.mapper.CommonMapper;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.params.TimePoint;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.utils.VoParameterConstructor;
import com.trs.police.common.core.vo.*;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.constant.MonitorConstant;
import com.trs.police.control.domain.builder.WarningListBuilder;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.vo.CameraVO;
import com.trs.police.control.domain.vo.HaiKanCamerasDeviceInfoVO;
import com.trs.police.control.domain.vo.HaiKanCamerasInfoVO;
import com.trs.police.control.domain.vo.HaiKanNodesRootVO;
import com.trs.police.control.domain.vo.homepage.LineChartVO;
import com.trs.police.control.domain.vo.homepage.LineVO;
import com.trs.police.control.domain.vo.homepage.*;
import com.trs.police.control.domain.vo.warning.WarningListVO;
import com.trs.police.control.mapper.*;
import com.trs.police.control.properties.WarningLevelDisplayProperties;
import com.trs.police.control.service.HkService;
import com.trs.police.control.service.HomePageService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 10/7/2023 下午1:59
 */
@Service
@Slf4j
public class HomePageServiceImpl implements HomePageService {

    @Resource
    private HomePageMapper homePageMapper;
    @Resource
    private MonitorMapper monitorMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private ProfileService profileService;
    @Resource
    private WarningTrackMapper warningTrackMapper;
    @Resource
    private MonitorWarningModelMapper monitorWarningModelMapper;
    @Resource
    private WarningProcessMapper warningProcessMapper;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private SourceMapper sourceMapper;

    @Autowired
    private HkServiceImpl hkService;

    @Autowired
    private DhServiceImpl dhService;

    @Autowired
    private TwServiceImpl twService;

    private Map<String,HkService> map;

    @Autowired
    private WarningLevelDisplayProperties warningLevelDisplayProperties;

    @Override
    public ControlCountVO controlCount() {
        ControlCountVO vo = new ControlCountVO();
        vo.setMonitorCount(monitorMapper.getMonitorPersonCount());
        return vo;
    }

    /**
     * 初始化
     */
    @PostConstruct
    public void init(){
        map = new HashMap<>();
        map.put("hk",hkService);
        map.put("dh",dhService);
        map.put("tw",twService);
    }

    @Override
    public WarningHandleStatisticVO getWarningHandleStatistic(ListParamsRequest request, Long level) {
        List<KeyValueTypeVO> permission = permissionService.buildParamsByPermission();
        request.getFilterParams().addAll(permission);
        request.getFilterParams().add(new KeyValueTypeVO("level", level, "long"));
        final List<KeyValueVO> disposalStatistic = homePageMapper.getWarningHandleStatistic(request);
        final List<KeyValueVO> levelStatistic = homePageMapper.getWarningLevelStatistic(request);
        final WarningHandleStatisticVO vo = new WarningHandleStatisticVO();
        vo.setAllCount(
                disposalStatistic.stream().map(item -> Integer.valueOf(item.getValue())).reduce(0, Integer::sum));
        vo.setBlueCount(levelStatistic.stream()
                .filter(item -> MonitorLevelEnum.BLUE.getCode().equals(Integer.valueOf(item.getKey())))
                .map(item -> Integer.valueOf(item.getValue())).findFirst().orElse(0));
        vo.setRedCount(levelStatistic.stream()
                .filter(item -> MonitorLevelEnum.RED.getCode().equals(Integer.valueOf(item.getKey())))
                .map(item -> Integer.valueOf(item.getValue())).findFirst().orElse(0));
        vo.setYellowCount(levelStatistic.stream()
                .filter(item -> MonitorLevelEnum.YELLOW.getCode().equals(Integer.valueOf(item.getKey())))
                .map(item -> Integer.valueOf(item.getValue())).findFirst().orElse(0));
        vo.setOrangeCount(levelStatistic.stream()
                .filter(item -> MonitorLevelEnum.ORANGE.getCode().equals(Integer.valueOf(item.getKey())))
                .map(item -> Integer.valueOf(item.getValue())).findFirst().orElse(0));
        String levelWay = BeanFactoryHolder.getEnv().getProperty("com.trs.police.warning.levelWay", "1");
        if ("2".equals(levelWay)) {
            // 1 采用红橙黄蓝 2 采用省厅一级，二级，三级
            vo.setBlueCount(vo.getBlueCount() + vo.getYellowCount());
            vo.setYellowCount(0);
        }
        vo.setSignCount(disposalStatistic.stream()
                .filter(item -> Integer.parseInt(item.getKey()) >= WarningStatusEnum.SIGN_FINISH.getCode())
                .map(item -> Integer.valueOf(item.getValue())).reduce(0, Integer::sum));
        vo.setFeedbackCount(disposalStatistic.stream()
                .filter(item -> Integer.parseInt(item.getKey()) >= WarningStatusEnum.REPLY_FINISH.getCode())
                .map(item -> Integer.valueOf(item.getValue())).reduce(0, Integer::sum));
        vo.setDoneCount(disposalStatistic.stream()
                .filter(item -> Integer.parseInt(item.getKey()) >= WarningStatusEnum.PROCESS_FINISH.getCode())
                .map(item -> Integer.valueOf(item.getValue())).reduce(0, Integer::sum));

        return vo;
    }

    @Override
    public PageResult<WarningHomePageListVO> getWarningList(ListParamsRequest request) {
        List<KeyValueTypeVO> dataPermission = permissionService.buildParamsByPermission();
        request.getFilterParams().addAll(dataPermission);
        PageParams pageParams = request.getPageParams();
        Page<WarningHomePageListVO> page = homePageMapper.getWarningPageList(request,
                pageParams.toPage());
        handleWarningLevel(page.getRecords());
        return PageResult.of(page.getRecords(), pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    void handleWarningLevel(List<WarningHomePageListVO> warningHomePageListVos) {
        warningHomePageListVos.forEach(r -> r.setWarningLevel(warningLevelDisplayProperties.getYsNameAndCloudNameMap().getOrDefault(r.getWarningLevel(), r.getWarningLevel())));
    }


    @Override
    public PageResult<MonitorPersonVO> getMonitorPerson(ListParamsRequest request) {
        List<KeyValueTypeVO> permission = permissionService.buildParamsByPermission();
        request.getFilterParams().addAll(permission);
        PageParams pageParams = request.getPageParams();
        Page<MonitorPersonVO> page = homePageMapper.getMonitorPerson(request, pageParams.toPage());


        // 获取构造vo的数据
        List<Long> personIds = page.getRecords()
                .stream()
                .map(MonitorPersonVO::getPersonId)
                .distinct()
                .collect(Collectors.toList());
        // 获取人员列表
        List<PersonVO> personList = CollectionUtils.isEmpty(personIds)
                ? new ArrayList<>()
                : profileService.getPersonByIds(personIds);
        // 管控人员
        List<IdNameVO> monitorUsersByIds = CollectionUtils.isEmpty(personIds) ? new ArrayList<>() : homePageMapper.getPersonMonitorUsersByIds(personIds);
        // 最后的预警信息
        List<TrackVO> lastTrackBatch = CollectionUtils.isEmpty(personList) ? new ArrayList<>() : homePageMapper.getLastTrackBatch(personIds);

        // 构造vo
        page.getRecords().forEach(vo -> vo.setLastTrack(TrackVO.ofDefaultValue()));
        VoParameterConstructor.of(page.getRecords())
                .singleValueMatcher(vs -> personList, (personVO, person) -> Objects.equals(personVO.getPersonId(), person.getId()))
                .consumer((personVO, person) -> {
                    personVO.setName(person.getName());
                    personVO.setIdNumber(person.getCertificateNumber());
                    personVO.setImgs(List.of(FileInfoVO.of(person.getCertificateNumber())));
                })
                .multiValueMatcher(
                        vs -> monitorUsersByIds,
                        (personVO, monitorUsersById) -> Objects.equals(personVO.getPersonId(), monitorUsersById.getId()))
                .consumer((personVO, monitorUsersById) -> {
                    personVO.setMonitorUserName(monitorUsersById.stream().map(IdNameVO::getUuid).collect(Collectors.toList()));
                    personVO.setMonitorDeptName(monitorUsersById.stream().map(IdNameVO::getName).collect(Collectors.toList()));
                })
                .singleValueMatcher(
                        vs -> lastTrackBatch,
                        (personVO, lastTrack) -> Objects.equals(personVO.getPersonId(), lastTrack.getPersonId())
                )
                .consumer(MonitorPersonVO::setLastTrack)
                .build();
        return PageResult.of(page.getRecords(), pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }


    @Override
    public PersonInfoVO getPersonInfo(Long personId) {
        return homePageMapper.getPersonInfo(personId);
    }

    @Override
    public PageResult<PersonWarningListVO> getPersonWarningList(Long personId, ListParamsRequest request) {
        PageParams pageParams = request.getPageParams();

        Page<WarningEntity> page = homePageMapper.getPersonWarningList(personId, request,
                pageParams.toPage());
        List<PersonWarningListVO> result = page.getRecords().stream().map(this::getPersonWarningListVO)
                .collect(Collectors.toList());

        return PageResult.of(result, pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }


    private PersonWarningListVO getPersonWarningListVO(WarningEntity e) {
        PersonWarningListVO warningVO = new PersonWarningListVO();
        warningVO.setId(e.getId());
        warningVO.setLevel("- -");
        if (Objects.nonNull(e.getWarningLevel())) {
            warningVO.setLevel(e.getWarningLevel().getName());
        }
        warningVO.setContent("- -");
        SourceEntity sourceEntity = sourceMapper.selectByWarningId(e.getId());
        if (Objects.nonNull(sourceEntity)) {
            warningVO.setContent(StringUtil.blank2Default(sourceEntity.getName()));
        }
        String models = monitorWarningModelMapper.getByModelIds(e.getModelId()).stream()
                .map(WarningModelVO::getName).collect(Collectors.joining("、"));
        warningVO.setModel(models);

        WarningProcessEntity done = warningProcessMapper.getWarningDone(e.getId()).orElse(null);
        warningVO.setStatus(Objects.isNull(done) ? "处置中" : "已完结");
        String sourceType = String.join("、", warningTrackMapper.getWarningSourceType(e.getId()));
        warningVO.setSourceType(StringUtil.blank2Default(sourceType));
        warningVO.setAddress(StringUtil.blank2Default(e.getActivityAddress()));
        warningVO.setTime(e.getWarningTime());

        return warningVO;
    }


    @Override
    public List<KeyValueVO> getPersonWarningCount(Long personId, ListParamsRequest request) {

        List<KeyValueVO> result = homePageMapper.getPersonWarningCount(personId, request);

        LinkedList<KeyValueVO> collect = Arrays.stream(MonitorLevelEnum.values()).map(e -> {
            String count = KeyValueVO.get(result, e.getCode().toString());
            return new KeyValueVO(e.getName() + "色", StringUtil.blank2Target(count, "0"));
        }).collect(Collectors.toCollection(LinkedList::new));

        int sum = collect.stream().mapToInt(e -> Integer.parseInt(e.getValue())).sum();
        collect.addFirst(new KeyValueVO("全部", String.valueOf(sum)));

        return collect;
    }

    @Override
    public List<WarningHomePageListVO> getWarningMapData(ListParamsRequest request) {
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        List<WarningHomePageListVO> warnMapData = homePageMapper.getWarnMapData(request);
        handleWarningLevel(warnMapData);
        return warnMapData;
    }

    @Override
    public List<PersonMapVO> getPersonMapData(ListParamsRequest request) {
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        return homePageMapper.getPersonMapData(request);
    }

    @Override
    public List<KeyValueVO> getMonitorStatistics(ListParamsRequest request) {
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        return homePageMapper.getMonitorStatistics(request);

    }

    @Override
    public List<HeatMapVO> getHeatMapData(ListParamsRequest request) {
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        return homePageMapper.getHeatMapData(request);
    }

    @Override
    public List<CodeNameCountVO> getLevelStatistics(ListParamsRequest request) {
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        List<KeyValueVO> levelStatistics = homePageMapper.getLevelStatistics(request);

        return Arrays.stream(MonitorLevelEnum.values()).map(e -> {
            String countString = KeyValueVO.get(levelStatistics, e.getCode().toString());
            int count = StringUtils.isBlank(countString) ? 0 : Integer.parseInt(countString);
            return new CodeNameCountVO(e.getCode().toString(), e.getName() + "色", count);
        }).collect(Collectors.toList());
    }

    @Override
    public List<CodeNameCountVO> getLabelStatistics(ListParamsRequest request) {
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        List<KeyValueTypeVO> topLevelPersonLabel = commonMapper.getTopLevelPersonLabel();
        return homePageMapper.getLabelStatistics(topLevelPersonLabel, request);
    }

    @Override
    public List<PlaceCountVO> getPlaceCount(String type, ListParamsRequest request) {
        return homePageMapper.getPlaceCount(type, request.getFilterParams());
    }

    @Override
    public PageResult<WarningListVO> getWarningProcessList(ListParamsRequest request) {
        PageParams pageParams = request.getPageParams();
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        Page<WarningProcessEntity> page = warningProcessMapper.selectWarningProcessList(request, pageParams.toPage());
        return PageResult.of(
                page.getRecords().parallelStream().map(WarningListVO::processEntityToHomePageVo)
                        .collect(Collectors.toList()),
                pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    @Override
    public LineChartVO getWarningTrend(ListParamsRequest request) {
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        KeyValueTypeVO timeRange = request.getFilterParams().stream().filter(e -> "timeRange".equals(e.getKey()))
                .findFirst().orElse(null);
        if (Objects.isNull(timeRange)) {
            return null;
        }
        TimeParams timeParams = (TimeParams) timeRange.getValue();
        if ("0".equals(timeParams.getRange())) {
            timeParams.setRange("5");
        }
        List<LineVO> warningLevelLineVos = List.of(
                new LineVO(MonitorLevelEnum.RED),
                new LineVO(MonitorLevelEnum.ORANGE),
                new LineVO(MonitorLevelEnum.YELLOW),
                new LineVO(MonitorLevelEnum.BLUE),
                new LineVO("0", "总数", new ArrayList<>())
        );
        List<TimePoint> timePoints = TimePoint.of(timeParams);
        List<KeyValueTypeVO> xAxis =
                timePoints.stream().map(timePoint -> {
                    TimeParams tp = new TimeParams(timePoint.getTime(), timePoint.getEndTime());
                    List<CodeNameCountVO> count = homePageMapper.getWarningLevelCount(tp, request);
                    countMapToLineVO(count, warningLevelLineVos);

                    KeyValueTypeVO kv = new KeyValueTypeVO();
                    kv.setKey(timePoint.getAxisText());
                    kv.setValue(new TimeParams(timePoint.getTime(), timePoint.getEndTime()));
                    kv.setType("timeParams");

                    return kv;
                }).collect(Collectors.toList());

        return new LineChartVO(xAxis, warningLevelLineVos);
    }

    @Override
    public List<ModelCountVo> getModelCount(ListParamsRequest request) {
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        return homePageMapper.getModelCount(request);
    }

    @Override
    public List<CodeNameCountVO> getDistributionStatistics(ListParamsRequest request) {
        List<KeyValueTypeVO> filterParams = request.getFilterParams();
        filterParams.addAll(permissionService.buildParamsByPermission());
        KeyValueTypeVO dept = KeyValueTypeVO.get(filterParams, "dept");
        String deptCode = Objects.isNull(dept) ? MonitorConstant.TOP_DEPT : (String) dept.getValue();

        List<DeptDto> nextLevelDept = permissionService.getNextLevelDept(deptCode);
        List<CodeNameCountVO> distributionStatistics = homePageMapper.getDistributionStatistics(nextLevelDept, request);
        if (nextLevelDept.isEmpty()) {
            List<SimpleUserVO> users = permissionService.getUserOfDept(deptCode);
            users.stream().filter(user -> {
                        for (CodeNameCountVO distributionStatistic : distributionStatistics) {
                            if (user.getUserId().toString().equals(distributionStatistic.getCode())) {
                                distributionStatistic.setCode(generateUserDeptIdCode(user.getUserId(), user.getDeptId()));
                                return false;
                            }
                        }
                        return true;
                    }).map(
                            user -> new CodeNameCountVO(generateUserDeptIdCode(user.getUserId(), user.getDeptId()),
                                    user.toUserString(), 0))
                    .forEach(distributionStatistics::add);
        }

        return distributionStatistics;

    }

    @NotNull
    private static String generateUserDeptIdCode(Long userId, Long deptId) {
        return "user_" + userId + "_" + deptId;
    }

    @Override
    public LineChartVO getDistrictHandle(ListParamsRequest request) {
        List<KeyValueTypeVO> filterParams = request.getFilterParams();
        filterParams.addAll(permissionService.buildParamsByPermission());
        KeyValueTypeVO dept = KeyValueTypeVO.get(filterParams, "dept");
        String deptCode = Objects.isNull(dept) ? MonitorConstant.TOP_DEPT : (String) dept.getValue();
        List<DeptDto> nextLevelDept = permissionService.getNextLevelDept(deptCode);
        // 区域排序,结构如下：1232,510521000000,510504000000,510522000000
        String areaSort = BeanFactoryHolder.getEnv().getProperty("control.handle.area.sort", "");
        if (StringUtils.isNotEmpty(areaSort)){
            String[] areaSortArray = areaSort.split(",");
            List<String> sortedCodes = Arrays.asList(areaSortArray);
            // 根据 sortedCodes 对 nextLevelDept 进行排序
            Map<String, Integer> sortOrder = new HashMap<>();
            for (int i = 0; i < sortedCodes.size(); i++) {
                sortOrder.put(sortedCodes.get(i), i);
            }
            nextLevelDept.sort((dept1, dept2) -> {
                int index1 = sortOrder.getOrDefault(dept1.getCode(), Integer.MAX_VALUE);
                int index2 = sortOrder.getOrDefault(dept2.getCode(), Integer.MAX_VALUE);
                return Integer.compare(index1, index2);
            });

        }
        List<LineVO> warningStatusLineVos = List.of(
                new LineVO(WarningStatusEnum.WAITING_SIGN),
                new LineVO(WarningStatusEnum.SIGN_FINISH),
                new LineVO(WarningStatusEnum.REPLY_FINISH),
                new LineVO(WarningStatusEnum.PROCESS_FINISH),
                new LineVO("0", "总数", new ArrayList<>()));
        List<KeyValueTypeVO> xAxis;
        if (!nextLevelDept.isEmpty()) {
            xAxis = nextLevelDept.stream().map(d -> {
                List<CodeNameCountVO> count = homePageMapper.getDistrictHandle(d.getCode(), request);
                countMapToLineVO(count, warningStatusLineVos);

                KeyValueTypeVO keyValueTypeVO = new KeyValueTypeVO();
                keyValueTypeVO.setKey(d.getShortName());
                keyValueTypeVO.setValue(d.getCode());
                keyValueTypeVO.setType("deptCode");
                return keyValueTypeVO;

            }).collect(Collectors.toList());
        } else {
            List<SimpleUserVO> users = permissionService.getUserOfDept(deptCode);
            xAxis = users.stream().map(user -> {
                List<CodeNameCountVO> count = homePageMapper.getUserHandle(user.getUserId(), request);
                countMapToLineVO(count, warningStatusLineVos);

                KeyValueTypeVO keyValueTypeVO = new KeyValueTypeVO();
                keyValueTypeVO.setKey(user.getUserName());
                keyValueTypeVO.setValue(user.getUserId());
                keyValueTypeVO.setType("user");
                return keyValueTypeVO;
            }).collect(Collectors.toList());
        }
        return new LineChartVO(xAxis, warningStatusLineVos);
    }

    private void countMapToLineVO(List<CodeNameCountVO> count, List<LineVO> warningStatusLineVos) {
        Map<String, Integer> countMap = count.stream()
                .collect(Collectors.toMap(CodeNameCountVO::getCode, CodeNameCountVO::getCount));
        for (LineVO lineVO : warningStatusLineVos) {
            String key = lineVO.getCode();
            if ("0".equals(key)) {
                Integer reduce = countMap.values().stream().reduce(0, Integer::sum);
                lineVO.getValues().add(reduce);
            } else {
                lineVO.getValues().add(countMap.getOrDefault(key, 0));
            }
        }
    }

    @Override
    public List<CameraVO> getCameraList(List<GeometryVO> geometryList) {
        if (CollectionUtils.isEmpty(geometryList)) {
            return sourceMapper.selectCameraList(new GeometryVO());
        }
        return geometryList.stream()
                .map(geometry -> sourceMapper.selectCameraList(geometry))
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public CameraVO getCameraDetail(Long id, String code) {
        CameraVO camera = new CameraVO();
        String key = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.monitor.videoEnv", "hk");
        if (!Objects.isNull(id)) {
            camera = getCameraVO(id, key, camera);
            if (Objects.isNull(camera)) {
                return null;
            }
        }
        if (com.trs.common.utils.StringUtils.isNotEmpty(code)) {
            camera.setCode(code);
        }
        HkService service = map.get(key);
        String previewUrl = service.getCameraPreviewUrl(code);
        if (StringUtils.isNotBlank(previewUrl)) {
            camera.setUrl(previewUrl);
            camera.setIsOnline(Boolean.TRUE);
        } else {
            camera.setIsOnline(Boolean.FALSE);
        }
        return camera;
    }

    private CameraVO getCameraVO(Long id, String key, CameraVO camera) {
        switch (key) {
            case "hk":
                camera = sourceMapper.selectCamera(id);
                break;
            case "dh":
                camera = sourceMapper.selectDhCamera(id);
                break;
            default:
                break;
        }
        return camera;
    }

    @Override
    public List<TrackCountVO> getTrackCount(ListParamsRequest request) {
        List<KeyValueTypeVO> filterParams = request.getFilterParams();
        KeyValueTypeVO dept = KeyValueTypeVO.get(filterParams, "dept");
        filterParams.addAll(permissionService.buildParamsByPermission());
        String deptCode = Objects.isNull(dept) ? MonitorConstant.TOP_DEPT : (String) dept.getValue();
        List<DeptDto> nextLevelDept = permissionService.getNextLevelDept(deptCode);
        if (nextLevelDept.isEmpty()) {
            //按用户查询
            DeptDto deptDto = permissionService.getDeptByCode(deptCode);
            List<SimpleUserVO> users = permissionService.getUserOfDept(deptCode);
            return users.stream().map(user -> {
                TrackCountVO vo = new TrackCountVO();
                vo.setCode(generateUserDeptIdCode(user.getUserId(), deptDto.getId()));
                vo.setName(user.getUserName());
                Integer haveTrackCount = homePageMapper.countHaveTrackPersonByUser(user.getUserId(), request);
                vo.setHaveTrack(haveTrackCount);
                vo.setNoTrack(homePageMapper.countPersonByUser(user.getUserId(), request) - haveTrackCount);
                return vo;
            }).collect(Collectors.toList());
        } else {
            return homePageMapper.getDistributionStatistics(nextLevelDept, request).stream().map(d -> {
                TrackCountVO vo = new TrackCountVO();
                vo.setCode(d.getCode());
                vo.setName(d.getName());
                String prefix = StringUtil.getPrefixCode(d.getCode());
                Integer haveTrackCount = homePageMapper.countHaveTrackPersonByDept(prefix, request);
                vo.setHaveTrack(haveTrackCount);
                vo.setNoTrack(d.getCount() - haveTrackCount);
                return vo;
            }).collect(Collectors.toList());
        }
    }

    @Override
    public PageResult<MonitorPersonListVO> getMonitorPersonList(ListParamsRequest request) {
        List<KeyValueTypeVO> filterParams = request.getFilterParams();

        processDeptFilter(filterParams);

        filterParams.addAll(permissionService.buildParamsByPermission());
        PageParams pageParams = request.getPageParams();
        Page<MonitorPersonListVO> page = homePageMapper.getMonitorPersonList(request, pageParams.toPage());
        page.getRecords().parallelStream().forEach(e -> {
            PersonVO person = profileService.findById(e.getPersonId());
            e.setName(person.getName());
            e.setIdNumber(person.getCertificateNumber());
            e.setMonitorUser(homePageMapper.getPersonMonitorUsersList(e.getPersonId()));
            e.setLabels(List.of());
            if (!person.getTargetType().isEmpty()) {
                e.setLabels(commonMapper.getGroupLabelPathName(person.getTargetType()));
            }
        });

        return PageResult.of(page.getRecords(), pageParams.getPageNumber(), page.getTotal(),
                pageParams.getPageSize());
    }

    /**
     * 分布统计，将部门code("user"_用户id_部门id)拆分
     *
     * @param filterParams 筛选条件
     */
    private static void processDeptFilter(List<KeyValueTypeVO> filterParams) {
        KeyValueTypeVO dept = KeyValueTypeVO.get(filterParams, "dept");
        if (Objects.nonNull(dept) && StringUtils.startsWith((String) (dept.getValue()), "user")) {
            String[] userDeptId = ((String) (dept.getValue())).split("_");
            filterParams.add(new KeyValueTypeVO("currentUser", userDeptId[1], "string"));
            //将部门筛选替换成按用户部门筛选
            dept.setKey("currentUserDept");
            dept.setValue(userDeptId[2]);
        }
    }

    @Override
    public List<WarningHomePageListVO> getPersonWarningListAll(Long personId, ListParamsRequest request) {
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        request.getFilterParams().add(new KeyValueTypeVO("personId", personId, "string"));
        List<WarningHomePageListVO> warnMapData = homePageMapper.getWarnMapData(request);
        handleWarningLevel(warnMapData);
        return warnMapData;
    }

    @Override
    public PageResult<WarningListVO> getWarningInfoList(ListParamsRequest request) {
        PageParams pageParams = request.getPageParams();
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        Page<WarningEntity> page = homePageMapper.getWarningList(request, pageParams.toPage());
        return PageResult.of(
                page.getRecords().parallelStream().map(WarningListVO::warningEntityToAllListVO)
                        .collect(Collectors.toList()),
                pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    @Override
    public String getListTitle(String type, ListParamsRequest params) {
        List<KeyValueTypeVO> filterParams = params.getFilterParams();
        if (filterParams.isEmpty()) {
            return "";
        }
        String deptName = "";
        String levelName = "";
        String personLabelName = "";
        String timeRangeName = "";
        String warningModelName = "";
        String userName = "";

        for (KeyValueTypeVO filterParam : filterParams) {
            String key = filterParam.getKey();
            switch (key) {
                case "deptCode":
                case "dept":
                    String deptCode = (String) filterParam.getValue();
                    DeptDto dept;
                    if (deptCode.startsWith("user")) {
                        String userId = (deptCode.split("_"))[1];
                        UserDto user = permissionService.getUserById(Long.valueOf(userId));
                        userName = user.getRealName();
                    } else {
                        dept = permissionService.getDeptByCode(deptCode);
                        deptName = dept.getShortName();
                    }
                    break;
                case "user":
                    UserDto user = permissionService.getUserById((Long) filterParam.getValue());
                    userName = user.getRealName();
                    break;
                case "level":
                    String levelCode = filterParam.getValue().toString();
                    MonitorLevelEnum monitorLevelEnum = MonitorLevelEnum.codeOf(Integer.parseInt(levelCode));
                    levelName = Objects.isNull(monitorLevelEnum) ? "" : monitorLevelEnum.getName() + "色";
                    break;
                case "timeRange":
                    TimeParams timeParams = (TimeParams) filterParam.getProcessedValue();
                    String name = timeParams.getName();
                    if (StringUtils.isNotBlank(name)) {
                        timeRangeName = name;
                    } else {
                        timeRangeName = String.format("%s至%s", TimeUtil.getSimpleTime(timeParams.getBeginTime()),
                                TimeUtil.getSimpleTime(timeParams.getEndTime()));
                    }
                    break;
                case "personLabel":
                    List<Object> personLabelList = KeyValueTypeVO.nestingListSimplification(filterParam.getValue());
                    if (!personLabelList.isEmpty()) {
                        long labelId = (Integer) personLabelList.get(0);
                        String labelName = homePageMapper.getPeronLabelName(labelId);
                        if (StringUtils.isNotBlank(labelName)) {
                            personLabelName = labelName + (personLabelList.size() > 1 ? "等" : "");
                        }
                    }
                    break;
                case "warningModel":
                    List<Long> modelIds = WarningListBuilder.getModelIds(filterParam.getProcessedValue());
                    if (!modelIds.isEmpty()) {
                        long labelId = modelIds.get(0);
                        warningModelName = homePageMapper.getWarningModelName(labelId);
                    }
                    break;
                default:
                    break;
            }
        }

        StringBuilder title = new StringBuilder();
        if ("control".equals(type)) {
            title.append(deptName).append(userName).append(timeRangeName).append(levelName).append("临控")
                    .append(personLabelName);
        } else if ("warning".equals(type)) {
            title.append(deptName).append(userName).append(timeRangeName).append(personLabelName).append(levelName)
                    .append(warningModelName);
        } else if ("track".equals(type)) {
            title.append(deptName).append(userName).append(levelName).append("临控").append(personLabelName)
                    .append(timeRangeName).append("轨迹情况");
        }

        return title.toString();
    }

    @Override
    public void downloadCamerasInfo() {
        List<String> indexCodeList = hkService.getNodesRoot().stream().map(HaiKanNodesRootVO::getIndexCode).collect(Collectors.toList());
        for (String indexCode : indexCodeList) {
            try {
                List<String> cameraIndexCodeList = Optional.ofNullable(hkService.getNodesByParams(indexCode))
                        .orElse(Collections.emptyList())
                        .stream().map(HaiKanCamerasInfoVO::getCameraIndexCode)
                        .collect(Collectors.toList());
                log.info("根据组织编号获取下级所有监控点列表数据为:{}", cameraIndexCodeList);
                for (String cameraIndexCode : cameraIndexCodeList) {
                    try {
                        List<HaiKanCamerasDeviceInfoVO> encodeDeviceList = Optional.ofNullable(hkService.getCamerasInfo(cameraIndexCode))
                                .orElse(Collections.emptyList());
                        log.info("根据编号获取监控点详细信息数据为:{}", encodeDeviceList);
                        // 转换 encodeDeviceList 列表为 warning_source 列表
                        List<SourceEntity> sourceList = encodeDeviceList.stream()
                                .map(vo -> vo.toWarningSource(vo))
                                .collect(Collectors.toList());
                        sourceList.forEach(list -> {
                            if (com.trs.common.utils.StringUtils.isNotEmpty(list.getCode())) {
                                sourceMapper.insert(list);
                            }
                        });
                        encodeDeviceList.forEach(vo -> {
                            if (com.trs.common.utils.StringUtils.isNotEmpty(vo.getCameraIndexCode())) {
                                sourceMapper.insertHkCamera(vo);
                            }
                        });
                    } catch (Exception e) {
                        log.error("获取监控点详细信息时出错，跳过此监控点: {}", cameraIndexCode, e);
                    }
                }
            } catch (Exception e) {
                log.error("根据组织编号获取监控点时出错，跳过此编号: {}", indexCode, e);
            }
        }
    }


}
