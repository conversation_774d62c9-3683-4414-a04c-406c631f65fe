package com.trs.police.control.domain.dto.fkrxyj;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 导入fk人员的参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ImportFkPersonDTO {

    /**
     * 人员姓名
     */
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证")
    private String idCard;


    /**
     * 人员状态：0-首次入去，1-入区三天
     */
    @ExcelProperty(value = "人员状态")
    private String personStatus;

    /**
     * 首次进入时间
     */
    @ExcelProperty(value = "首次进入时间")
    private String firstIntoTime;

    /**
     * 照片
     */
    @ExcelProperty(value = "照片")
    private String photo;

    /**
     * 民族-值来源于：SELECT * FROM `t_dict` where type='nation';
     */
    @ExcelProperty(value = "民族")
    private String nation;

    /**
     * 户籍地代码
     */
    @ExcelProperty(value = "户籍地")
    private String place;


    /**
     * 电话
     */
    @ExcelProperty(value = "电话")
    private String tel;

    /**
     * 管控级别
     */
    @ExcelProperty(value = "人员级别")
    private String controlLevel;

    /**
     * 标签列表[1, 2, 3]
     */
    @ExcelProperty(value = "人员类别")
    private String labelId;

    /**
     * 责任pcs
     */
    @ExcelProperty(value = "责任派出所代码")
    private String zrpcs;

    /**
     * 性别 1 男 2 女
     */
    @ExcelProperty(value = "性别")
    private String xb;
}
