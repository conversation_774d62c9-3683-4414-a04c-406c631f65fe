package com.trs.police.control.service.warning;

import com.trs.police.control.constant.WarningFkrxyjConstant;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.trs.police.control.constant.WarningFkrxyjConstant.*;

/**
 * 预警服务工厂
 *
 * <AUTHOR>
 */
@Component
public class WarningServiceFactory {

    @Autowired
    private ScrqWarningService scrqWarningService;

    @Autowired
    private RqntWarningService rqntWarningService;

    @Autowired
    private TgphWarningService tgphWarningService;

    @Autowired
    private JmWarningService jmWarningService;

    @Autowired
    private MgqyWarningService mgqyWarningService;

    @Autowired
    private JjWarningService jjWarningService;

    /**
     * 获取预警服务
     *
     * @param entity 预警数据
     * @return w
     */
    public WarningService of(WarningFkrxyjEntity entity) {

        switch (entity.getWarningModel()) {
            case EARLY_WARNING_WITH_TRAJECTORY:
                return rqntWarningService;
            case WarningFkrxyjConstant.FIRST_ENTRY_INTO_THE_ZONE:
                return scrqWarningService;
            case WANDERING_ON_THE_SAME_POLE:
                return tgphWarningService;
            case WANDERING_ON_SILENCE:
                return jmWarningService;
            case WANDERING_ON_SENSITIVE:
                return mgqyWarningService;
            case WANDERING_ON_GATHER:
                return jjWarningService;
            default:
                return null;
        }
    }
}
