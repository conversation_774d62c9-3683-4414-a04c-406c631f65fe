package com.trs.police.control.service;

import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.WarningSourceVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.control.domain.dto.fkrxyj.FkCheckRegularDTO;
import com.trs.police.control.domain.dto.fkrxyj.InitFkDataDTO;
import com.trs.police.control.domain.dto.fkrxyj.PersonRelateToFkDTO;
import com.trs.police.control.domain.dto.fkrxyj.PersonRelateToFkListDTO;
import com.trs.police.control.domain.dto.fkzt.RyypDTO;
import com.trs.police.control.domain.dto.fkzt.WarningYpDTO;
import com.trs.police.control.domain.vo.TrackPointVO;
import com.trs.police.control.domain.vo.fkrxyj.*;
import com.trs.police.control.domain.vo.fkzt.PersonVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/24 10:12
 */
public interface PersonRelate2FkService {

    /**
     * 获取人员列表
     *
     * @param params 参数
     * @return {@link PersonVO}
     */
    PageResult<PersonVO> personList(ListParamsRequest params);

    /**
     * 根据人员档案添加
     *
     * @param personId 人员档案id
     */
    void addByPersonId(Long personId);

    /**
     * 研判
     *
     * @param dto dto
     */
    void ryyp(RyypDTO dto);

    /**
     * 预警研判
     *
     * @param dto dto
     */
    void warningYp(WarningYpDTO dto);

    /**
     * 获取人员列表导入模板
     *
     * @return {@link FileInfoVO}
     */
    FileInfoVO getFkryImportTemplate();

    /**
     * 获取反恐人员根节点
     *
     * @return 反恐人员根节点
     */
    Long getFkryLabelRoot();

    /**
     * 导入反恐人员
     *
     * @param fileId 文件id
     */
    void importFkry(Long fileId);

    /**
     * 导出反恐人员
     *
     * @param ids ids
     */
    void exportFkry(List<Long> ids);


    /**
     * 获取未建档人员列表
     *
     * @param dto 请求参数
     * @return {@link PersonRelateToFkNoRecordVo}
     */
    PageResult<PersonRelateToFkNoRecordVo> getNotRecordList(PersonRelateToFkListDTO dto);

    /**
     * 获取已建档人员列表
     *
     * @param dto 请求参数
     * @return {@link PersonRelateToFkRecordVo}
     */
    PageResult<PersonRelateToFkRecordVo> getRecordList(PersonRelateToFkListDTO dto);

    /**
     * 保存
     *
     * @param dto 参数
     * @return 保存结果
     */
    boolean saveRelationOfProfile(PersonRelateToFkDTO dto);

    /**
     * 保存
     *
     * @param dto 参数
     * @return 保存结果
     */
    boolean manualInitPersonRelate2Fk(InitFkDataDTO dto);


    /**
     * 获取FK人员预警信息
     *
     * @param fkPersonId fk专题主键id
     * @param pageParams 分页参数
     * @return 预警信息
     */
    PageResult<FkPersonWarningVO> getFkWarning(Long fkPersonId, PageParams pageParams);

    /**
     * 获取FK人员轨迹信息
     *
     * @param fkPersonId fk专题主键id
     * @param pageParams 分页参数
     * @return 轨迹信息
     */
    PageResult<TrackPointVO> getFkTrackList(Long fkPersonId, ListParamsRequest pageParams);

    /**
     * 校验FK专题是否已经常控
     *
     * @param dto dto
     * @return 校验结果
     */
    List<FkCheckRegularVO> checkRegular(FkCheckRegularDTO dto);

    /**
     * 未建档导出
     *
     * @param httpServletResponse httpServletResponse
     * @param params              导出参数
     */
    void notRecordedExport(HttpServletResponse httpServletResponse, ExportParams params) throws Exception;

    /**
     * 已建档导出
     *
     * @param httpServletResponse httpServletResponse
     * @param params              导出参数
     */
    void recordedExport(HttpServletResponse httpServletResponse, ExportParams params) throws Exception;

    /**
     * 获取FK专题详情
     *
     * @param fkPersonId 主键id
     * @return 详情
     */
    PersonRelateToFkNoRecordVo getNoRecordDetail(Long fkPersonId);

    /**
     * 获取FK专题详情头部
     *
     * @param fkPersonId 主键id
     * @return 详情
     */
    List<ListColumnResultVO> getNoRecordHeaderDetail(Long fkPersonId);

    /**
     * 获取未建档人员信息
     *
     * @param fkPersonId 主键id
     * @return 详情
     */
    DynamicTableResultVO getNoRecordPersonInfoDetail(Long fkPersonId);

    /**
     * fk人员监控关注
     *
     * @param fkPersonIds 主键id集合，以逗号进行分割
     */
    void careMonitor(String fkPersonIds);

    /**
     * fk人员监控关注
     *
     */
    void careMonitorAll();

    /**
     * 获取反恐感知元
     *
     * @param id xx
     * @return xx
     */
    WarningSourceVO getFkSource(String id);
}
