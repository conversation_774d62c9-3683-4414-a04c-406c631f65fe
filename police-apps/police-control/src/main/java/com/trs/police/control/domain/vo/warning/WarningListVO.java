package com.trs.police.control.domain.vo.warning;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.constant.enums.MonitorBaseTypeEnum;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.WarningModelVO;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.json.serializer.SimpleTimeSerializer;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.control.domain.entity.monitor.MonitorEntity;
import com.trs.police.control.domain.entity.monitor.RegularMonitorEntity;
import com.trs.police.control.domain.entity.warning.WarningNotifyEntity;
import com.trs.police.control.domain.vo.NameAndIdNumberVO;
import com.trs.police.control.domain.vo.WarningEntityWrapperVO;
import com.trs.police.control.mapper.*;
import com.trs.police.control.properties.WarningLevelDisplayProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预警列表vo
 *
 * <AUTHOR>
 */
@Data
public class WarningListVO implements Serializable {

    private static final long serialVersionUID = -3110158718672940223L;

    /**
     * id
     */
    private Long id;
    /**
     * 预警详情
     */
    private String content;
    /**
     * 预警模型
     */
    private List<String> model;
    /**
     * 预警时间
     */
    @JsonSerialize(using = SimpleTimeSerializer.class, nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime warningTime;
    /**
     * 预警级别
     */
    private CodeNameVO warningLevel;

    /**
     * 布控级别
     */
    private CodeNameVO monitorLevel;

    /**
     * 常控级别
     */
    private CodeNameVO regularLevel;

    /**
     * 预警状态
     */
    private CodeNameVO status;
    /**
     * 逾期通知
     */
    private String overdueNotice;
    /**
     * 是否已读
     */
    private Boolean isRead;
    /**
     * 标签
     */
    private String tag;

    /**
     * 常控|临空 id
     */
    private Long monitorId;
    /**
     * 管控类型 常控|临空
     */
    private Integer controlType;

    /**
     * 活动地点
     */
    private String activityAddress;
    /**
     * 活动时间
     */
    @JsonSerialize(using = SimpleTimeSerializer.class, nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime activityTime;
    /**
     * 活动人员
     */
    private List<String> activityPerson;
    /**
     * 逾期信息
     */
    private List<OverdueVO> overdue;
    /**
     * 操作按钮
     */
    private List<String> operations;

    /**
     * 发起人
     */
    private SimpleUserVO initiator;

    /**
     * 通知人
     */
    private List<String> notifyPerson;
    /**
     * 通知部门
     */
    private List<String> notifyDept;
    /**
     * 处置结果
     */
    private String handleResult;

    /**
     * 感知源类型
     */
    private String sourceType;

    /**
     * 数据来源
     */
    private String dataSourceType;
    /**
     * 管控对象信息
     */
    private List<KeyValueVO> monitorTargetInfo;

    private String category;

    /**
     * 命中专题-码表type:hit_subject
     */
    private CodeNameVO hitSubject;

    /**
     * 命中专题的场景-码表type:hit_subject_scene
     */
    private CodeNameVO hitSubjectScene;

    /**
     * 发文字号
     */
    private String fwzh;

    /**
     * 首次处置反馈时限
     */
    @JsonSerialize(using = SimpleTimeSerializer.class, nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime scczfksx;

    /**
     * 布控平台
     */
    private List<Long> monitorPlatform;

    /**
     * 预警平台
     */
    private Integer warningPlatform;

    /**
     * 处置责任单位
     */
    private String dept;

    /**
     * 预警状态
     */
    private String warningStatus;

    /**
     * 超时状态
     * 1 签收已超时
     * 2 签收即将超时
     * 3 反馈已超时
     * 4 反馈即将超时
     */
    private Integer overdueStatus;

    /**
     * 活动人员
     */
    private List<NameAndIdNumberVO> newActivityPerson;


    /**
     * 转换vo
     *
     * @param warningEntity {@link WarningEntity}
     * @param currentUser   当前用户
     * @return {@link WarningListVO}
     */
    public static WarningListVO warningEntityToMyListVO(WarningEntity warningEntity, SimpleUserVO currentUser) {
        WarningListVO vo = new WarningListVO();
        setWarningInfo(vo, warningEntity);
        WarningProcessMapper warningProcessMapper = BeanUtil.getBean(WarningProcessMapper.class);
        WarningProcessEntity warningProcess = warningProcessMapper.getUserProcessByWarningId(warningEntity.getId(),
                currentUser.getUserId(), currentUser.getDeptId());
        if (Objects.nonNull(warningProcess)) {
            String overdueStr = String.join("、", getOverdue(warningProcess.getSignOverdue(), warningProcess.getReplyOverdue()));
            if (StringUtils.isNotBlank(overdueStr)) {
                vo.setOverdue(List.of(new OverdueVO(currentUser.getUserName(), overdueStr)));
            }
            vo.setStatus(new CodeNameVO(warningProcess.getStatus().getCode(), warningProcess.getStatus().getName()));
            if (Objects.nonNull(warningProcess.getDone())) {
                vo.setHandleResult(Boolean.TRUE.equals(warningProcess.getDone().getIsHandle()) ? "已处置" : "不处置");
            }
        }
        vo.setActivityTime(warningEntity.getActivityTime());
        WarningNotifyMapper notifyMapper = BeanUtil.getBean(WarningNotifyMapper.class);
        vo.setNotifyPerson(notifyMapper.getNotifyPersonByWarningId(warningEntity.getId()));
        vo.setNotifyDept(notifyMapper.getNotifyDeptByWarningId(warningEntity.getId()));
        WarningNotifyEntity warningNotifyEntity = notifyMapper.selectByWarningIdAndUser(warningEntity.getId(), currentUser.getUserId(),
                currentUser.getDeptId());
        vo.setIsRead(
                        warningNotifyEntity == null ? Boolean.FALSE :
                                warningNotifyEntity.getIsRead());
        provideMonitorLevelAndRegularLevel(warningEntity, vo);
        return setTrackInfo(vo, warningEntity);
    }


    /**
     * 转换vo
     *
     * @param processEntity {@link WarningEntity}
     * @return {@link WarningListVO}
     */
    public static WarningListVO processEntityToHomePageVo(WarningProcessEntity processEntity) {
        WarningListVO vo = new WarningListVO();
        WarningMapper warningMapper = BeanUtil.getBean(WarningMapper.class);
        WarningEntity warningEntity = warningMapper.selectById(processEntity.getWarningId());
        setWarningInfo(vo, warningEntity);
        if (Objects.nonNull(processEntity.getDone())) {
            vo.setHandleResult(Boolean.TRUE.equals(processEntity.getDone().getIsHandle()) ? "已处置" : "不处置");
        }
        vo.setStatus(new CodeNameVO(processEntity.getStatus().getCode(), processEntity.getStatus().getName()));
        vo.setActivityTime(warningEntity.getActivityTime());
        WarningNotifyMapper notifyMapper = BeanUtil.getBean(WarningNotifyMapper.class);
        vo.setNotifyPerson(notifyMapper.getNotifyPersonByWarningId(processEntity.getWarningId()));
        vo.setNotifyDept(notifyMapper.getNotifyDeptByWarningId(processEntity.getWarningId()));
        setTrackInfo(vo, warningEntity);
        return vo;
    }

    /**
     * 转换vo
     *
     * @param warningEntity {@link WarningEntity}
     * @return {@link WarningListVO}
     */
    public static WarningListVO warningEntityToAllListVO(WarningEntity warningEntity) {
        WarningListVO vo = new WarningListVO();
        setWarningInfo(vo, warningEntity);
        final WarningProcessMapper processMapper = BeanUtil.getBean(WarningProcessMapper.class);
        final WarningProcessEntity warningDone = processMapper.getWarningDone(warningEntity.getId()).orElse(null);
        if (Objects.nonNull(warningDone)) {
            vo.setHandleResult(Boolean.TRUE.equals(warningDone.getDone().getIsHandle()) ? "已处置" : "不处置");
        } else {
            vo.setHandleResult("-");
        }
        if (Boolean.TRUE.equals(processMapper.getWarningDoneStatus(warningEntity.getId()))) {
            vo.setStatus(new CodeNameVO(4, "已完结"));
        } else {
            vo.setStatus(new CodeNameVO(5, "处置中"));
        }
        List<OverdueVO> overdue = processMapper.getWarningDisposalByWarningId(warningEntity.getId()).stream()
                .map(item -> {
                    OverdueVO overdueVO = new OverdueVO();
                    overdueVO.setName(item.getNotifyItem());
                    overdueVO.setOverdue(String.join("、", getOverdue(item.getSignOverdue(), item.getReplyOverdue())));
                    return overdueVO;
                })
                .filter(o -> StringUtils.isNotBlank(o.getOverdue()))
                .collect(Collectors.toList());
        vo.setOverdue(overdue);
        vo.setActivityTime(warningEntity.getActivityTime());
        WarningNotifyMapper notifyMapper = BeanUtil.getBean(WarningNotifyMapper.class);
        vo.setNotifyPerson(notifyMapper.getNotifyPersonByWarningId(warningEntity.getId()));
        vo.setNotifyDept(notifyMapper.getNotifyDeptByWarningId(warningEntity.getId()));
        setTrackInfo(vo, warningEntity);
        vo.setIsRead(true);
        provideMonitorLevelAndRegularLevel(warningEntity, vo);
        return vo;
    }

    /**
     * 提供布控级别和常控级别
     *
     * @param warningEntity 预警信息对象
     * @param vo vo
     */
    private static void provideMonitorLevelAndRegularLevel(WarningEntity warningEntity, WarningListVO vo) {
        if (warningEntity instanceof WarningEntityWrapperVO) {
            DictService dictService = BeanUtil.getBean(DictService.class);
            Map<String, Map<Long, String>> dictMap = dictService.getDictListByTypeList(List.of("monitor_level", "control_regular_monitor_level"))
                    .stream()
                    .collect(Collectors.groupingBy(DictDto::getType, Collectors.toMap(
                            DictDto::getCode,
                            DictDto::getName
                    )));
            Integer monitorLevelCode = ((WarningEntityWrapperVO) warningEntity).getMonitorLevel();
            Integer regularLevelCode = ((WarningEntityWrapperVO) warningEntity).getRegularLevel();
            Optional.ofNullable(monitorLevelCode).ifPresent(code -> {
                vo.setMonitorLevel(new CodeNameVO(code, dictMap.get("monitor_level").get(code.longValue())));
            });
            Optional.ofNullable(regularLevelCode).ifPresent(code -> {
                vo.setRegularLevel(new CodeNameVO(code, dictMap.get("control_regular_monitor_level").get(code.longValue())));
            });
        }
    }

    @NotNull
    private static WarningListVO setTrackInfo(WarningListVO vo, WarningEntity warningEntity) {
        WarningTrackMapper trackMapper = BeanUtil.getBean(WarningTrackMapper.class);
        vo.setActivityPerson(trackMapper.getWarningPersonName(warningEntity.getId()));
        vo.setNewActivityPerson(trackMapper.getWarningPersonNameAndIdNumber(warningEntity.getId()));
        vo.setSourceType(String.join("、", trackMapper.getWarningSourceType(warningEntity.getId())));
        vo.setDataSourceType(String.join("、", trackMapper.getWarningDataSourceType(warningEntity.getId())));
        vo.setCategory(String.join("、", trackMapper.getWarningSourceCategory(warningEntity.getId())));
        setWarningInitiator(vo, warningEntity);
        vo.setMonitorTargetInfo(getMonitorTarget(vo.getTag(), warningEntity.getId(), vo.getMonitorId()));
        return vo;
    }

    private static void setWarningInfo(WarningListVO vo, WarningEntity warningEntity) {
        vo.setId(warningEntity.getId());
        vo.setActivityAddress(warningEntity.getActivityAddress());
        vo.setContent(warningEntity.getContent());
        vo.setControlType(warningEntity.getControlType().getCode());
        vo.setMonitorId(warningEntity.getMonitorId());
        vo.setWarningTime(warningEntity.getWarningTime());

        //用来控制预警级别的展示
        DictService dictService = BeanUtil.getBean(DictService.class);
//        Map<Long, String> monitorLevelV2 = dictService.getDictByType("monitor_level_v2")
//                .stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName));
        WarningLevelDisplayProperties displayProperties = BeanUtil.getBean(WarningLevelDisplayProperties.class);
        Integer warningLevelCode = warningEntity.getWarningLevel().getCode();
        vo.setWarningLevel(new CodeNameVO(warningLevelCode, displayProperties.getDisplayMap().get(warningLevelCode)));

//        Integer code = warningEntity.getWarningLevel().getCode();
//        vo.setWarningLevel(
//                new CodeNameVO(code, monitorLevelV2.get(code.longValue())));

        MonitorWarningModelMapper monitorWarningModelMapper = BeanUtil.getBean(MonitorWarningModelMapper.class);
        List<String> models = monitorWarningModelMapper.getByModelIds(warningEntity.getModelId()).stream()
                .map(WarningModelVO::getName).collect(Collectors.toList());
        vo.setModel(models);
    }

    private static void setWarningInitiator(WarningListVO vo, WarningEntity warningEntity) {
        PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
        switch (Objects.requireNonNull(warningEntity.getControlType())) {
            case MONITOR:
                MonitorMapper monitorMapper = BeanUtil.getBean(MonitorMapper.class);
                final MonitorEntity monitorEntity = monitorMapper.selectById(warningEntity.getMonitorId());
                if (Objects.nonNull(monitorEntity)) {
                    vo.setInitiator(permissionService.findSimpleUser(monitorEntity.getMonitorPersonId(),
                            monitorEntity.getMonitorPersonUnit()));
                    vo.setTag(monitorEntity.getMonitorType().getName());
                }
                break;
            case REGULAR:
                RegularMonitorMapper regularMonitorMapper = BeanUtil.getBean(RegularMonitorMapper.class);
                final RegularMonitorEntity regularMonitorEntity = regularMonitorMapper.selectById(
                        warningEntity.getMonitorId());
                if (Objects.nonNull(regularMonitorEntity)) {
                    vo.setInitiator(permissionService.findSimpleUser(regularMonitorEntity.getCreateUserId(),
                            regularMonitorEntity.getCreateDeptId()));
                    vo.setTag(MonitorBaseTypeEnum.PERSON.getName());
                }
                break;
            default:
        }
    }

    /**
     * 生成TodoTaskVO
     *
     * @return TodoTaskVO
     */
    public TodoTaskVO of() {
        return TodoTaskVO.builder()
                .type(1)
                .id(id)
                .status(status)
                .content(content)
                .isRead(isRead)
                .overdueNotice(overdueNotice)
                .time(TimeUtil.getSimpleTime(warningTime))
                .originTime(warningTime)
                .build();
    }

    /**
     * getter
     *
     * @param signOverdue  签收逾期
     * @param replyOverdue 反馈逾期
     * @return 逾期信息
     */
    public static List<String> getOverdue(Boolean signOverdue, Boolean replyOverdue) {
        ArrayList<String> infoList = new ArrayList<>(2);
        if (Boolean.TRUE.equals(signOverdue)) {
            infoList.add("签收逾期");
        }
        if (Boolean.TRUE.equals(replyOverdue)) {
            infoList.add("反馈逾期");
        }
        return infoList;
    }

    private static List<KeyValueVO> getMonitorTarget(String monitorType, Long warningId, Long monitorId) {
        MonitorTargetRelationMapper monitorTargetRelationMapper = BeanUtil.getBean(MonitorTargetRelationMapper.class);
        if (Objects.nonNull(monitorType)) {
            switch (monitorType) {
                case "群体":
                    return monitorTargetRelationMapper.selectGroupByWarning(warningId);
                case "人员":
                    return monitorTargetRelationMapper.selectPersonByWarning(warningId);
                case "区域":
                    return monitorTargetRelationMapper.selectAreaByMonitor(monitorId);
                default:
            }
        }
        return Collections.emptyList();
    }

    /**
     * 列表逾期信息展示
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OverdueVO {

        /**
         * 名称
         */
        private String name;
        /**
         * 逾期信息
         */
        private String overdue;
    }
}
