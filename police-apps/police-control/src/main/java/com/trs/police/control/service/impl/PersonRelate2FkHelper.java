package com.trs.police.control.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.constant.CommonConstants;
import com.trs.police.control.constant.WarningFkrxyjConstant;
import com.trs.police.control.constant.enums.FkDataSourceEnum;
import com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.mapper.PersonRelateToFkMapper;
import com.trs.police.control.service.warning.FkWarningUpdater;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * PersonRelate2FkHelper
 */
@Component
public class PersonRelate2FkHelper {

    @Resource
    private PersonRelateToFkMapper personRelateToFkMapper;

    @Autowired
    private FkWarningUpdater fkWarningUpdater;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 预警转fk人员
     *
     * @param warningFkrxyjEntity 预警消息
     */
    public void fkWarning2Person(WarningFkrxyjEntity warningFkrxyjEntity) {
        QueryWrapper<PersonRelateToFkEntity> fkWrapper = new QueryWrapper<>();
        fkWrapper.eq("id_card", warningFkrxyjEntity.getIdCard());
        PersonRelateToFkEntity personRelateToFkEntity = personRelateToFkMapper.selectOne(fkWrapper);
        //不存在则新增，存在则需要根据当前预警更新人员状态,和首次进入时间
        if (null == personRelateToFkEntity) {
            personRelateToFkEntity = new PersonRelateToFkEntity();
            personRelateToFkEntity.setName(warningFkrxyjEntity.getName());
            personRelateToFkEntity.setNation(getNation(warningFkrxyjEntity.getName()));
            personRelateToFkEntity.setCreateTime(LocalDateTime.now());
            personRelateToFkEntity.setIdCard(warningFkrxyjEntity.getIdCard());
            personRelateToFkEntity.setPhoto(warningFkrxyjEntity.getSuspectPhoto());
            personRelateToFkEntity.setOnRecord(CommonConstants.RECORD_STATUS_0);
            personRelateToFkEntity.setFirstIntoTime(warningFkrxyjEntity.getCaptureTime());
            int personStatus = CommonConstants.SCRQ;
            if (!warningFkrxyjEntity.getWarningModel().equalsIgnoreCase(WarningFkrxyjConstant.EARLY_WARNING_WITH_TRAJECTORY)){
                personStatus = CommonConstants.RQST;
            }
            personRelateToFkEntity.setPersonStatus(personStatus);
            // 默认数据来源为省厅库
            personRelateToFkEntity.setDataSource(FkDataSourceEnum.STK.getCode());
            // 更新最后的预警信息
            if (Objects.nonNull(personRelateToFkEntity.getId())) {
                personRelateToFkEntity.setLastWarningId(warningFkrxyjEntity.getId());
                personRelateToFkEntity.setLastWarningTime(warningFkrxyjEntity.getCreateTime());
            }
            personRelateToFkMapper.insert(personRelateToFkEntity);
            return;
        }
        int personStatus = CommonConstants.SCRQ;
        if (!warningFkrxyjEntity.getWarningModel().equalsIgnoreCase(WarningFkrxyjConstant.EARLY_WARNING_WITH_TRAJECTORY)){
            personStatus = CommonConstants.RQST;
        }
        // 更新最后的预警信息
        if (Objects.nonNull(personRelateToFkEntity.getId())) {
            personRelateToFkEntity.setLastWarningId(warningFkrxyjEntity.getId());
            personRelateToFkEntity.setLastWarningTime(warningFkrxyjEntity.getCreateTime());
        }
        personRelateToFkEntity.setPersonStatus(personStatus);
        personRelateToFkEntity.setFirstIntoTime(warningFkrxyjEntity.getCaptureTime());
        personRelateToFkMapper.updateById(personRelateToFkEntity);
        // 更新责任pcs等信息
        fkWarningUpdater.updatePerson(warningFkrxyjEntity);
    }

    /**
     * 根据人员档案id生成fk人员
     *
     * @param personId 人员id
     * @return fk人员
     */
    public Optional<PersonRelateToFkEntity> buildByPersonId(Long personId) {
        PersonVO cardById = profileService.findById(personId);
        if (Objects.isNull(cardById)) {
            return Optional.empty();
        }
        PersonRelateToFkEntity entity = new PersonRelateToFkEntity();
        entity.setCreateTime(LocalDateTime.now());
        entity.setName(cardById.getName());
        entity.setIdCard(cardById.getCertificateNumber());
        entity.setPersonStatus(CommonConstants.SCRQ);
        entity.setFirstIntoTime(null);
        entity.setPhoto(CollectionUtils.isEmpty(cardById.getImgs()) ? null : cardById.getImgs().get(0).getUrl());
        entity.setOnRecord(1);
        entity.setPersonProfileId(personId);
        entity.setNation(getNation(cardById.getName()));
        entity.setCareMonitorStatus(1);
        entity.setDataSource(FkDataSourceEnum.STK.getCode());
        entity.setPlaceCode(cardById.getRegisteredResidence());
        entity.setWorkSituation(null);
        entity.setTenPersonType(0);
        entity.setTel(JSON.toJSONString(CollectionUtils.isEmpty(cardById.getTel()) ? new ArrayList<>() : cardById.getTel()));
        entity.setControlLevel(Objects.nonNull(cardById.getControlLevel()) ? cardById.getControlLevel().longValue() : null);
        entity.setLabelId(CollectionUtils.isEmpty(cardById.getPersonLabel()) ? "[]" : JSON.toJSONString(cardById.getPersonLabel()));
        if (!StringUtil.isEmpty(cardById.getDutyPoliceStation())) {
            List<Long> ids = Stream.of(cardById.getDutyPoliceStation().split(","))
                    .distinct()
                    .map(permissionService::getDeptByCode)
                    .map(DeptDto::getId)
                    .collect(Collectors.toList());
            entity.setZrpcs(JSON.toJSONString(ids));
        }
        entity.setXb(cardById.getXb());
        return Optional.of(entity);
    }

    private Integer getNation(String name) {
        if (Objects.isNull(name)) {
            return WarningFkrxyjConstant.NATION_CODE_WZ;
        }
        return name.contains("·")
                ? WarningFkrxyjConstant.NATION_CODE_WZ : WarningFkrxyjConstant.NATION_CODE_HZ;
    }

}
