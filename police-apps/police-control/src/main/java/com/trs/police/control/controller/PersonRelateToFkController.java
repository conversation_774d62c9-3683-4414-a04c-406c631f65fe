package com.trs.police.control.controller;

import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.WarningSourceVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.control.domain.dto.fkrxyj.FkCheckRegularDTO;
import com.trs.police.control.domain.dto.fkrxyj.InitFkDataDTO;
import com.trs.police.control.domain.dto.fkrxyj.PersonRelateToFkDTO;
import com.trs.police.control.domain.dto.fkrxyj.PersonRelateToFkListDTO;
import com.trs.police.control.domain.dto.fkzt.RyypDTO;
import com.trs.police.control.domain.dto.fkzt.WarningYpDTO;
import com.trs.police.control.domain.vo.TrackPointVO;
import com.trs.police.control.domain.vo.fkrxyj.*;
import com.trs.police.control.domain.vo.fkzt.PersonVO;
import com.trs.police.control.service.PersonRelate2FkService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/26 10:11
 */
@RestController
@RequestMapping("/fkzt")
@Api(value = "FK专题相关接口", tags = "FK专题相关接口")
@Slf4j
public class PersonRelateToFkController {

    @Resource
    private PersonRelate2FkService personRelate2FkService;

    /**
     * fk人员列表
     *
     * @param params ps
     * @return ps
     */
    @PostMapping("/fkPersonList")
    public PageResult<PersonVO> personList(@RequestBody ListParamsRequest params) {
        return personRelate2FkService.personList(params);
    }

    /**
     * 人员研判
     *
     * @param dto 参数
     */

    @PostMapping("/ryyp")
    public void ryyp(@RequestBody RyypDTO dto) {
        personRelate2FkService.ryyp(dto);
    }

    /**
     * 预警研判
     *
     * @param dto 参数
     */

    @PostMapping("/warningYp")
    public void warningYp(@RequestBody WarningYpDTO dto) {
        personRelate2FkService.warningYp(dto);
    }

    /**
     * 获取反恐人员导入模版
     *
     * @return 反恐人员倒入模版
     */
    @GetMapping("/getFkryImportTemplate")
    FileInfoVO getFkryImportTemplate() {
        return personRelate2FkService.getFkryImportTemplate();
    }

    /**
     * 获取反恐人员根节点
     *
     * @return 反恐人员根节点
     */
    @GetMapping("/getFkryLabelRoot")
    public Long getFkryLabelRoot() {
        return personRelate2FkService.getFkryLabelRoot();
    }

    /**
     * 导入反恐人员
     *
     * @param fileId 文件id
     */
    @PostMapping("/importFkry")
    public void importFkry(Long fileId) {
        personRelate2FkService.importFkry(fileId);
    }

    /**
     * 导出反恐人员
     *
     * @param ids ids
     */
    @PostMapping("/exportFkry")
    public void exportFkry(@RequestBody List<Long> ids) {
        personRelate2FkService.exportFkry(ids);
    }

    /**
     * @param personRelateToFkListDTO 参数
     * @return 列表
     */
    @ApiOperation(value = "获取未建档人员列表", notes = "获取未建档人员列表")
    @PostMapping("/getNotRecordList")
    public PageResult<PersonRelateToFkNoRecordVo> getNotRecordList(@RequestBody PersonRelateToFkListDTO personRelateToFkListDTO) {
        return personRelate2FkService.getNotRecordList(personRelateToFkListDTO);
    }

    /**
     * @param personRelateToFkListDTO 参数
     * @return 列表
     */
    @ApiOperation(value = "获取已建档人员列表", notes = "获取已建档人员列表")
    @PostMapping("/getRecordList")
    public PageResult<PersonRelateToFkRecordVo> getRecordList(@RequestBody PersonRelateToFkListDTO personRelateToFkListDTO) {
        //排序字段特殊处理
        String intoOrder = personRelateToFkListDTO.getFirstIntoTimeOrder();
        String createOrder = personRelateToFkListDTO.getCreateTimeOrder();
        if (!StringUtils.isEmpty(intoOrder)) {
            personRelateToFkListDTO.setCreateTimeOrder("");
        }
        if (!StringUtils.isEmpty(createOrder)) {
            personRelateToFkListDTO.setFirstIntoTimeOrder("");
        }

        return personRelate2FkService.getRecordList(personRelateToFkListDTO);
    }

    /**
     * 关联档案和人员
     *
     * @param dto 参数
     * @return 列表
     */
    @ApiOperation(value = "关联档案和人员", notes = "关联档案和人员")
    @PostMapping("/saveRelationOfProfile")
    public String saveRelationOfProfile(@RequestBody PersonRelateToFkDTO dto) {
        boolean result = personRelate2FkService.saveRelationOfProfile(dto);
        return result ? "关联档案和FK人员关系成功" : "关联档案和FK人员关系失败！";
    }

    /**
     * 手动初始化fk人员数据
     *
     * @param dto 参数
     * @return 结果
     */
    @ApiOperation(value = "手动初始化fk人员数据", notes = "手动初始化fk人员数据")
    @PostMapping("/manualInitPersonRelate2Fk")
    public String manualInitPersonRelate2Fk(InitFkDataDTO dto) {
        boolean result = personRelate2FkService.manualInitPersonRelate2Fk(dto);
        return result ? "手动初始化fk人员数据成功" : "手动初始化fk人员数据失败！";
    }

    /**
     * 获取反恐感知元
     *
     * @param id xx
     * @return xx
     */
    @GetMapping("/source")
    public RestfulResultsV2<WarningSourceVO> getFkSource(@RequestParam(value = "id") String id){
        return RestfulResultsV2.ok(personRelate2FkService.getFkSource(id));
    }


    /**
     * 获取FK人员预警信息
     *
     * @param fkPersonId fk专题主键id
     * @param pageParams 分页参数
     * @return 预警信息
     */
    @ApiOperation(value = "获取FK人员预警信息", notes = "获取FK人员预警信息")
    @PostMapping("/detail/{fkPersonId}/warning")
    public PageResult<FkPersonWarningVO> getFkWarning(@PathVariable Long fkPersonId,
                                                      @RequestBody ListParamsRequest pageParams) {
        return personRelate2FkService.getFkWarning(fkPersonId, pageParams.getPageParams());
    }

    /**
     * 获取FK人员轨迹信息
     *
     * @param fkPersonId fk专题主键id
     * @param pageParams 分页参数
     * @return 轨迹信息
     */
    @ApiOperation(value = "获取FK人员轨迹信息", notes = "获取FK人员轨迹信息")
    @PostMapping("/track/{fkPersonId}/list")
    public PageResult<TrackPointVO> getFkTrackList(@PathVariable Long fkPersonId,
                                                   @RequestBody ListParamsRequest pageParams) {
        return personRelate2FkService.getFkTrackList(fkPersonId, pageParams);
    }

    /**
     * 校验FK专题是否已经常控
     *
     * @param dto dto
     * @return 校验结果
     */
    @ApiOperation(value = "校验FK专题是否已经常控", notes = "校验FK专题是否已经常控")
    @PostMapping("/checkRegular")
    public List<FkCheckRegularVO> checkRegular(@RequestBody FkCheckRegularDTO dto) {
        return personRelate2FkService.checkRegular(dto);
    }

    /**
     * 未建档导出
     *
     * @param httpServletResponse httpServletResponse
     * @param params              导出参数
     */
    @ApiOperation(value = "未建档导出", notes = "未建档导出")
    @PostMapping("/notRecorded/export")
    public void notRecordedExport(HttpServletResponse httpServletResponse, @RequestBody ExportParams params) {
        try {
            personRelate2FkService.notRecordedExport(httpServletResponse, params);
        } catch (Exception e) {
            log.error("列表导出错误：", e);
            throw new TRSException("未建档关注人员列表！");
        }
    }

    /**
     * 已建档导出
     *
     * @param httpServletResponse httpServletResponse
     * @param params              导出参数
     */
    @ApiOperation(value = "已建档导出", notes = "已建档导出")
    @PostMapping("/recorded/export")
    public void recordedExport(HttpServletResponse httpServletResponse, @RequestBody ExportParams params) {
        try {
            personRelate2FkService.recordedExport(httpServletResponse, params);
        } catch (Exception e) {
            log.error("列表导出错误：", e);
            throw new TRSException("已建档关注人员列表！");
        }
    }

    /**
     * 获取FK专题详情
     *
     * @param fkPersonId 主键id
     * @return 详情
     */
    @ApiOperation(value = "获取FK专题详情", notes = "获取FK专题详情")
    @GetMapping("/detail/{fkPersonId}")
    public PersonRelateToFkNoRecordVo getNoRecordDetail(@PathVariable("fkPersonId") Long fkPersonId) {
        return personRelate2FkService.getNoRecordDetail(fkPersonId);
    }

    /**
     * 获取未建档FK专题详情
     *
     * @param fkPersonId 主键id
     * @return 详情
     */
    @ApiOperation(value = "获取未建档FK专题详情", notes = "获取未建档FK专题详情")
    @GetMapping("/detail/header/{fkPersonId}")
    public List<ListColumnResultVO> getNoRecordHeaderDetail(@PathVariable("fkPersonId") Long fkPersonId) {
        return personRelate2FkService.getNoRecordHeaderDetail(fkPersonId);
    }

    /**
     * 获取未建档人员信息
     *
     * @param fkPersonId 主键id
     * @return 详情
     */
    @ApiOperation(value = "获取未建档人员信息", notes = "获取未建档人员信息")
    @GetMapping("/detail/personInfo/{fkPersonId}")
    public DynamicTableResultVO getNoRecordPersonInfoDetail(@PathVariable("fkPersonId") Long fkPersonId) {
        return personRelate2FkService.getNoRecordPersonInfoDetail(fkPersonId);
    }

    /**
     * 对fk人员进行关注监控
     *
     * @param fkPersonIds 主键id集合，以逗号进行分割
     * @return 关注监控结果
     */
    @ApiOperation(value = "对fk人员进行关注监控", notes = "对fk人员进行关注监控")
    @GetMapping("/careMonitor")
    public String careMonitor(String fkPersonIds) {
        personRelate2FkService.careMonitor(fkPersonIds);
        return "fk人员关注监控成功！";
    }

    /**
     * 关注监控所有的fk人员
     *
     * @return 关注监控结果
     */
    @ApiOperation(value = "对fk人员进行关注监控", notes = "对fk人员进行关注监控")
    @GetMapping("/careMonitorAll")
    public String careMonitorAll() {
        personRelate2FkService.careMonitorAll();
        return "fk人员关注监控成功！";
    }

}
