package com.trs.police.control.service.warning;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.mapper.PersonRelateToFkMapper;
import com.trs.police.control.mapper.SourceMapper;
import com.trs.police.control.mapper.WarningFkrxyjMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * fk预警信息更新
 *
 * <AUTHOR>
 */
@Component
public class FkWarningUpdater {

    @Autowired
    private WarningServiceFactory warningServiceFactory;

    @Resource
    private WarningFkrxyjMapper warningFkrxyjMapper;

    @Resource
    private PersonRelateToFkMapper personRelateToFkMapper;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private SourceMapper sourceFkMapper;

    /**
     * 更新预警信息
     *
     * @param entity en
     */
    public void update(WarningFkrxyjEntity entity) {
        WarningService warningService = warningServiceFactory.of(entity);
        if (Objects.isNull(warningService)) {
            return;
        }
        MonitorLevelEnum level = warningService.getWarningLevel(entity);
        entity.setWarningLevel(level);
        warningFkrxyjMapper.updateById(entity);
        // 找到人员，修改责任派出所
        updatePerson(entity);
    }

    /**
     * 更新预警信息
     *
     * @param entity en
     */
    public void updatePerson(WarningFkrxyjEntity entity) {
        // 找到人员，修改责任派出所
        PersonRelateToFkEntity fkEntity = personRelateToFkMapper.selectOne(
                Wrappers.lambdaQuery(PersonRelateToFkEntity.class)
                        .eq(PersonRelateToFkEntity::getIdCard, entity.getIdCard())
        );
        if (Objects.nonNull(fkEntity)) {
            String before = fkEntity.getZrpcs();
            // 初始化责任派出所
            initZrpcs(fkEntity, entity);
            if (!Objects.equals(before, fkEntity.getZrpcs())) {
                personRelateToFkMapper.updateById(fkEntity);
            }
        }
    }

    /**
     * 初始化责任派出所
     *
     * @param fkEntity fkEntity
     * @param warning 预警信息
     */
    private void initZrpcs(PersonRelateToFkEntity fkEntity, WarningFkrxyjEntity warning) {
        // 已经建档的人员
        if (Objects.nonNull(fkEntity.getPersonProfileId())) {
            if (!StringUtil.isEmpty(fkEntity.getZrpcs())) {
                JSONArray pcs = JSONArray.parseArray(fkEntity.getZrpcs());
                if (pcs.size() > 0) {
                    return;
                }
            }
            // 直接取档案的责任pcs
            PersonCardVO cardById = profileService.getPersonCardById(fkEntity.getPersonProfileId());
            if (!StringUtil.isEmpty(cardById.getDutyPoliceStation())) {
                List<Long> ids = Stream.of(cardById.getDutyPoliceStation().split(","))
                        .distinct()
                        .map(permissionService::getDeptByCode)
                        .map(DeptDto::getId)
                        .collect(Collectors.toList());
                fkEntity.setZrpcs(JSON.toJSONString(ids));
            }
        }
        // 未建档的人员
        initZrpcsByGzy(fkEntity, warning);
    }

    // 通过感知源初始化责任pcs
    private void initZrpcsByGzy(PersonRelateToFkEntity fkEntity, WarningFkrxyjEntity entity) {
        // 查找当前感知源是否匹配到责任派出所
        SourceEntity sourceFkEntity = StringUtils.isEmpty(entity.getDeviceCode()) ? null : sourceFkMapper.selectOne(
                Wrappers.lambdaQuery(SourceEntity.class)
                        .eq(SourceEntity::getCode, entity.getDeviceCode())
        );
        if (Objects.nonNull(sourceFkEntity) && Objects.nonNull(sourceFkEntity.getDept())) {
            fkEntity.setZrpcs(JSON.toJSONString(Arrays.asList(sourceFkEntity.getDept())));
            return;
        }
        // 目前没有感知源pcs的映射关系 直接走配置
        Long id = BeanFactoryHolder.getEnv().getProperty("control.fkry.zrpcs.id", Long.class, 7L);
        if (Objects.nonNull(id)) {
            fkEntity.setZrpcs(JSON.toJSONString(Arrays.asList(id)));
        }
    }
}
