# 8.1

- XMKFB-3036 后 -高新部署数据碰撞

# 10.4

- XMKFB-4681 泸州-碰撞功能确认

# 10.5

- XMKFB-4792 【泸州】数据碰撞，碰撞结果列表数据支持搜索查询
- XMKFB-4797 【泸州】数据碰撞，所有车辆的碰撞数据都打上了出租车标签
- XMKFB-4782 【泸州】数据碰撞，人员碰撞结果中同一人员重复出现
- XMKFB-4799 【泸州】数据碰撞，/spacetime-collision/$id/detail/page接口响应速度应作优化

# 11.1

- XMKFB-5018 【泸州】碰撞任务，多轨迹播放列表中的数据接口报错
- XMKFB-4787 【泸州】数据碰撞，无法碰撞出虚拟号imsi数据结果
- 


## 新增配置项

```properties
# 创建spacetime-collision-new.properties
# 20241112 - 褚川宝 - 增加轨迹信息表配置
search.ai.gj.tableName=theme_gjxxb
# 20241112 - 褚川宝 - DB-SDK相关配置
trs.db.default.repository.type=ES
trs.db.default.repository.host=
trs.db.default.repository.port=
trs.db.default.repository.user=
trs.db.default.repository.password=
trs.db.default.repository.enableKerberos=false
trs.db.default.repository.cloudType=huawei
trs.db.default.repository.userKerTabPath=
trs.db.default.repository.krb5ConfPath=
trs.db.default.repository.principal=
trs.db.default.repository.zookeeperPrincipal=
collision.gjxxb.db.type=ES

```

# 11.2

- XMKFB-4792 【泸州】数据碰撞，碰撞结果列表数据支持搜索查询

# 11.3

- XMKFB-5313 【泸州】数据碰撞，碰撞结果里显示的人员轨迹数为空

# 17.1

- XMKFB-8155 【自贡】【数据碰撞】【验收】增加消息通知

# 18.1

- XMKFB-8237 轨迹碰撞部署并测试成功
