package com.trs.police.spacetime.collision.controller;

import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.spacetime.collision.domain.dto.TrailDotDto;
import com.trs.police.spacetime.collision.domain.request.ResultDetailRequest;
import com.trs.police.spacetime.collision.domain.request.TrackCountRequest;
import com.trs.police.spacetime.collision.domain.vo.*;
import com.trs.police.spacetime.collision.service.CollisionService;
import com.trs.police.spacetime.collision.service.ExportService;
import com.trs.police.spacetime.collision.service.impl.CollisionResultImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * 时空碰撞controller
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class CollisionController {

    @Resource
    private CollisionService collisionService;
    @Resource
    private ExportService exportService;
    @Resource
    private CollisionResultImpl collisionResult;


    /**
     * 创建碰撞 https://yapi-192.trscd.com.cn/project/4974/interface/api/153726
     *
     * @param createVO vo
     * @return id
     */
    @PostMapping("/create")
    public Long collisionCreate(@RequestBody CollisionCreateVO createVO) {
        return collisionService.collisionCreate(createVO);
    }

    /**
     * 修改碰撞 https://yapi-192.trscd.com.cn/project/4974/interface/api/153735
     *
     * @param id       碰撞id
     * @param createVO vo
     */
    @PutMapping("/{id}/edit")
    public void collisionEdit(@PathVariable("id") Long id, @RequestBody CollisionCreateVO createVO) {
        collisionService.collisionEdit(id, createVO);
    }

    /**
     * 删除碰撞 https://yapi-192.trscd.com.cn/project/4974/interface/api/153744
     *
     * @param id 碰撞id
     */
    @DeleteMapping("/{id}")
    public void collisionDelete(@PathVariable("id") Long id) {
        collisionService.collisionDelete(id);
    }

    /**
     * 运行碰撞 https://yapi-192.trscd.com.cn/project/4974/interface/api/153771
     *
     * @param id 碰撞id
     */
    @GetMapping("/{id}/run")
    public void collisionRun(@PathVariable("id") Long id) {
        collisionService.collisionRun(id);
    }

    /**
     * 停止碰撞 https://yapi-192.trscd.com.cn/project/4974/interface/api/153780
     *
     * @param id 碰撞id
     */
    @GetMapping("/{id}/stop")
    public void collisionStop(@PathVariable("id") Long id) {
        collisionService.collisionStop(id);
    }

    /**
     * 时空碰撞列表 https://yapi-192.trscd.com.cn/project/4974/interface/api/153753
     *
     * @param request 请求参数
     * @return {@link CollisionListVO}
     */
    @PostMapping("/list")
    public PageResult<CollisionListVO> collisionList(@RequestBody ListParamsRequest request) {
        return collisionService.collisionList(request);
    }

    /**
     * <a href="https://yapi-192.trscd.com.cn/project/4974/interface/api/153762">详情</a>
     *
     * @param id 时空碰撞id
     * @return {@link CollisionDetailVO}
     */
    @GetMapping("/{id}/detail")
    public CollisionDetailVO collisionDetail(@PathVariable Long id) {
        return collisionService.collisionDetail(id);
    }

    /**
     * 详情 https://yapi-192.trscd.com.cn/project/4974/interface/api/153996
     *
     * @param id      时空碰撞id
     * @param request 参数
     * @return {@link CollisionDetailVO}
     */
    @PostMapping("/{id}/detail/page")
    public PageResult<CollisionResultDetail> collisionDetailPage(@PathVariable Long id, @RequestBody ResultDetailRequest request) {
        return collisionService.getTrackPage(id, request.getDisplayType(),
                Optional.ofNullable(request.getPageParams()).orElse(new PageParams()),
                Optional.ofNullable(request.getSearchParams()).orElse(new SearchParams()));
    }

    /**
     * 碰撞结果详情
     *
     * @param id 时空碰撞id
     * @param vo 碰撞结果
     * @return {@link CollisionTrackVO}
     */
    @PostMapping("/{id}/detail/result")
    public List<List<CollisionTrackResultVO>> collisionDetailPersonCard(@PathVariable("id") Long id,
                                                                        @RequestBody List<CollisionResultDetail> vo) {
        return collisionService.collisionDetailPersonCard(id, vo);
    }

    /**
     * 照片
     *
     * @param id      碰撞id
     * @param trackId 轨迹id
     * @return 结果
     */
    @PostMapping("/{id}/detail/result/{trackId}/photo")
    public List<FileInfoVO> collisionDetailPhoto(@PathVariable("id") Long id,
                                                 @PathVariable("trackId") Long trackId) {
        return collisionService.collisionDetailPhoto(id, trackId);
    }

    /**
     * 更新碰撞通知电话
     *
     * @param id  碰撞id
     * @param tel 电话
     */
    @PostMapping("/{id}/tel")
    public void updateCollisionNotifyTel(@PathVariable("id") Long id, @Nullable @RequestParam String tel) {
        collisionService.updateCollisionNotifyTel(id, tel);
    }

    /**
     * 获取时空碰撞未读数量
     *
     * @return 未读数量
     */
    @GetMapping("/un-read/count")
    public Integer getCollisionUnReadCount() {
        return collisionService.getCollisionUnReadCount();
    }

    /**
     * 获取列表统计数据
     *
     * @return 统计数据
     */
    @GetMapping("/list/statistics")
    public CollisionListStatisticsVO getCollisionListStatistics() {
        return collisionService.getCollisionListStatistics(new ListParamsRequest());
    }

    /**
     * 获取列表统计数据
     *
     * @param request 参数
     * @return 统计数据
     */
    @PostMapping("/list/statistics")
    public CollisionListStatisticsVO getCollisionListStatisticsPost(@RequestBody(required = false) ListParamsRequest request) {
        return collisionService.getCollisionListStatistics(request);
    }

    /**
     * 统计数据
     * https://yapi-192.trscd.com.cn/project/4974/interface/api/154383
     *
     * @param request 参数
     * @return {@link TrackCountVO}
     */
    @PostMapping("/prediction")
    public TrackCountVO countTracks(@RequestBody TrackCountRequest request) {
        return collisionService.countTracks(request);
    }

    /**
     * 导出
     *
     * @param httpServletResponse response
     * @param id                  碰撞id
     **/
    @GetMapping("/{id}/export")
    public void exportList(HttpServletResponse httpServletResponse, @PathVariable("id") Long id) {
        try {
            exportService.exportList(httpServletResponse, id);
        } catch (IOException e) {
            log.error("导出失败！", e);
            throw new TRSException("导出失败！");
        }
    }

    /**
     * 统计数据
     * https://yapi-192.trscd.com.cn/project/4974/interface/api/154383
     */
    @GetMapping("/result")
    public void processResult() {
        collisionResult.processResult();
    }

    /**
     * 根据特征值获取轨迹点位列表
     * https://trsyapi.trscd.com.cn/project/534/interface/api/42580
     *
     * @param params 请求参数
     * @return 结果
     */
    @PostMapping("/trail/dots")
    public List<CollisionTrailDotResultVO> trailDots(@RequestBody List<TrailDotDto> params) {
        return collisionService.getCollisionTrailDots(params);
    }
}
