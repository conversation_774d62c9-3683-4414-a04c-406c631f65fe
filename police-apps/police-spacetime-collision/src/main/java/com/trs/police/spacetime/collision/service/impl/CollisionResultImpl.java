package com.trs.police.spacetime.collision.service.impl;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.Source;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.core.vo.message.PhoneMessageVO;
import com.trs.police.common.openfeign.starter.service.ControlService;
import com.trs.police.common.openfeign.starter.service.MessageService;
import com.trs.police.spacetime.collision.constant.enums.CollisionDisplayTypeEnum;
import com.trs.police.spacetime.collision.constant.enums.CollisionStatusEnum;
import com.trs.police.spacetime.collision.constant.enums.WarningSourceTypeEnum;
import com.trs.police.spacetime.collision.domain.dto.AreaResult;
import com.trs.police.spacetime.collision.domain.dto.MysqlResultDto;
import com.trs.police.spacetime.collision.domain.entity.CollisionEntity;
import com.trs.police.spacetime.collision.domain.entity.CollisionJobEntity;
import com.trs.police.spacetime.collision.domain.entity.CollisionResultEntity;
import com.trs.police.spacetime.collision.domain.vo.CollisionKafkaMessageVo;
import com.trs.police.spacetime.collision.mapper.CollisionJobMapper;
import com.trs.police.spacetime.collision.mapper.CollisionMapper;
import com.trs.police.spacetime.collision.mapper.CollisionResultMapper;
import com.trs.police.spacetime.collision.mapper.MysqlResultMapper;
import com.trs.police.spacetime.collision.service.CollisionResultService;
import com.trs.police.spacetime.collision.service.IGuiJiXinXiBiaoService;
import com.trs.web.builder.send.entity.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 碰撞结果入库
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CollisionResultImpl implements CollisionResultService {

    @Resource
    private CollisionJobMapper collisionJobMapper;
    @Resource
    private CollisionResultMapper collisionResultMapper;
    @Resource
    private CollisionMapper collisionMapper;
    @Resource
    private MysqlResultMapper mysqlResultMapper;
    @Resource
    private MessageService messageService;
    @Resource
    private ControlService controlService;
    @Resource
    private IGuiJiXinXiBiaoService iGuiJiXinXiBiaoService;


    @Override
    public void processCollision(String message) {
        try {
            Message<String> input = JSON.parseObject(message, Message.class);
            if (Objects.isNull(input)) {
                log.error("消息解析失败！消息：{}", message);
                return;
            }
            String contentStr = input.getContent();
            CollisionKafkaMessageVo kafkaMessageVo = JsonUtil.parseObject(contentStr, CollisionKafkaMessageVo.class);
            if (Objects.isNull(kafkaMessageVo)) {
                log.error("碰撞结果解析失败！消息：{}", message);
                return;
            }
            final Boolean success = kafkaMessageVo.getSuccess();
            final var dataNode = kafkaMessageVo.getData();
            final String jobIdStr = dataNode.getJobId();
            final Long jobId = Long.parseLong(jobIdStr);
            final CollisionJobEntity job = collisionJobMapper.selectById(jobId);
            if (Objects.isNull(job)) {
                log.warn("碰撞任务不存在！任务id：{}", jobId);
                return;
            }
            final CollisionEntity collision = collisionMapper.selectById(job.getCollisionId());
            if (Objects.isNull(collision)) {
                log.warn("碰撞任务关联的碰撞不存在！任务id：{}，关联的碰撞ID：{}", jobId, job.getCollisionId());
                return;
            }
            if (Objects.nonNull(collision.getLastJobId()) && !Objects.equals(collision.getLastJobId(), jobId)) {
                log.warn(
                        "碰撞中最后运行的jobId[{}]不为空且跟当前消息中的任务id[{}]不一致，可能是历史任务，直接丢弃",
                        collision.getLastJobId(),
                        job.getId()
                );
                return;
            }
            //碰撞状态为运行中或者跟最后运行的jobId一致（spark可能运行很久，超时返回的数据也要处理）
            if (Objects.equals(collision.getStatus(), CollisionStatusEnum.RUNNING)
                    || Objects.equals(collision.getLastJobId(), jobId)) {
                job.setEndTime(LocalDateTime.now());
                job.setSuccess(success);
                if (success) {
                    collision.setStatus(CollisionStatusEnum.FINISH);
                    saveJobResult(collision.getId(), jobId);
                    job.setResult(getResultCount(jobId).toArray(new KeyValueVO[0]));
                    sendPhoneMessage(collision);
                } else {
                    collision.setStatus(CollisionStatusEnum.FAIL);
                    job.setFailReason("碰撞任务运行失败！");
                }
                collisionJobMapper.updateById(job);
                collision.setUnRead(Boolean.TRUE);
                collisionMapper.updateById(collision);
                log.info("碰撞结果入库成功！碰撞任务id：{}", job.getId());
            } else {
                log.info("碰撞状态不为运行中，已丢弃！碰撞任务id：{}", job.getId());
            }
        } catch (Exception e) {
            log.error("碰撞结果处理失败！消息：{}", message, e);
        }
    }

    private List<KeyValueVO> getResultCount(Long id) {
        List<KeyValueVO> resultList = collisionResultMapper.countResultByDisplayType(id);
        if (Objects.isNull(resultList) || resultList.isEmpty()) {
            return List.of(new KeyValueVO("结果", "0"));
        } else {
            return resultList
                    .stream()
                    .map(resultVO -> {
                        KeyValueVO keyValueVO = new KeyValueVO();
                        Integer code = Integer.parseInt(resultVO.getKey());
                        keyValueVO.setKey(CollisionDisplayTypeEnum.getNameByCode(code));
                        int count = collisionResultMapper.countResultByObjectValue(id, code).size();
                        keyValueVO.setValue(Integer.toString(count));
                        return keyValueVO;
                    }).collect(Collectors.toList());
        }
    }

    /**
     * test
     */
    public void processResult() {
        collisionJobMapper.selectList(null).forEach(job -> {
            if (job.getSuccess() != null && job.getSuccess()) {
                job.setResult(getResultCount(job.getId()).toArray(new KeyValueVO[0]));
                collisionJobMapper.updateById(job);
            }
        });
    }

    private void saveJobResult(Long collisionId, Long jobId) {
        List<MysqlResultDto> resultList = mysqlResultMapper.selectResultByJobId(jobId.toString());
        List<Source> sourceList = resultList.stream()
                .flatMap(resultDto -> saveTracks(collisionId, jobId, resultDto).stream())
                .collect(Collectors.toList());
        try {
            controlService.saveSourceIfNotExists(sourceList);
        } catch (Exception e) {
            log.error("感知源同步失败！", e);
        }

    }

    private List<Source> saveTracks(Long collisionId, Long jobId, MysqlResultDto resultDto) {
        List<AreaResult> areaResults = JsonUtil.parseArray(resultDto.getTracks(), AreaResult.class);
        return areaResults.stream().flatMap(area -> {
            return area.getTrack().stream().map(track -> {
                CollisionResultEntity entity = new CollisionResultEntity();
                entity.setCollisionId(collisionId);
                entity.setJobId(jobId);
                entity.setObjectType(resultDto.getObjectType());
                entity.setObjectValue(resultDto.getObjectValue());
                Long personId = getPersonId(resultDto.getObjectType(), resultDto.getObjectValue());
                String content = String.format("%s探测到%s（%s）", track.getGzylx(), resultDto.getObjectType(), resultDto.getObjectValue());
                if (personId != null) {
                    content = content + String.format("，该设备属于人员%s",
                            collisionResultMapper.getNameByPersonId(personId));
                    entity.setDisplayType(CollisionDisplayTypeEnum.PERSON.getCode());
                    entity.setPersonId(personId);
                } else {
                    entity.setDisplayType(getDisplayType(resultDto.getObjectType()));
                }
                entity.setContent(content);
                entity.setAreaName(area.getCondition());
                entity.setTrackTime(LocalDateTime.parse(TimeUtils.stringToString(track.getTime(), TimeUtils.YYYYMMDD_HHMMSS2), TimeUtil.WARNING_MESSAGE_PATTERN));
                entity.setDistrict(track.getDistrict());

                String trackId = track.getRecordId();
                entity.setTrackTime(LocalDateTime.parse(TimeUtils.stringToString(track.getTime(), TimeUtils.YYYYMMDD_HHMMSS2), TimeUtil.WARNING_MESSAGE_PATTERN));
                entity.setRecordId(trackId);
                entity.setTableName(track.getSjlyxtfldm());
                entity.setAddress(track.getAddress());
                //轨迹经纬度
                if (track.getLocation() != null && track.getLocation().size() == 2) {
                    entity.setLatitude(track.getLocation().get(0));
                    entity.setLongitude(track.getLocation().get(1));
                }
                entity.setGzymc(track.getGzymc());
                entity.setGzylx(track.getGzylx());
                entity.setGzybh(track.getGzybh());
                collisionResultMapper.insert(entity);
                return getUpdateSource(entity);
            });
            //更新感知源
        }).collect(Collectors.toList());
    }

    /**
     * 查询照片
     *
     * @param tableName 表名
     * @param recordId  id
     * @return 结果
     */
    public String[] getPhotos(String tableName, String recordId) {
        try {
            List<String> photoFieldNames = JsonUtil.parseArray(mysqlResultMapper.selectByWarningType(tableName), String.class);
            if (photoFieldNames == null || photoFieldNames.isEmpty()) {
                return new String[0];
            }
            Map<String, Object> track = iGuiJiXinXiBiaoService.selectPhotoByRecordId(tableName, recordId);
            if (track == null || track.isEmpty()) {
                return new String[0];
            }
            List<String> photos = new ArrayList<>();
            for (Map.Entry<String, Object> entry : track.entrySet()) {
                if (photoFieldNames.contains(entry.getKey())) {
                    String url = entry.getValue().toString();
                    photos.add("/oss/photo/forward?base64Url=" + Base64.getEncoder().encodeToString(url.getBytes()));
                }
            }
            return photos.toArray(new String[0]);
        } catch (Exception e) {
            log.error("查询照片失败", e);
            return new String[0];
        }
    }

    private void sendPhoneMessage(CollisionEntity collision) {
        try {
            messageService.sendPhoneMessage(
                    new PhoneMessageVO(Stream.of(collision.getNotifyTel()).toArray(String[]::new),
                            String.format("时空碰撞'%s'已完成，请及时查看！", collision.getName()),
                            3L, collision.getId()));
        } catch (Exception e) {
            log.info("时空碰撞短信通知失败");
        }
    }

    private Source getUpdateSource(CollisionResultEntity entity) {
        //更新感知源
        Source source = new Source();
        source.setId(entity.getGzybh());
        source.setName(entity.getGzymc());
        WarningSourceTypeEnum typeEnum = WarningSourceTypeEnum.cnNameOf(entity.getGzylx());
        source.setType(typeEnum != null ? typeEnum.getEnName() : null);
        source.setDistrict(entity.getDistrict());
        source.setAddress(entity.getAddress());
        source.setLongitude(entity.getLongitude());
        source.setLatitude(entity.getLatitude());
        return source;
    }

    private Long getPersonId(String objectType, String objectValue) {
        switch (objectType) {
            case "身份证":
            case "护照":
                return collisionResultMapper.getPersonIdByIdNumber(objectValue);
            case "手机号":
                return collisionResultMapper.getPersonIdByPhoneNumber(objectValue);
            case "车牌号":
                return collisionResultMapper.getPersonIdByCarNumber(objectValue);
            case "MAC":
            case "IMEI":
            case "IMSI":
                return collisionResultMapper.getPersonIdByVirtualIdentity(objectValue);
            default:
        }
        return null;
    }

    private Integer getDisplayType(String objectType) {
        //身份证 护照 手机号 mac imei imsi 车牌
        switch (objectType) {
            case "身份证":
                return CollisionDisplayTypeEnum.ID_NUMBER.getCode();
            case "护照":
                return CollisionDisplayTypeEnum.PASSPORT.getCode();
            case "手机号":
                return CollisionDisplayTypeEnum.TEL.getCode();
            case "车牌号":
                return CollisionDisplayTypeEnum.CAR.getCode();
            case "MAC":
            case "IMEI":
            case "IMSI":
                return CollisionDisplayTypeEnum.VIRTUAL_IDENTITY.getCode();
            default:
        }
        return null;
    }

}
