package com.trs.police.spacetime.collision.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.JsonUtils;
import com.trs.police.common.core.constant.enums.DelayMessageTypeEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.*;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.WarningSourceVO;
import com.trs.police.common.core.vo.message.ScheduleMessageVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.openfeign.starter.service.ControlService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ScheduleService;
import com.trs.police.spacetime.collision.constant.enums.CollisionDisplayTypeEnum;
import com.trs.police.spacetime.collision.constant.enums.CollisionStatusEnum;
import com.trs.police.spacetime.collision.constant.enums.HitLogicEnum;
import com.trs.police.spacetime.collision.constant.enums.SourceTypeEnum;
import com.trs.police.spacetime.collision.domain.dto.TrailDotDto;
import com.trs.police.spacetime.collision.domain.entity.CollisionAreaEntity;
import com.trs.police.spacetime.collision.domain.entity.CollisionEntity;
import com.trs.police.spacetime.collision.domain.entity.CollisionJobEntity;
import com.trs.police.spacetime.collision.domain.entity.CollisionResultEntity;
import com.trs.police.spacetime.collision.domain.request.CollisionCreateRequest;
import com.trs.police.spacetime.collision.domain.request.TrackCountRequest;
import com.trs.police.spacetime.collision.domain.vo.*;
import com.trs.police.spacetime.collision.domain.vo.CollisionCreateVO.CollisionArea;
import com.trs.police.spacetime.collision.domain.vo.CollisionCreateVO.HitLogic;
import com.trs.police.spacetime.collision.domain.vo.CollisionDetailVO.CollisionResultVO;
import com.trs.police.spacetime.collision.mapper.*;
import com.trs.police.spacetime.collision.properties.CollisionProperties;
import com.trs.police.spacetime.collision.service.CollisionService;
import com.trs.police.spacetime.collision.service.IGuiJiXinXiBiaoService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时空碰撞
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CollisionServiceImpl implements CollisionService {

    @Resource
    private CollisionMapper collisionMapper;
    @Resource
    private CollisionAreaMapper collisionAreaMapper;
    @Resource
    private CollisionJobMapper collisionJobMapper;
    @Resource
    private CollisionProperties collisionProperties;
    @Resource
    private CollisionResultMapper collisionResultMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private ScheduleService scheduleService;
    @Resource
    private ControlService controlService;
    @Resource
    private IGuiJiXinXiBiaoService iGuiJiXinXiBiaoService;
    @Resource
    private CollisionResultImpl collisionResult;
    @Resource
    private MysqlResultMapper mysqlResultMapper;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Long collisionCreate(CollisionCreateVO createVO) {
        CollisionEntity entity = createVO.toEntity();
        collisionMapper.insert(entity);

        List<CollisionAreaEntity> areaEntities = updateArea(entity.getId(), createVO.getAreas());

        if (entity.getStatus().equals(CollisionStatusEnum.RUNNING)) {
            CollisionCreateRequest request = CollisionCreateRequest.toRequest(entity, areaEntities);
            subscribeCollision(entity.getId(), entity.getName(), request);
        }
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void collisionEdit(Long id, CollisionCreateVO createVO) {
        CollisionEntity entity = createVO.toEntity();
        entity.setId(id);
        collisionMapper.updateById(entity);

        collisionAreaMapper.deleteByCollisionId(id);
        List<CollisionAreaEntity> areaEntities = updateArea(id, createVO.getAreas());

        if (entity.getStatus().equals(CollisionStatusEnum.RUNNING)) {
            CollisionCreateRequest request = CollisionCreateRequest.toRequest(entity, areaEntities);
            subscribeCollision(entity.getId(), entity.getName(), request);
        }
    }

    private List<CollisionAreaEntity> updateArea(Long id, List<CollisionArea> areas) {
        return areas.stream().map(area -> {
            CollisionAreaEntity areaEntity = area.toEntity(id);
            collisionAreaMapper.insert(areaEntity);
            return areaEntity;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void collisionDelete(Long id) {
        collisionMapper.deleteById(id);
        collisionAreaMapper.deleteByCollisionId(id);
    }

    @Override
    public void collisionRun(Long id) {
        CollisionEntity entity = collisionMapper.selectById(id);
        entity.setStatus(CollisionStatusEnum.RUNNING);
        collisionMapper.updateById(entity);
        List<CollisionAreaEntity> areaEntities = collisionAreaMapper.selectByCollisionId(id);
        if (entity.getStatus().equals(CollisionStatusEnum.RUNNING)) {
            CollisionCreateRequest request = CollisionCreateRequest.toRequest(entity, areaEntities);
            subscribeCollision(entity.getId(), entity.getName(), request);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void collisionStop(Long id) {
        CollisionEntity entity = collisionMapper.selectById(id);
        entity.setStatus(CollisionStatusEnum.DRAFT);
        collisionMapper.updateById(entity);
    }

    /**
     * processOverdueOnStart<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/5 11:42
     */
    @PostConstruct
    public void processOverdueOnStart() {
        log.info("开始回填LastJobId");
        for (CollisionEntity entity : new LambdaQueryChainWrapper<>(collisionMapper).isNull(CollisionEntity::getLastJobId).list()) {
            log.info("[{}:{}]开始回填LastJobId", entity.getId(), entity.getName());
            if (Objects.isNull(entity.getLastJobId())) {
                new LambdaQueryChainWrapper<>(collisionJobMapper)
                        .eq(CollisionJobEntity::getCollisionId, entity.getId())
                        .orderByDesc(CollisionJobEntity::getId)
                        .page(new Page<>(1, 1))
                        .getRecords()
                        .forEach(it -> {
                            entity.setLastJobId(it.getId());
                            collisionMapper.updateById(entity);
                            log.info("[{}:{}]回填LastJobId[{}]", entity.getId(), entity.getName(), it.getId());
                        });
            }
            log.info("[{}:{}]完成回填LastJobId[{}]", entity.getId(), entity.getName(), entity.getLastJobId());
        }
        log.info("完成回填LastJobId");
        log.info("启动时触发数据库中正在运行任务的超时订阅");
        for (CollisionEntity entity : new LambdaQueryChainWrapper<>(collisionMapper)
                .eq(CollisionEntity::getStatus, CollisionStatusEnum.RUNNING.getCode())
                .list()) {
            if (Objects.isNull(entity.getLastJobId())) {
                new LambdaQueryChainWrapper<>(collisionJobMapper)
                        .eq(CollisionJobEntity::getCollisionId, entity.getId())
                        .orderByDesc(CollisionJobEntity::getId)
                        .page(new Page<>(1, 1))
                        .getRecords()
                        .forEach(it -> {
                            entity.setLastJobId(it.getId());
                            collisionMapper.updateById(entity);
                        });
            }
            if (Objects.nonNull(entity.getLastJobId())) {
                log.info("[{}:{}]订阅超时，jobId[{}]", entity.getId(), entity.getName(), entity.getLastJobId());
                processOverdue(entity.getLastJobId());
            } else {
                log.warn("[{}:{}]没有jobId，无法订阅超时，直接标记失败", entity.getId(), entity.getName());
                entity.setStatus(CollisionStatusEnum.FAIL);
                collisionMapper.updateById(entity);
            }
        }
        log.info("结束超时订阅");
    }

    private void subscribeCollision(Long collisionId, String name, CollisionCreateRequest request) {
        CollisionJobEntity job = new CollisionJobEntity(collisionId, name);
        collisionJobMapper.insert(job);
        request.setJobId(job.getId());
        Map<String, Object> requestMap = new HashMap<>(5);
        requestMap.put("jobName", name);
        requestMap.put("mainClass", "com.trs.ty.engine.spark.controller.NewCollideMain");
        requestMap.put("jarPath", collisionProperties.getJarPath());
        requestMap.put("jarFileName", collisionProperties.getJarFileName());
        requestMap.put("inputParams", JsonUtil.toJsonString(request));
        final String url = collisionProperties.getCollisionCreateUrl();
        String requestStr = JsonUtil.toJsonString(requestMap);
        log.info("时空碰撞请求内容：{}", requestStr);
        String response = OkHttpUtil.getInstance().postData(url, requestStr);
        final CollisionEntity collision = collisionMapper.selectById(collisionId);
        if (StringUtils.isNotBlank(response)) {
            processOverdue(job.getId());
            // 回写最后一次的jobId
            collision.setLastJobId(job.getId());
            collisionMapper.updateById(collision);
            log.info("时空碰撞订阅成功! 订阅id：{} 返回信息：{}", collisionId, response);
        } else {
            collision.setStatus(CollisionStatusEnum.FAIL);
            collisionMapper.updateById(collision);
            job.setFailReason("碰撞任务发起失败！");
            job.setSuccess(false);
            job.setEndTime(LocalDateTime.now());
            collisionJobMapper.updateById(job);
            log.error("时空碰撞订阅发生错误！");
        }
    }

    /**
     * 设置逾期
     *
     * @param jobId 碰撞任务
     */
    private void processOverdue(Long jobId) {
        //签收逾期时限
        ScheduleMessageVO message = new ScheduleMessageVO(OperateModule.COLLISION,
                DelayMessageTypeEnum.EXPIRE,
                jobId, LocalDateTime.now().plusHours(2));
        scheduleService.subscribeDelayJob(message);
    }

    @Override
    public PageResult<CollisionListVO> collisionList(ListParamsRequest request) {
        PageParams pageParams = request.getPageParams();
        Page<CollisionListVO> page = collisionMapper.collisionList(request, pageParams.toPage());
        page.getRecords().forEach(item -> {
            if (JsonUtils.isValidArray(item.getResult())) {
                item.setResultCount(JsonUtil.parseArray(item.getResult(), KeyValueVO.class));
            }
        });
        return PageResult.of(page.getRecords(), pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    @Override
    public CollisionDetailVO collisionDetail(Long id) {
        CollisionDetailVO vo = new CollisionDetailVO();
        CollisionEntity collisionEntity = collisionMapper.selectById(id);
        if (collisionEntity != null) {
            vo.setStatus(collisionEntity.getStatus().getCode());
            vo.setName(collisionEntity.getName());
            vo.setNotifyTel(collisionEntity.getNotifyTel());
            vo.setHitLogic(
                    new HitLogic(HitLogicEnum.codeOf(collisionEntity.getHitLogic()), collisionEntity.getSpacetimeNumber()));
            vo.setSourceType(collisionEntity.getSourceType());
            if (Objects.isNull(collisionEntity.getSourceType()) || collisionEntity.getSourceType().isEmpty()) {
                vo.setSourceTypeName(List.of("全部"));
            } else {
                vo.setSourceTypeName(collisionEntity.getSourceType().stream()
                        .map(sourceType -> {
                            SourceTypeEnum typeEnum = SourceTypeEnum.codeOf(sourceType);
                            return typeEnum == null ? null : typeEnum.getName();
                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }
            vo.setAreas(collisionAreaMapper.selectByCollisionId(id).stream().map(item -> {
                CollisionArea collisionArea = new CollisionArea();
                collisionArea.setAreaName(item.getName());
                collisionArea.setGeometry(item.getGeometry());
                collisionArea.setTimeRanges(JsonUtil.parseArray(item.getTimeRanges(), TimeParams.class));
                collisionArea.setHitLogic(item.getDataCountType());
                return collisionArea;
            }).collect(Collectors.toList()));
            vo.setCollisionResult(getCollisionResultById(id));
            SimpleUserVO userVO =
                    permissionService.findSimpleUser(collisionEntity.getCreateUserId(), collisionEntity.getCreateDeptId());
            vo.setCreateUser(userVO.getUserName());
            vo.setCreateDept(userVO.getDeptShortName());
            vo.setTime(TimeUtil.getSimpleTime(collisionEntity.getCreateTime()));
            final CollisionJobEntity job = collisionJobMapper.getRecentJobByCollisionId(id);
            if (job != null) {
                vo.setRunTime(getRunTime(collisionEntity, job));
                vo.setRunStartTime(job.getStartTime());
            }
            //只有自己发起的需要设置已读
            if (AuthHelper.getNotNullUser().getId().equals(collisionEntity.getCreateUserId())
                    && Boolean.TRUE.equals(collisionEntity.getUnRead())) {
                collisionMapper.updateUnread(id);
            }
        }

        return vo;
    }

    private String getRunTime(CollisionEntity collision, CollisionJobEntity job) {
        if (collision.getStatus().equals(CollisionStatusEnum.DRAFT)) {
            return null;
        }
        final LocalDateTime createTime = job.getStartTime();
        Duration duration;
        if (job.getEndTime() != null) {
            duration = Duration.between(createTime, job.getEndTime());
        } else {
            duration = Duration.between(createTime, LocalDateTime.now());
        }
        return TimeUtil.durationToFullString(duration);
    }

    private List<CollisionResultVO> getCollisionResultById(Long id) {
        CollisionJobEntity job = collisionJobMapper.getRecentJobByCollisionId(id);
        if (job == null || job.getResult() == null) {
            return Collections.emptyList();
        }
        return Arrays.stream(job.getResult()).map(keyValue -> {
            CollisionResultVO vo = new CollisionResultVO();
            vo.setDisplayType(keyValue.getKey());
            vo.setCount(Integer.parseInt(keyValue.getValue()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<CollisionResultDetail> getTrackPage(Long collisionId, String displayType, PageParams pageParams, SearchParams searchParams) {
        CollisionJobEntity job = collisionJobMapper.getRecentJobByCollisionId(collisionId);
        if (job == null) {
            return PageResult.empty(pageParams);
        }
        CollisionDisplayTypeEnum displayTypeEnum = CollisionDisplayTypeEnum.nameOf(displayType);
        if (displayTypeEnum == null) {
            return PageResult.empty(pageParams);
        }
        boolean isPerson = Objects.equals(displayTypeEnum, CollisionDisplayTypeEnum.PERSON);
        Page<CollisionResultDetail> page;
        if (isPerson) {
            //查询人员详情
            page = collisionResultMapper.getResultPageByJobIdAndDisplayTypeWithPersonInfo(job.getId(), displayTypeEnum.getCode(), searchParams, pageParams.toPage());
        } else {
            page = collisionResultMapper.getResultPageByJobIdAndDisplayType(job.getId(), displayTypeEnum.getCode(), searchParams, pageParams.toPage());
        }
        //查询出租车数据
        if (displayTypeEnum.equals(CollisionDisplayTypeEnum.CAR)) {
            List<GroupVo> query = collisionResultMapper.getAllPlateTaxi(job.getId());
            Map<String, Long> allPlateTaxi = Objects.nonNull(query) ? query.stream().collect(Collectors.toMap(GroupVo::getName, GroupVo::getNum)) : new HashMap<>(0);
            page.getRecords().forEach(r -> r.setIsTaxi(allPlateTaxi.getOrDefault(r.getObjectValue(), 0L) > 0));
        }
        return PageResult.of(page.getRecords(), pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    @Override
    public List<List<CollisionTrackResultVO>> collisionDetailPersonCard(Long id, List<CollisionResultDetail> vo) {
        CollisionJobEntity job = collisionJobMapper.getRecentJobByCollisionId(id);
        if (job != null) {
            List<CollisionAreaEntity> areas = collisionAreaMapper.selectByCollisionId(id);
            return vo.stream().map(detail -> {
                detail.setCollisionId(id);
                return getTracksByCertificate(detail, areas, job.getId());
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private List<CollisionTrackResultVO> getTracksByCertificate(CollisionResultDetail certificate, List<CollisionAreaEntity> areas, Long jobId) {
        CollisionTrackResultVO resultAll = new CollisionTrackResultVO();
        resultAll.setAreaName("全部");
        List<CollisionTrackVO> result = collisionResultMapper.getTrackByCollisionResult(certificate, null, jobId);
        result.forEach(item -> processTrackDetail(certificate, item));
        resultAll.setTracks(result);
        List<CollisionTrackResultVO> areaResults = new ArrayList<>();
        areaResults.add(resultAll);

        areas.forEach(area -> {
            CollisionTrackResultVO resultByArea = new CollisionTrackResultVO();
            resultByArea.setAreaName(area.getName());
            List<CollisionTrackVO> trackByArea = collisionResultMapper.getTrackByCollisionResult(certificate, area.getName(), jobId);
            result.forEach(item -> processTrackDetail(certificate, item));
            resultByArea.setTracks(trackByArea);
            areaResults.add(resultByArea);
        });
        return areaResults.stream().filter(area -> !area.getTracks().isEmpty()).collect(Collectors.toList());
    }

    private void processTrackDetail(CollisionResultDetail certificate, CollisionTrackVO track) {
        //添加标识符信息
        track.setCollisionResult(certificate);
        //根据配置返回是否需要显示照片
        track.setHavePhoto(mysqlResultMapper.selectByWarningType(track.getTableName()) != null);
        if ("出租车GPS".equals(track.getGzylx())) {
            WarningSourceVO sourceVO = new WarningSourceVO();
            sourceVO.setType(track.getGzylx());
            sourceVO.setTopType(track.getGzylx());
            sourceVO.setPoint("POINT(" + track.getLongitude() + " " + track.getLatitude() + ")");
            track.setWarningSource(sourceVO);
        }
    }

    @Override
    public void updateCollisionNotifyTel(Long id, String tel) {
        CollisionEntity collisionEntity = collisionMapper.selectById(id);
        if (Objects.nonNull(collisionEntity)) {
            collisionEntity.setNotifyTel(tel);
            collisionMapper.updateById(collisionEntity);
        }
    }

    @Override
    public Integer getCollisionUnReadCount() {
        return collisionMapper.getUnReadCount(AuthHelper.getNotNullSimpleUser().getUserId());
    }

    @Override
    public CollisionListStatisticsVO getCollisionListStatistics(ListParamsRequest request) {
        List<IdNameCountVO> list = collisionMapper.countCollision(request);
        CollisionListStatisticsVO vo = new CollisionListStatisticsVO();
        vo.setAllCount(list.stream().filter(i -> Objects.nonNull(i.getCount())).mapToInt(i -> i.getCount().intValue()).sum());
        vo.setFinishCount(
                list.stream()
                        .filter(i -> Objects.nonNull(i.getCount()))
                        .filter(i -> Objects.equals(String.valueOf(CollisionStatusEnum.FINISH.getCode()), i.getName()))
                        .mapToInt(i -> i.getCount().intValue())
                        .sum()
        );
        vo.setDraftCount(
                list.stream()
                        .filter(i -> Objects.nonNull(i.getCount()))
                        .filter(i -> Objects.equals(String.valueOf(CollisionStatusEnum.DRAFT.getCode()), i.getName()))
                        .mapToInt(i -> i.getCount().intValue())
                        .sum()
        );
        vo.setRunningCount(
                list.stream()
                        .filter(i -> Objects.nonNull(i.getCount()))
                        .filter(i -> Objects.equals(String.valueOf(CollisionStatusEnum.RUNNING.getCode()), i.getName()))
                        .mapToInt(i -> i.getCount().intValue())
                        .sum()
        );
        vo.setFailCount(
                list.stream()
                        .filter(i -> Objects.nonNull(i.getCount()))
                        .filter(i -> Objects.equals(String.valueOf(CollisionStatusEnum.FAIL.getCode()), i.getName()))
                        .mapToInt(i -> i.getCount().intValue())
                        .sum()
        );
        return vo;
    }

    @Override
    public TrackCountVO countTracks(TrackCountRequest request) {
        GeometryVO geometryVO = request.getGeometry();
        Long trackCount = request.getTimeRanges().stream().mapToLong(timeParams -> {
            if ("circle".equals(geometryVO.getType())) {
                Point point = getPoint(geometryVO.getGeometry());
                if (point == null) {
                    return 0L;
                }
                return iGuiJiXinXiBiaoService.countTrackByCircleAndTime(
                        point.getX(),
                        point.getY(),
                        geometryVO.getProperties().getRadius(),
                        TimeUtil.getSubscribeTime(timeParams.getBeginTime()),
                        TimeUtil.getSubscribeTime(timeParams.getEndTime())
                );
            } else if ("line".equals(geometryVO.getType())) {
                List<Polygon> polygons = GeoUtils.lineVoToPolygonList(geometryVO);
                return polygons.stream()
                        .mapToLong(polygon -> iGuiJiXinXiBiaoService.countTrackByAreaAndTime(
                                polygon.toText(),
                                TimeUtil.getSubscribeTime(timeParams.getBeginTime()),
                                TimeUtil.getSubscribeTime(timeParams.getEndTime())
                        )).sum();
            } else {
                return iGuiJiXinXiBiaoService.countTrackByAreaAndTime(
                        geometryVO.getGeometry(),
                        TimeUtil.getSubscribeTime(timeParams.getBeginTime()),
                        TimeUtil.getSubscribeTime(timeParams.getEndTime())
                );
            }
        }).sum();
        var flag = BeanFactoryHolder.getEnv().getProperty("collision.need.count.waring.source", Boolean.class, false);
        Integer sourceCount = flag ? controlService.getAreaOfSourceCount(List.of(request.getGeometry())) : 0;
        return new TrackCountVO(sourceCount, trackCount);
    }

    @Override
    public List<FileInfoVO> collisionDetailPhoto(Long id, Long trackId) {
        CollisionResultEntity track = collisionResultMapper.selectById(trackId);
        if (track.getPhoto() == null && track.getTableName() != null && track.getRecordId() != null) {
            String[] photoUrls = collisionResult.getPhotos(track.getTableName(), track.getRecordId());
            List<FileInfoVO> photoList = new ArrayList<>();
            for (int i = 0; i < photoUrls.length; i++) {
                FileInfoVO p = new FileInfoVO(photoUrls[i]);
                p.setId((long) i + 1);
                photoList.add(p);
            }
            CollisionResultEntity entity = collisionResultMapper.selectById(track.getId());
            entity.setPhoto(photoList.toArray(FileInfoVO[]::new));
            collisionResultMapper.updateById(entity);
            return photoList;
        } else {
            return Arrays.asList(track.getPhoto());
        }
    }

    private static Point getPoint(String wkt) {
        // 解析WKT字符串并创建CRS对象
        try {
            return (Point) new WKTReader(new GeometryFactory()).read(wkt);
        } catch (ParseException e) {
            log.error("parse error! wkt：{}", wkt, e);
        }
        return null;
    }

    @Override
    public List<CollisionTrailDotResultVO> getCollisionTrailDots(List<TrailDotDto> params) {
        final List<TrailDotDto> list = params.stream().filter(a -> StringUtils.isNotEmpty(a.getIdNumber())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("证件号码不能为空！");
        }
        List<CollisionTrailDotResultVO> result = new ArrayList<>();
        for (TrailDotDto param : params) {
            CollisionTrailDotResultVO resultVO = new CollisionTrailDotResultVO();
            resultVO.setIdNumber(param.getIdNumber());
            resultVO.setStartTime(param.getStartTime());
            resultVO.setEndTime(param.getEndTime());
            final List<CollisionTrailDotVO> trailDot = iGuiJiXinXiBiaoService.selectTrailDot(
                    param.getIdNumber(),
                    param.getStartTime(),
                    param.getEndTime()
            );
            resultVO.setList(trailDot);
            result.add(resultVO);
        }
        return result;
    }
}
