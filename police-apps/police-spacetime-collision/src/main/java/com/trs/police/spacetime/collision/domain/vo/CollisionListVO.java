package com.trs.police.spacetime.collision.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.json.serializer.SimpleTimeSerializer;
import com.trs.police.common.core.vo.KeyValueVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 11:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CollisionListVO implements Serializable {

    private static final long serialVersionUID = -1843719923135409063L;

    private Long id;
    private String name;
    private Integer status;
    @JsonSerialize(using = SimpleTimeSerializer.class, nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime time;
    private String createUser;
    private String createDept;
    private String result;
    private List<KeyValueVO> resultCount;
    private Boolean unRead;
    private String notifyTel;
}
