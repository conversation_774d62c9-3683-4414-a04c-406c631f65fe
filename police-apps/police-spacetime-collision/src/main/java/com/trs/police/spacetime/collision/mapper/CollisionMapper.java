package com.trs.police.spacetime.collision.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.spacetime.collision.domain.entity.CollisionEntity;
import com.trs.police.spacetime.collision.domain.vo.CollisionListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 时空碰撞mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CollisionMapper extends BaseMapper<CollisionEntity> {

    /**
     * 时空碰撞列表
     *
     * @param request 请求参数
     * @param page    分页参数
     * @return {@link CollisionListVO}
     */
    Page<CollisionListVO> collisionList(@Param("request") ListParamsRequest request, Page<CollisionListVO> page);

    /**
     * countCollision<BR>
     *
     * @param request 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/5 10:37
     */
    List<IdNameCountVO> countCollision(@Param("request") ListParamsRequest request);

    /**
     * 查询用户未读碰撞数量
     *
     * @param userId userId
     * @return 数量
     */
    @Select("select count(0) from t_spacetime_collision where create_user_id=#{userId} and status=3 and un_read=1")
    Integer getUnReadCount(@Param("userId") Long userId);

    /**
     * 设置已读
     *
     * @param id 碰撞id
     */
    @Update("update t_spacetime_collision set un_read = 0 where id = #{id}")
    void updateUnread(@Param("id") Long id);
}
