package com.trs.police.spacetime.collision.service;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.spacetime.collision.domain.dto.TrailDotDto;
import com.trs.police.spacetime.collision.domain.request.TrackCountRequest;
import com.trs.police.spacetime.collision.domain.vo.*;

import java.util.List;

/**
 * 时空碰撞
 *
 * <AUTHOR>
 */
public interface CollisionService {

    /**
     * 创建碰撞
     *
     * @param createVO vo
     * @return id
     */
    Long collisionCreate(CollisionCreateVO createVO);

    /**
     * 修改碰撞
     *
     * @param id       碰撞id
     * @param createVO vo
     */
    void collisionEdit(Long id, CollisionCreateVO createVO);

    /**
     * 删除碰撞
     *
     * @param id 碰撞id
     */
    void collisionDelete(Long id);

    /**
     * 运行碰撞
     *
     * @param id 碰撞id
     */
    void collisionRun(Long id);

    /**
     * 停止碰撞
     *
     * @param id 碰撞id
     */
    void collisionStop(Long id);

    /**
     * 时空碰撞列表
     *
     * @param request 请求参数
     * @return {@link  CollisionListVO}
     */
    PageResult<CollisionListVO> collisionList(ListParamsRequest request);

    /**
     * 时空碰撞详情
     *
     * @param id 碰撞id
     * @return {@link CollisionDetailVO}
     */
    CollisionDetailVO collisionDetail(Long id);

    /**
     * 查询结果
     *
     * @param collisionId  id
     * @param displayType  类型
     * @param pageParams   分页
     * @param searchParams 检索
     * @return 结果
     */
    PageResult<CollisionResultDetail> getTrackPage(Long collisionId, String displayType, PageParams pageParams, SearchParams searchParams);

    /**
     * 时空碰撞人员tab详情
     *
     * @param id 时空碰撞id
     * @param vo 碰撞结果
     * @return {@link   CollisionTrackVO}
     */
    List<List<CollisionTrackResultVO>> collisionDetailPersonCard(Long id, List<CollisionResultDetail> vo);

    /**
     * 更新碰撞通知电话
     *
     * @param id  碰撞id
     * @param tel 电话
     */
    void updateCollisionNotifyTel(Long id, String tel);

    /**
     * 获取碰撞未读数量
     *
     * @return 未读数量
     */
    Integer getCollisionUnReadCount();

    /**
     * 列表统计数据
     *
     * @param request 参数
     * @return 数据
     */
    CollisionListStatisticsVO getCollisionListStatistics(ListParamsRequest request);

    /**
     * 统计数据
     *
     * @param request 参数
     * @return {@link TrackCountVO}
     */
    TrackCountVO countTracks(TrackCountRequest request);

    /**
     * 照片
     *
     * @param id      碰撞id
     * @param trackId 轨迹id
     * @return 结果
     */
    List<FileInfoVO> collisionDetailPhoto(Long id, Long trackId);

    /**
     * 获取只当证件号码的轨迹点位列表
     *
     * @param params 请求参数
     * @return 轨迹点位结果
     */
    List<CollisionTrailDotResultVO> getCollisionTrailDots(List<TrailDotDto> params);
}
