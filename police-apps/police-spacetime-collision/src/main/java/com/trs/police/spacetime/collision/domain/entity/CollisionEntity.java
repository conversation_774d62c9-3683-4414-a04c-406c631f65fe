package com.trs.police.spacetime.collision.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import com.trs.police.spacetime.collision.constant.enums.CollisionStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 时空碰撞entity
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_spacetime_collision", autoResultMap = true)
public class CollisionEntity extends AbstractBaseEntity {

    private static final long serialVersionUID = -5468825788781026631L;
    /**
     * 碰撞名称
     */
    private String name;
    /**
     * 状态
     */
    private CollisionStatusEnum status;
    /**
     * 命中逻辑
     */
    private Integer hitLogic;
    /**
     * 命中逻辑区域数量
     */
    private Integer spacetimeNumber;
    /**
     * 感知源类型
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> sourceType;

    /**
     * 通知电话
     */
    private String notifyTel;

    /**
     * 未读
     */
    private Boolean unRead;

    /**
     * 最后运行的任务ID
     */
    private Long lastJobId;
}
