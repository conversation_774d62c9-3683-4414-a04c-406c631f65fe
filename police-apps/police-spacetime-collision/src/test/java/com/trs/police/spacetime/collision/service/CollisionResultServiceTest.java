package com.trs.police.spacetime.collision.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class CollisionResultServiceTest {

    @Autowired
    private CollisionResultService service;

    @Test
    void processCollision() {
        service.processCollision("{\n" +
                "\t\"content\": \"{\\\"code\\\":200,\\\"message\\\":\\\"成功执行\\\",\\\"success\\\":true,\\\"data\\\":{\\\"jobId\\\":\\\"1\\\",\\\"jobName\\\":\\\"1\\\"}}\",\n" +
                "\t\"createTime\": 1748916804450,\n" +
                "\t\"messageType\": \"tb_new_collide_service2\",\n" +
                "\t\"serialId\": \"540832f5-a60d-4c03-9d83-f1cab0463790\"\n" +
                "}");
    }
}