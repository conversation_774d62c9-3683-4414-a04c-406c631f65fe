<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.risk.mapper.TraceableDataJqMapper">

    <resultMap id="dataBaseMap" type="com.trs.police.risk.domain.vo.traceableData.TraceableDataBaseVO">
        <result column="code" property="code"/>
        <result column="tel" property="tel"/>
        <result column="content" property="content"/>
        <result column="tags" property="tags"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="time" property="time"/>
        <result column="scoreKeyWords" property="scoreKeyWords"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="idNumber" property="idNumber"/>
    </resultMap>

    <resultMap id="jqMap" extends="dataBaseMap" type="com.trs.police.risk.domain.vo.traceableData.TraceableDataJqVO">
        <result column="address" property="address"/>
<!--        <collection column="{code=code}" property="feedbacks"-->
<!--            ofType="com.trs.police.risk.domain.vo.traceableData.FeedbackVO"-->
<!--            select="getFeedbacks"/>-->
    </resultMap>

    <resultMap id="traceableDataListMap" type="com.trs.police.risk.domain.vo.traceableData.TraceableDataListVO">
        <result column="personId" property="personId"/>
        <result column="code" property="code"/>
        <result column="tel" property="tel"/>
        <result column="content" property="content"/>
        <result column="tags" property="tags"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="time" property="time"/>
        <result column="feedbacks" property="feedbacks"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="address" property="address"/>
        <result column="isCurrent" property="isCurrent"/>
        <result column="isRelated" property="isRelated"/>
        <result column="relationId" property="relatedId"/>
        <result column="sourceCode" property="sourceCode"/>
        <result column="score" property="score"/>
        <result column="idNumber" property="idNumber"/>
        <result column="relationType" property="relationType"/>
        <result column="policeKind" property="policeKind"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="scoreKeyWords" property="scoreKeyWords"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
<!--        <association column="{code=source,type=sourceType}" property="source"-->
<!--            javaType="com.trs.police.common.core.vo.CodeNameVO"-->
<!--            select="com.trs.police.common.core.mapper.CommonMapper.getDict"/>-->
<!--        <association column="relationId=relationId" property="relatedType"-->
<!--            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"-->
<!--            javaType="com.trs.police.risk.domain.vo.ListSourceVO"-->
<!--            select="getRelatedType"/>-->
    </resultMap>

    <resultMap id="currentClueSourceDetailMap" type="com.trs.police.risk.domain.vo.traceableData.ClueSourceDetailVO">
        <result column="id" property="clueId"/>
        <association column="{code=source,type=sourceType}" property="source"
                     javaType="com.trs.police.common.core.vo.CodeNameVO"
                     select="com.trs.police.common.core.mapper.CommonMapper.getDict"/>
    </resultMap>
    <resultMap id="getFeedbackMap" type="com.trs.police.risk.domain.vo.FeedbackTimeVO">
        <result column="jjdbh" property="jjdbh"/>
        <result column="fksj" property="fksj"/>
        <result column="cjczqk" property="cjczqk"/>
    </resultMap>

    <resultMap id="traceableDataJqListMap" type="com.trs.police.risk.domain.vo.traceableData.TraceableDataListVO"
        extends="traceableDataListMap">
<!--        <collection column="{code=code}" property="feedbacks"-->
<!--            ofType="java.lang.String"-->
<!--            javaType="java.util.List"-->
<!--            select="getFeedbackContent"/>-->
<!--        <collection column="{code=code}" property="feedbackTime"-->
<!--                    javaType="java.util.Date"-->
<!--                    select="getFeedbackTime"/>-->
    </resultMap>

    <resultMap id="scoreMap" type="com.trs.police.risk.domain.vo.traceableData.TraceableDataScoreVO">
        <result column="code" property="code"/>
        <result column="score" property="score"/>
        <result column="scoreKeyWords" property="scoreKeyWords"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="scoreDetail" property="scoreDetail"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="tags" property="tags"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="sourceType" property="sourceType"
            typeHandler="com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler"/>
        <result column="relationType" property="relationType"/>
    </resultMap>

    <select id="getRelatedType" resultType="com.trs.police.risk.domain.vo.ListSourceVO">
        select
                r.id                                                                                as id,
                r.related_type                                                                      as code,
               (select name from t_dict where type = 'risk_related_type' and code = r.related_type) as name,
               (select name from t_dept where id = r.create_dept_id)                                as deptName,
               (select real_name from t_user where id = r.create_user_id)                           as userName,
               r.create_time                                                                        as time
        from t_risk_traceable_data_relation r
        where r.id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>


    <select id="getByCode" resultMap="jqMap">
        select t.JJDBH                   as code,
               t.BJDH                    as tel,
               t.BJNR                    as content,
               t.TAGS ->> '$[*].tagName' as tags,
               <if test="feedbackTimeOffsetEnabled">
                   DATE_ADD(t.BJSJ , INTERVAL 8 HOUR)
               </if>
               <if test="!feedbackTimeOffsetEnabled">
                   t.BJSJ
               </if>
               as time,
               t.SCORE_KEY_WORDS         as scoreKeyWords,
               t.JQDZ                    as address,
               t.BJRZJHM                 as idNumber
        from t_profile_sthy t
        where t.JJDBH = #{code} order by id desc limit 1
    </select>

    <select id="getRiskTraceableData"
        resultMap="traceableDataJqListMap">
        <bind name="searchParams" value="params.searchParams"/>
        <bind name="filterParams" value="params.filterParams"/>
        select
        tpp.id as personId,
        t.JJDBH as code,
        t.BJDH as tel,
        t.BJNR as content,
        ifnull(t.TAGS ->> '$[*].tagName','[]') as tags,
        <if test="timeOffsetEnabled">
            DATE_ADD(t.BJSJ , INTERVAL 8 HOUR)
        </if>
        <if test="!timeOffsetEnabled">
            t.BJSJ
        </if>
        as time,
        ifnull(t.SCORE_KEY_WORDS,'[]') as scoreKeyWords,
        t.JQDZ as address,
        ifnull(r.is_current, 0) as isCurrent,
        if(r.id is null,0,1) as isRelated,
        r.id as relationId,
        ifnull(t.source_type, 2) as sourceCode,
        'risk_source' as sourceType,
        ifnull(t.score_detail ->> '$.contentTotal', 0) as score,
        r.relation_type as relationType,
        t.BJRZJHM as idNumber,
        tpp.police_kind as policeKind
        from t_profile_sthy t
        left join t_profile_person tpp on tpp.id_number = t.BJRZJHM
        left join (select * from t_risk_traceable_data_relation where risk_id = #{riskId} and (source_type = 2 or source_type=4)) r
        on r.code = t.JJDBH
        where
        1 = 1
        <if test="tel != null and tel != ''">
            and t.BJDH = #{tel}
        </if>
        <if test="idNumber != null and idNumber != ''">
            and t.BJRZJHM = #{idNumber}
        </if>
        and t.BJNR is not null
        <foreach collection="filterParams" item="filterParam">
            <if test="filterParam.key == 'relationType'">
                and r.related_type = #{filterParam.value}
            </if>
            <if test="filterParam.key == 'isRelated'">
                and r.id is not null
            </if>
            <if test="filterParam.key == 'isIdNumber'">
                and (r.relation_type = 'idCard' or r.relation_type = 'idCardAndPhone')
            </if>
            <if test="filterParam.key == 'tag'">
                and JSON_OVERLAPS(t.tags ->> '$[*].tagId',(select JSON_ARRAYAGG(id) from t_risk_label where pid =
                #{filterParam.value}
                or id = #{filterParam.value}))
            </if>
            <if test="filterParam.key == 'source'">
                    and t.source_type = #{filterParam.value}
            </if>
            <if test="filterParam.key == 'bjsj' and filterParam.getProcessedValue().isAll() == false">
                <bind name="timeParam" value="filterParam.getProcessedValue()"/>
                and
                <if test="timeOffsetEnabled">
                    DATE_ADD(t.BJSJ, INTERVAL 8 HOUR)
                </if>
                <if test="!timeOffsetEnabled">
                    t.BJSJ
                </if>
                between #{timeParam.beginTime} and #{timeParam.endTime}
            </if>
<!--            <if test="filterParam.key == 'startTime'">-->
<!--                and-->
<!--                <if test="timeOffsetEnabled">-->
<!--                    DATE_ADD(t.BJSJ, INTERVAL 8 HOUR) >= #{filterParam.value}-->
<!--                </if>-->
<!--                <if test="!timeOffsetEnabled">-->
<!--                    t.BJSJ >= #{filterParam.value}-->
<!--                </if>-->
<!--            </if>-->
<!--            <if test="filterParam.key == 'endTime'">-->
<!--                and-->
<!--                <if test="timeOffsetEnabled">-->
<!--                    DATE_ADD(t.BJSJ, INTERVAL 8 HOUR) &lt;= #{filterParam.value}-->
<!--                </if>-->
<!--                <if test="!timeOffsetEnabled">-->
<!--                    t.BJSJ &lt;= #{filterParam.value}-->
<!--                </if>-->
<!--            </if>-->
        </foreach>
        <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="searchValue" value="'%' + searchParams.searchValue.trim() + '%'"/>
            <choose>
                <when test="searchParams.searchField ==  'fullText'">
                    and ( t.BJNR like #{searchValue}  or exists (select f.id from t_profile_sthy_fkxx f where f.jjdbh = t.jjdbh and f.cjczqk like  #{searchValue}))
                </when>
                <when test="searchParams.searchField == 'feedback'">
                    and exists (select f.id from t_profile_sthy_fkxx f where f.jjdbh = t.jjdbh and f.cjczqk like  #{searchValue})
                </when>
                <when test="searchParams.searchField == 'content'">
                    and t.BJNR like #{searchValue}
                </when>
            </choose>
        </if>
    </select>
    <select id="getScoreByCode" resultMap="scoreMap">
        select t.JJDBH           as code,
               t.TAGS            as tags,
               t.SCORE           as score,
               t.SCORE_DETAIL    as scoreDetail,
               t.SCORE_KEY_WORDS as scoreKeyWords,
               t.SOURCE_TYPE     as sourceType,
               t.relation_type   as relationType
        from t_profile_sthy t
        where t.JJDBH = #{code}
    </select>

    <select id="getFeedbacks" resultType="com.trs.police.risk.domain.vo.traceableData.FeedbackVO">
        select
        (select short_name from t_dept where code = t.fkdwdm) as dept,
        <if test="feedbackTimeOffsetEnabled">
            DATE_ADD(t.fksj , INTERVAL 8 HOUR)
        </if>
        <if test="!feedbackTimeOffsetEnabled">
            t.fksj
        </if>
        as time,
        t.cjczqk                                              as content,
        t.fkdwdm                                             as deptCode
        from t_profile_sthy_fkxx t
        where t.jjdbh = #{code}
          and t.jqcljgsm is not null
          and t.jqcljgsm != ''
        order by t.fksj desc
        limit 1
    </select>


    <select id="getFeedbackContent" resultType="java.lang.String">
        select t.cjczqk
        from t_profile_sthy_fkxx t
        where t.jjdbh = #{code}
        and t.jqcljgsm is not null
        and t.jqcljgsm != ''
        order by t.fksj desc limit 1
    </select>

    <select id="getFeedbackTime" resultMap="getFeedbackMap">
        select t.jjdbh,t.cjczqk,t.fksj
        from t_profile_sthy_fkxx t
        where t.jjdbh in
        <foreach collection="codeList" separator="," close=")" open="(" item="code">
            #{code}
        </foreach>
        and t.jqcljgsm is not null
        and t.jqcljgsm != ''
        order by t.fksj desc
    </select>



    <update id="updateScore">
        update t_profile_sthy
        set SCORE           = #{data.score},
            SCORE_DETAIL    = #{data.scoreDetail,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            SCORE_KEY_WORDS = #{data.scoreKeyWords,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            TAGS            = #{data.tags,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            relation_type   = #{data.relationType}
        where JJDBH = #{data.code}
    </update>

    <select id="getRiskTraceableDataTagCode" resultType="java.lang.String">
        select
            ifnull(t.tags ->> '$[*].tagId','[]') as tags
        from t_profile_sthy t
        join (select * from t_risk_traceable_data_relation where risk_id = #{riskId} and (source_type = 2 or source_type = 4)) r on r.code = t.JJDBH
        where is_current = 1
    </select>

    <select id="getRiskBaseInfo" resultType="com.trs.police.risk.domain.vo.RiskReportVO">
        select
        (select tps.cjczqk from t_profile_sthy_fkxx tps where tr.code = tps.JJDBH and tps.cjczqk is not null order by tps.fksj desc limit 1) as riskContent,
        r.risk_code as riskCode,
        (select name from t_dict td WHERE r.risk_type = td.code and type = "risk_type") as riskType
        from
        t_risk r
        left join t_risk_traceable_data_relation tr on r.id=tr.risk_id and tr.is_current=1
        WHERE
        r.id = #{id};
    </select>

    <select id="getRiskTraceableDataTotal" resultType="java.lang.Long">
    <bind name="searchParams" value="params.searchParams"/>
    <bind name="filterParams" value="params.filterParams"/>
        select count(DISTINCT JJDBH)
        from (
        select
        t.JJDBH
        from t_profile_sthy t
        left join t_profile_person tpp on tpp.id_number = t.BJRZJHM
        left join (select * from t_risk_traceable_data_relation where risk_id = #{riskId} and (source_type = 2 or source_type=4)) r
        on r.code = t.JJDBH
        where
        1 = 1
        <if test="tel != null and tel != ''">
            and t.BJDH = #{tel}
        </if>
        and t.BJNR is not null
        <foreach collection="filterParams" item="filterParam">
            <if test="filterParam.key == 'relationType'">
                and r.related_type = #{filterParam.value}
            </if>
            <if test="filterParam.key == 'isRelated'">
                and r.id is not null
            </if>
            <if test="filterParam.key == 'isIdNumber'">
                and (r.relation_type = 'idCard' or r.relation_type = 'idCardAndPhone')
            </if>
            <if test="filterParam.key == 'tag'">
                and JSON_OVERLAPS(t.tags ->> '$[*].tagId',(select JSON_ARRAYAGG(id) from t_risk_label where pid =
                #{filterParam.value}
                or id = #{filterParam.value}))
            </if>
            <if test="filterParam.key == 'source'">
                and t.source_type = #{filterParam.value}
            </if>
            <if test="filterParam.key == 'bjsj' and filterParam.getProcessedValue().isAll() == false">
                <bind name="timeParam" value="filterParam.getProcessedValue()"/>
                and
                <if test="timeOffsetEnabled">
                    DATE_ADD(t.BJSJ, INTERVAL 8 HOUR)
                </if>
                <if test="!timeOffsetEnabled">
                    t.BJSJ
                </if>
                between #{timeParam.beginTime} and #{timeParam.endTime}
            </if>
        </foreach>
        <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="searchValue" value="'%' + searchParams.searchValue.trim() + '%'"/>
            <choose>
                <when test="searchParams.searchField ==  'fullText'">
                    and ( t.BJNR like #{searchValue}  or exists (select f.id from t_profile_sthy_fkxx f where f.jjdbh = t.jjdbh and f.cjczqk like  #{searchValue}))
                </when>
                <when test="searchParams.searchField == 'feedback'">
                    and exists (select f.id from t_profile_sthy_fkxx f where f.jjdbh = t.jjdbh and f.cjczqk like  #{searchValue})
                </when>
                <when test="searchParams.searchField == 'content'">
                    and t.BJNR like #{searchValue}
                </when>
            </choose>
        </if>
        union all
        select
        t.JJDBH
        from t_profile_sthy t
        left join t_profile_person tpp on tpp.id_number = t.BJRZJHM
        left join (select * from t_risk_traceable_data_relation where risk_id = #{riskId} and (source_type = 2 or source_type=4)) r
        on r.code = t.JJDBH
        where
        r.id is not null
        <if test="idNumber != null and idNumber != ''">
            and t.BJRZJHM = #{idNumber}
        </if>
        and t.BJNR is not null
        <foreach collection="filterParams" item="filterParam">
            <if test="filterParam.key == 'relationType'">
                and r.related_type = #{filterParam.value}
            </if>
            <if test="filterParam.key == 'isRelated'">
                and r.id is not null
            </if>
            <if test="filterParam.key == 'isIdNumber'">
                and (r.relation_type = 'idCard' or r.relation_type = 'idCardAndPhone')
            </if>
            <if test="filterParam.key == 'tag'">
                and JSON_OVERLAPS(t.tags ->> '$[*].tagId',(select JSON_ARRAYAGG(id) from t_risk_label where pid =
                #{filterParam.value}
                or id = #{filterParam.value}))
            </if>
            <if test="filterParam.key == 'source'">
                and t.source_type = #{filterParam.value}
            </if>
            <if test="filterParam.key == 'bjsj' and filterParam.getProcessedValue().isAll() == false">
                <bind name="timeParam" value="filterParam.getProcessedValue()"/>
                and
                <if test="timeOffsetEnabled">
                    DATE_ADD(t.BJSJ, INTERVAL 8 HOUR)
                </if>
                <if test="!timeOffsetEnabled">
                    t.BJSJ
                </if>
                between #{timeParam.beginTime} and #{timeParam.endTime}
            </if>
        </foreach>
        <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="searchValue" value="'%' + searchParams.searchValue.trim() + '%'"/>
            <choose>
                <when test="searchParams.searchField ==  'fullText'">
                    and ( t.BJNR like #{searchValue}  or exists (select f.id from t_profile_sthy_fkxx f where f.jjdbh = t.jjdbh and f.cjczqk like  #{searchValue}))
                </when>
                <when test="searchParams.searchField == 'feedback'">
                    and exists (select f.id from t_profile_sthy_fkxx f where f.jjdbh = t.jjdbh and f.cjczqk like  #{searchValue})
                </when>
                <when test="searchParams.searchField == 'content'">
                    and t.BJNR like #{searchValue}
                </when>
            </choose>
        </if>
        ) as temp;
    </select>


    <select id="getRiskTraceableDataTotalV2" resultType="java.lang.Long">
        <bind name="searchParams" value="params.searchParams"/>
        <bind name="filterParams" value="params.filterParams"/>
        select count(DISTINCT JJDBH)
        from (
        select
        t.JJDBH
        from t_profile_sthy t
        left join t_profile_person tpp on tpp.id_number = t.BJRZJHM
        left join (select * from t_risk_traceable_data_relation where risk_id = #{riskId} and (source_type = 2 or source_type=4)) r
        on r.code = t.JJDBH
        where
       1 = 1
        <if test="tel != null and tel != ''">
            and t.BJDH = #{tel}
        </if>
        <if test="idNumber != null and idNumber != ''">
            and t.BJRZJHM = #{idNumber}
        </if>
        and t.BJNR is not null
        <foreach collection="filterParams" item="filterParam">
            <if test="filterParam.key == 'relationType'">
                and r.related_type = #{filterParam.value}
            </if>
            <if test="filterParam.key == 'isRelated'">
                and r.id is not null
            </if>
            <if test="filterParam.key == 'isIdNumber'">
                and (r.relation_type = 'idCard' or r.relation_type = 'idCardAndPhone')
            </if>
            <if test="filterParam.key == 'tag'">
                and JSON_OVERLAPS(t.tags ->> '$[*].tagId',(select JSON_ARRAYAGG(id) from t_risk_label where pid =
                #{filterParam.value}
                or id = #{filterParam.value}))
            </if>
            <if test="filterParam.key == 'source'">
                and t.source_type = #{filterParam.value}
            </if>
            <if test="filterParam.key == 'bjsj' and filterParam.getProcessedValue().isAll() == false">
                <bind name="timeParam" value="filterParam.getProcessedValue()"/>
                and
                <if test="timeOffsetEnabled">
                    DATE_ADD(t.BJSJ, INTERVAL 8 HOUR)
                </if>
                <if test="!timeOffsetEnabled">
                    t.BJSJ
                </if>
                between #{timeParam.beginTime} and #{timeParam.endTime}
            </if>
        </foreach>
        <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="searchValue" value="'%' + searchParams.searchValue.trim() + '%'"/>
            <choose>
                <when test="searchParams.searchField ==  'fullText'">
                    and ( t.BJNR like #{searchValue}  or exists (select f.id from t_profile_sthy_fkxx f where f.jjdbh = t.jjdbh and f.cjczqk like  #{searchValue}))
                </when>
                <when test="searchParams.searchField == 'feedback'">
                    and exists (select f.id from t_profile_sthy_fkxx f where f.jjdbh = t.jjdbh and f.cjczqk like  #{searchValue})
                </when>
                <when test="searchParams.searchField == 'content'">
                    and t.BJNR like #{searchValue}
                </when>
            </choose>
        </if>
        ) as temp;
    </select>

</mapper>