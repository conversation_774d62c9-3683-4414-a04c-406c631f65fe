package com.trs.police.comparison.api.service.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.comparison.api.config.properties.CompareSubscriptionProperties;
import com.trs.police.comparison.api.dao.SubscribeGroupInfoMapper;
import com.trs.police.comparison.api.dao.SubscribeGroupMemberMapper;
import com.trs.police.comparison.api.dao.SubscribeInfoMapper;
import com.trs.police.comparison.api.entity.SubscribeGroupInfo;
import com.trs.police.comparison.api.entity.SubscribeGroupMember;
import com.trs.police.comparison.api.entity.SubscribeInfo;
import com.trs.police.comparison.api.exception.BizRuntimeException;
import com.trs.police.comparison.api.entity.request.IdentifierPair;
import com.trs.police.comparison.api.entity.request.RedisValueValue;
import com.trs.police.comparison.api.entity.vo.SubscribeGroupMemberPerIdentifierVo;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;


import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * 比对订阅缓存 redis实现
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Slf4j
public class CompareSubscriptionCache {

    @Resource
    private CompareSubscriptionProperties compareSubscriptionProperties;

    @Resource
    private SubscribeInfoMapper subscribeInfoMapper;

    @Resource
    private SubscribeGroupInfoMapper subscribeGroupInfoMapper;

    @Resource
    private SubscribeGroupMemberMapper subscribeGroupMemberMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private static final String REDIS_KEY_SEPARATOR = ":";

    //-------------------人员布控缓存start-------------------

    /**
     * 取消比对订阅时更新缓存
     *
     * @param subscribeInfoList
     * @return
     */
    public boolean cancelSubscribeInfo(List<SubscribeInfo> subscribeInfoList) {
        String hashKey =
            String.join(REDIS_KEY_SEPARATOR, compareSubscriptionProperties.getRedisKeyPrefix(), "event", "9");
        HashOperations<String, Object, Object> hashOperations = redisTemplate.opsForHash();
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();

        try {
            for (SubscribeInfo subscribeInfo : subscribeInfoList) {
                // 9是表达式，需单独处理
                if (9 == subscribeInfo.getIdentifierType()) {
                    String mapKey = subscribeInfo.getIdentifier();
                    String value = (String) hashOperations.get(hashKey, mapKey);
                    if(value == null){
                        log.error("取消比对订阅时更新缓存失败 redis 中 key {} 不存在", hashKey);
                        continue;
                    }
                    JSONObject valueJson = (JSONObject) JSON.parse(value);
                    if (valueJson.containsKey(subscribeInfo.getUserName())) {
                        valueJson.remove(subscribeInfo.getUserName());
                        if (valueJson.isEmpty()) {
                            hashOperations.delete(hashKey, mapKey);
                        } else {
                            hashOperations.put(hashKey, mapKey, valueJson.toJSONString());
                        }
                    }
                } else {
                    String key = String.join(
                        REDIS_KEY_SEPARATOR,
                        compareSubscriptionProperties.getRedisKeyPrefix(),
                        "event",
                        String.valueOf(subscribeInfo.getIdentifierType()),
                        subscribeInfo.getIdentifier());
                    String value = valueOperations.get(key);
                    if (value == null){
                        log.error("取消比对订阅时更新缓存失败 redis 中 key {} 不存在", key);
                        continue;
                    }
                    log.info("取消比对订阅时更新缓存 username {} key {} value {}", subscribeInfo.getUserName(), key, value);
                    JSONObject valueJson = (JSONObject) JSON.parse(value);
                    if (subscribeInfo.getUserName() != null && valueJson.containsKey(subscribeInfo.getUserName())) {
                        // 找到布控code
                        JSONArray jsonArray = valueJson.getJSONArray(subscribeInfo.getUserName());
                        if (jsonArray == null) {
                            log.error("取消比对订阅时更新缓存失败 redis 中 key {} 不存在", key);
                            return false;
                        }

                        Iterator<JSONObject> iterator = jsonArray.iterator();
                        while(iterator.hasNext()) {
                            JSONObject jsonObject = iterator.next();
                            // 清理指定布控code
                            if (jsonObject != null && jsonObject.getString("subscribeCode") != null && jsonObject.getString("subscribeCode").equals(subscribeInfo.getSubscribeCode())) {
                                iterator.remove();
                            }
                        }
                        // 如果用户下的订阅信息为空，则删除该用户的配置
                        if (jsonArray.isEmpty()) {
                            valueJson.remove(subscribeInfo.getUserName());
                        }
                        if (valueJson.isEmpty()) {
                            redisTemplate.delete(key);
                        } else {
                            valueOperations.set(key, valueJson.toJSONString());
                        }
                    }
                }
            }
        } catch (Throwable e) {
            log.error("比对订阅缓存更新失败：", e);
            return false;
        }
        return true;
    }

    /**
     * 更新缓存
     *
     * @param subscribeInfos
     * @return
     */
    public boolean updateCacheForSubscribeInfo(List<SubscribeInfo> subscribeInfos) {
        return updateCacheFor(
            subscribeInfos.stream()
                .map(
                    subscribeInfo ->
                        new IdentifierPair(
                            subscribeInfo.getIdentifierType(), subscribeInfo.getIdentifier()))
                .distinct()
                .collect(Collectors.toList()));
    }

    /**
     * 更新缓存
     *
     * @param identifierPairs
     * @return
     */
    public boolean updateCacheFor(List<IdentifierPair> identifierPairs) {
        // 按照布控特征值查询数据库状态
        final List<SubscribeInfo> subscribeInfos =
            subscribeInfoMapper.selectListByIdentifierPairs(identifierPairs);

        try {
            // 按照特征值分组的订阅数据
            final Map<Pair<Integer, String>, List<SubscribeInfo>> identifySubscribeInfoMap =
                subscribeInfos.stream()
                    .collect(
                        Collectors.groupingBy(
                            d ->  Pair.of(d.getIdentifierType(), d.getIdentifier())));
            // 生成redis存储结构数据
            final ConcurrentMap<Pair<Integer, String>, String> redisInfo =
                identifySubscribeInfoMap.entrySet().stream()
                    .collect(
                        Collectors.toConcurrentMap(
                            Map.Entry::getKey,
                            entry -> {
                                // 统一特征值的订阅数据按照用户分组
                                final Map<String, List<SubscribeInfo>> identifySubscribeInfoOfUserMap =
                                    entry.getValue().stream()
                                        .collect(Collectors.groupingBy(SubscribeInfo::getUserName));

                                // 将分组的value 转换为符合redis存储要求的 json 字符串 <用户id,用户对该特征值的订阅信息>
                                final Map<String, List<RedisValueValue>> subscribeInfoOfUserMap =
                                    identifySubscribeInfoOfUserMap.entrySet().stream()
                                        .collect(
                                            Collectors.toMap(
                                                Map.Entry::getKey,
                                                stringListEntry ->
                                                    stringListEntry.getValue().stream()
                                                        .map(RedisValueValue::new)
                                                        .collect(Collectors.toList())));

                                return JSON.toJSONString(subscribeInfoOfUserMap);
                            }));

            final ValueOperations<String, String> redisValueOperation = redisTemplate.opsForValue();
            final HashOperations<String, Object, Object> redisHashOperation = redisTemplate.opsForHash();
            final String hashKey =
                String.join(REDIS_KEY_SEPARATOR, compareSubscriptionProperties.getRedisKeyPrefix(), "event", "9");
            redisInfo.forEach(
                (k, v) -> {
                    final LocalDateTime maxDateTime =
                        identifySubscribeInfoMap.get(k).stream()
                            .map(SubscribeInfo::getSubscribeEndTime)
                            .max(LocalDateTime::compareTo)
                            .orElseThrow(() -> new BizRuntimeException("布控列表无最大有效期限,未未知异常", 400));
                    final Duration duration = Duration.between(LocalDateTime.now(), maxDateTime);
                    if (9 == k.getFirst()) {
                        redisHashOperation.put(hashKey, k.getSecond(), v);
                    } else {
                        redisValueOperation.set(
                            String.join(
                                REDIS_KEY_SEPARATOR,
                                compareSubscriptionProperties.getRedisKeyPrefix(),
                                "event",
                                String.valueOf(k.getFirst()),
                                k.getSecond()),
                            v,
                            // 添加一个小时随机延迟时间,避免redis数据集中过期
                            duration.abs().plus(Duration.ofSeconds(RandomUtils.nextLong(0, 3600))));
                    }
                });
        } catch (Throwable e) {
            log.error("比对订阅缓存更新失败：", e);
            return false;
        }
        return true;
    }

    //-------------------人员布控缓存end-------------------
    //-------------------聚集布控缓存start-------------------

    public boolean updateGroupCacheFor(String monitorId, String userName) {
        SubscribeGroupInfo subscribeGroupInfo = subscribeGroupInfoMapper
            .selectByMonitorIdAndUserName(monitorId, userName);
        try {
            Map<String, Object> map = new HashMap<>(4);
            map.put("monitorName", subscribeGroupInfo.getName());
            map.put("areaIds", subscribeGroupInfo.getAreaIds());
            map.put("memberGatherThreshold", subscribeGroupInfo.getMemberGatherThreshold());
            map.put("memberGatherTime", subscribeGroupInfo.getMemberGatherTime());

            redisTemplate.boundHashOps(String.join(REDIS_KEY_SEPARATOR,
                compareSubscriptionProperties.getRedisKeyPrefix(), "group", "info",
                userName)).put(monitorId, JSON.toJSONString(map));
        } catch (Throwable e) {
            log.error("群体布控缓存更新失败", e);
            return false;
        }
        return true;
    }

    public boolean deleteGroupCacheFor(String monitorId, String userName) {
        Long deleteResult = redisTemplate.boundHashOps(String.join(REDIS_KEY_SEPARATOR,
            compareSubscriptionProperties.getRedisKeyPrefix(), "group", "info",
            userName)).delete(monitorId);
        log.info("deleteGroupCacheFor删除{}结果：{}", monitorId, deleteResult);
        return Objects.nonNull(deleteResult) && deleteResult == 1;
    }

    public boolean updateMemberCacheFor(List<String> memberIds) {
        List<SubscribeGroupMember> groupMembers = subscribeGroupMemberMapper.selectByMemberIds(memberIds);

        try {
            Map<IdentifierPair, Map<String, SubscribeGroupMemberPerIdentifierVo>> readyCacheMap = groupMembers.stream()
                .flatMap(member -> {
                    final String memberId = member.getMemberId();
                    final List<String> monitorIds = member.getMonitorIds();
                    final String userName = member.getUserName();
                    return member.getIdentifierInfos().stream().map(identifierInfoStr -> {
                        IdentifierPair identifierPair = JSON.parseObject(identifierInfoStr, IdentifierPair.class);
                        SubscribeGroupMemberPerIdentifierVo perIdentifierVo = new SubscribeGroupMemberPerIdentifierVo();
                        perIdentifierVo.setMemberId(memberId);
                        perIdentifierVo.setMonitorIds(monitorIds);
                        perIdentifierVo.setUserName(userName);
                        perIdentifierVo.setIdentifierType(identifierPair.getIdentifierType());
                        perIdentifierVo.setIdentifier(identifierPair.getIdentifier());
                        return perIdentifierVo;
                    });
                }).collect(Collectors.toMap(
                    vo -> new IdentifierPair(vo.getIdentifierType(), vo.getIdentifier()),
                    vo -> {
                        Map<String, SubscribeGroupMemberPerIdentifierVo> map = new HashMap<>();
                        map.put(vo.getUserName(), vo);
                        return map;
                    },
                    (voMap1, voMap2) -> {
                        voMap1.putAll(voMap2);
                        return voMap1;
                    }));

            final ValueOperations<String, String> redisValueOperation = redisTemplate.opsForValue();

            for (Map.Entry<IdentifierPair, Map<String, SubscribeGroupMemberPerIdentifierVo>> entry : readyCacheMap
                .entrySet()) {
                redisValueOperation.set(String.join(
                    REDIS_KEY_SEPARATOR,
                    compareSubscriptionProperties.getRedisKeyPrefix(), "group", "member",
                    String.valueOf(entry.getKey().getIdentifierType()),
                    entry.getKey().getIdentifier()),
                    OBJECT_MAPPER.writeValueAsString(entry.getValue()));
            }
        } catch (Throwable e) {
            log.error("群体布控人员缓存更新失败", e);
            return false;
        }
        return true;
    }

    //-------------------聚集布控缓存end-------------------
    //-------------------区域布控缓存start-------------------

    //-------------------区域布控缓存end-------------------


}
