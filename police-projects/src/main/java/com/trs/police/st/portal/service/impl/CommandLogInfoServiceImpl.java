package com.trs.police.st.portal.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.st.portal.domain.dto.CommandLogContentDto;
import com.trs.police.st.portal.domain.dto.CommandLogHighlightingRiskDto;
import com.trs.police.st.portal.domain.dto.CommandLogReceiveIncidentDto;
import com.trs.police.st.portal.domain.dto.request.AddCommandLogInfoRequest;
import com.trs.police.st.portal.domain.dto.response.GetCommandLogInfoListResponse;
import com.trs.police.st.portal.domain.dto.response.GetCommandLogInfoResponse;
import com.trs.police.st.portal.domain.entity.CommandLogContentEntity;
import com.trs.police.st.portal.domain.entity.CommandLogHighlightingRiskEntity;
import com.trs.police.st.portal.domain.entity.CommandLogInfoEntity;
import com.trs.police.st.portal.domain.entity.CommandLogReceiveIncidentEntity;
import com.trs.police.st.portal.mapper.CommandLogContentMapper;
import com.trs.police.st.portal.mapper.CommandLogHighlightingRiskMapper;
import com.trs.police.st.portal.mapper.CommandLogInfoMapper;
import com.trs.police.st.portal.mapper.CommandLogReceiveIncidentMapper;
import com.trs.police.st.portal.service.CommandLogInfoService;
import com.trs.police.st.portal.util.HierarchicalUtil;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 新的指挥日志service实现
 */

@Service
public class CommandLogInfoServiceImpl implements CommandLogInfoService {

    @Resource
    private CommandLogInfoMapper infoMapper;

    @Resource
    private CommandLogReceiveIncidentMapper receiveIncidentMapper;

    @Resource
    private CommandLogHighlightingRiskMapper highlightingRiskMapper;

    @Resource
    private CommandLogContentMapper contentMapper;

    @Resource
    private PermissionService permissionService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCommandLogInfo(AddCommandLogInfoRequest request) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new RuntimeException("未能获取到登录用户");
        }
        final String districtCode = currentUser.getDeptCode().substring(0, 6);
        LocalDate date = request.getDate();
        CommandLogInfoEntity info = infoMapper.selectOne(Wrappers.<CommandLogInfoEntity>lambdaQuery()
                .eq(Objects.nonNull(request.getId()) && Objects.isNull(date), CommandLogInfoEntity::getId, request.getId())
                .eq(Objects.nonNull(date) && Objects.isNull(request.getId()), CommandLogInfoEntity::getDate, date)
                .eq(CommandLogInfoEntity::getDistrictCode, districtCode));

        if (Objects.isNull(info)) {
            //没找到，则新增
            info = new CommandLogInfoEntity();
            info.setDate(request.getDate());
            info.setDistrictCode(districtCode);
            infoMapper.insert(info);
        }
        final CommandLogInfoEntity finalInfo = info;

        //更新附属表数据
        //今日接警情况
        receiveIncidentMapper.delete(Wrappers.<CommandLogReceiveIncidentEntity>lambdaQuery()
                .eq(CommandLogReceiveIncidentEntity::getInfoId, info.getId()));
        request.getTodayIncidentList().stream().map(dto -> {
            CommandLogReceiveIncidentEntity entity = new CommandLogReceiveIncidentEntity();
            BeanUtils.copyProperties(dto, entity);
            entity.setInfoId(finalInfo.getId());
            entity.setIfPast(false);
            return entity;
        }).forEach(receiveIncidentMapper::insert);

        //往期接警情况
        request.getPreviousIncidentList().stream().map(dto -> {
            CommandLogReceiveIncidentEntity entity = new CommandLogReceiveIncidentEntity();
            BeanUtils.copyProperties(dto, entity);
            entity.setInfoId(finalInfo.getId());
            entity.setIfPast(true);
            return entity;
        }).forEach(receiveIncidentMapper::insert);

        //突出风险
        highlightingRiskMapper.delete(Wrappers.<CommandLogHighlightingRiskEntity>lambdaQuery()
                .eq(CommandLogHighlightingRiskEntity::getInfoId, finalInfo.getId()));
        request.getHighlightingRiskList().stream().map(dto -> {
            CommandLogHighlightingRiskEntity entity = new CommandLogHighlightingRiskEntity();
            BeanUtils.copyProperties(dto, entity);
            entity.setInfoId(finalInfo.getId());
            return entity;
        }).forEach(highlightingRiskMapper::insert);

        //具体内容
        contentMapper.delete(Wrappers.<CommandLogContentEntity>lambdaQuery()
                .eq(CommandLogContentEntity::getInfoId, finalInfo.getId()));
        request.getContentList().stream().map(dto -> {
            CommandLogContentEntity entity = new CommandLogContentEntity();
            BeanUtils.copyProperties(dto, entity);
            entity.setInfoId(finalInfo.getId());
            return entity;
        }).forEach(contentMapper::insert);
    }

    @Override
    public RestfulResultsV2<GetCommandLogInfoResponse> getCommandLogInfo(Long id, LocalDate date) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new RuntimeException("未能获取到登录用户");
        }
        final String districtCode = currentUser.getDeptCode().substring(0, 6);
        CommandLogInfoEntity info = infoMapper.selectOne(Wrappers.<CommandLogInfoEntity>lambdaQuery()
                .eq(Objects.nonNull(id) && Objects.isNull(date), CommandLogInfoEntity::getId, id)
                .eq(Objects.nonNull(date) && Objects.isNull(id), CommandLogInfoEntity::getDate, date)
                .eq(CommandLogInfoEntity::getDistrictCode, districtCode));

        GetCommandLogInfoResponse response = new GetCommandLogInfoResponse();

        if (Objects.isNull(info)) {
            return RestfulResultsV2.ok(response);
        }

        List<CommandLogReceiveIncidentEntity> incidentEntities = receiveIncidentMapper.selectList(Wrappers.<CommandLogReceiveIncidentEntity>lambdaQuery()
                .eq(CommandLogReceiveIncidentEntity::getInfoId, info.getId()));

        response.setTodayIncidentList(incidentEntities.stream()
                .filter(entity -> !entity.getIfPast())
                .map(entity -> {
                    CommandLogReceiveIncidentDto dto = new CommandLogReceiveIncidentDto();
                    BeanUtils.copyProperties(entity, dto);
                    return dto;
                }).collect(Collectors.toList()));

        response.setPreviousIncidentList(incidentEntities.stream()
                .filter(CommandLogReceiveIncidentEntity::getIfPast)
                .map(entity -> {
                    CommandLogReceiveIncidentDto dto = new CommandLogReceiveIncidentDto();
                    BeanUtils.copyProperties(entity, dto);
                    return dto;
                }).collect(Collectors.toList()));

        List<CommandLogHighlightingRiskDto> highlightingRiskList = highlightingRiskMapper.selectList(Wrappers.<CommandLogHighlightingRiskEntity>lambdaQuery()
                        .eq(CommandLogHighlightingRiskEntity::getInfoId, info.getId())).stream()
                .map(entity -> {
                    CommandLogHighlightingRiskDto dto = new CommandLogHighlightingRiskDto();
                    BeanUtils.copyProperties(entity, dto);
                    return dto;
                }).collect(Collectors.toList());
        response.setHighlightingRiskList(highlightingRiskList);

        List<CommandLogContentDto> contentList = contentMapper.selectList(Wrappers.<CommandLogContentEntity>lambdaQuery()
                        .eq(CommandLogContentEntity::getInfoId, info.getId())).stream()
                .map(entity -> {
                    CommandLogContentDto dto = new CommandLogContentDto();
                    BeanUtils.copyProperties(entity, dto);
                    return dto;
                }).collect(Collectors.toList());
        response.setContentList(contentList);

        return RestfulResultsV2.ok(response);
    }

    @Override
    public RestfulResultsV2<GetCommandLogInfoListResponse> getCommandLogInfoList(PageParams pageParams, LocalDate date) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new RuntimeException("未能获取到登录用户");
        }
        final String districtCode = HierarchicalUtil.getEffectiveCode(currentUser.getDeptCode().substring(0, 6));

        Page<CommandLogInfoEntity> infoPage = infoMapper.selectPage(pageParams.toPage(), Wrappers.<CommandLogInfoEntity>lambdaQuery()
                .likeRight(CommandLogInfoEntity::getDistrictCode, districtCode)
                .eq(CommandLogInfoEntity::getDate, date)
                .orderByDesc(CommandLogInfoEntity::getDistrictCode));

        List<GetCommandLogInfoListResponse> records = infoPage.getRecords().stream().map(entity -> {
            GetCommandLogInfoListResponse response = new GetCommandLogInfoListResponse();
            DeptDto dept = permissionService.getDeptById(entity.getCreateDeptId());
            response.setId(entity.getId());
            response.setDate(entity.getDate());
            response.setDistrictCode(entity.getDistrictCode());
            response.setDeptName(dept.getShortName());
            response.setUpdateTime(entity.getUpdateTime());
            response.setCreateUserName(permissionService.getUserById(entity.getCreateUserId()).getUsername());
            return response;
        }).collect(Collectors.toList());
        return RestfulResultsV2.ok(records)
                .addPageSize((int) infoPage.getSize())
                .addPageNum((int) infoPage.getPages())
                .addTotalCount(infoPage.getTotal());
    }

    @Override
    public RestfulResultsV2<String> getLastContent(LocalDate date, Integer type) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new RuntimeException("未能获取到登录用户");
        }
        String content = infoMapper.getLastContentByDate(date, currentUser.getDeptId(), type);
        return RestfulResultsV2.ok(content);
    }

    @Override
    public RestfulResultsV2<LocalDate> getLogExists(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dateList = infoMapper.selectList(Wrappers.<CommandLogInfoEntity>lambdaQuery()
                        .between(CommandLogInfoEntity::getDate, startDate, endDate)
                        .orderByAsc(CommandLogInfoEntity::getDate)
                        .select(CommandLogInfoEntity::getDate))
                .stream().map(CommandLogInfoEntity::getDate)
                .collect(Collectors.toList());
        return RestfulResultsV2.ok(dateList);
    }

    @Override
    public RestfulResultsV2<String> getIncidentDisposalHistory(String type, PageParams pageParams, LocalDate date) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new RuntimeException("未能获取到登录用户");
        }
        IPage<String> history = "disposal".equals(type)
                ? receiveIncidentMapper.getDisposalHistory(pageParams.toPage(), currentUser.getDeptId(), date)
                : receiveIncidentMapper.getRemarkHistory(pageParams.toPage(), currentUser.getDeptId(), date);
        return RestfulResultsV2.ok(history.getRecords())
                .addPageSize((int) history.getSize())
                .addPageNum((int) history.getPages())
                .addTotalCount(history.getTotal());
    }

    @Override
    public RestfulResultsV2<CommandLogReceiveIncidentDto> getIncidentHistory(PageParams pageParams, LocalDate date) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new RuntimeException("未能获取到登录用户");
        }
        IPage<CommandLogReceiveIncidentEntity> page = receiveIncidentMapper.getIncidentHistory(pageParams.toPage(), currentUser.getDeptId(), date);
        List<CommandLogReceiveIncidentDto> list = page.getRecords().stream().map(entity -> {
            CommandLogReceiveIncidentDto dto = new CommandLogReceiveIncidentDto();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
        return RestfulResultsV2.ok(list)
                .addPageSize((int) page.getSize())
                .addPageNum((int) page.getPages())
                .addTotalCount(page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCommandLog(Long id) {
        infoMapper.deleteById(id);
        receiveIncidentMapper.delete(Wrappers.<CommandLogReceiveIncidentEntity>lambdaQuery()
                .eq(CommandLogReceiveIncidentEntity::getId, id));
        highlightingRiskMapper.delete(Wrappers.<CommandLogHighlightingRiskEntity>lambdaQuery()
                .eq(CommandLogHighlightingRiskEntity::getInfoId, id));
        contentMapper.delete(Wrappers.<CommandLogContentEntity>lambdaQuery()
                .eq(CommandLogContentEntity::getInfoId, id));
    }
}
