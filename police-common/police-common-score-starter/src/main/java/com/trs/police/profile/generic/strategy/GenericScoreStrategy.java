package com.trs.police.profile.generic.strategy;

import com.trs.police.profile.generic.dto.GenericScoreResult;
import com.trs.police.profile.generic.dto.ScoreContext;
import com.trs.police.profile.generic.dto.ScoreRule;

/**
 * 通用积分计算策略接口
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 定义通用积分计算策略的统一接口
 */
public interface GenericScoreStrategy {

    /**
     * 计算积分
     *
     * @param context 积分计算上下文
     * @param rule    积分规则
     * @return 积分计算结果
     */
    GenericScoreResult calculateScore(ScoreContext context, ScoreRule rule);

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 获取支持的规则类型
     *
     * @return 支持的规则类型
     */
    String getSupportedRuleType();

    /**
     * 获取适用范围
     *
     * @return 适用范围（如：person、company、device等）
     */
    String[] getApplicableScopes();

    /**
     * 检查是否支持指定的规则
     *
     * @param ruleName 规则名称
     * @param ruleType 规则类型
     * @param scope    适用范围
     * @return 是否支持
     */
    default boolean supports(String ruleName, String ruleType, String scope) {
        // 检查策略名称
        boolean nameMatch = getStrategyName().equals(ruleName);

        // 检查规则类型
        boolean typeMatch = getSupportedRuleType() != null && getSupportedRuleType().equals(ruleType);

        // 检查适用范围
        boolean scopeMatch = true;
        if (getApplicableScopes() != null && getApplicableScopes().length > 0 && scope != null) {
            scopeMatch = java.util.Arrays.asList(getApplicableScopes()).contains(scope);
        }

        return (nameMatch || typeMatch) && scopeMatch;
    }

    /**
     * 获取策略描述
     *
     * @return 策略描述
     */
    default String getDescription() {
        return "通用积分计算策略：" + getStrategyName();
    }

    /**
     * 验证上下文数据是否有效
     *
     * @param context 积分计算上下文
     * @return 是否有效
     */
    default boolean validateContext(ScoreContext context) {
        return context != null
                && context.getPrimaryKey() != null
                && !context.getPrimaryKey().trim().isEmpty();
    }

    /**
     * 验证规则是否有效
     *
     * @param rule 积分规则
     * @return 是否有效
     */
    default boolean validateRule(ScoreRule rule) {
        return rule != null
                && rule.getRuleId() != null
                && !rule.getRuleId().trim().isEmpty()
                && rule.isEnabled();
    }

    /**
     * 获取策略优先级
     *
     * @return 优先级（数值越大优先级越高）
     */
    default int getPriority() {
        return 0;
    }

    /**
     * 是否支持批量计算
     *
     * @return 是否支持批量计算
     */
    default boolean supportsBatchCalculation() {
        return false;
    }

    /**
     * 批量计算积分（如果支持的话）
     *
     * @param contexts 积分计算上下文列表
     * @param rule     积分规则
     * @return 积分计算结果列表
     */
    default java.util.List<GenericScoreResult> batchCalculateScore(java.util.List<ScoreContext> contexts, ScoreRule rule) {
        if (!supportsBatchCalculation()) {
            throw new UnsupportedOperationException("该策略不支持批量计算");
        }

        // 默认实现：逐个计算
        return contexts.stream()
                .map(context -> calculateScore(context, rule))
                .filter(java.util.Objects::nonNull)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取策略配置要求
     *
     * @return 配置要求说明
     */
    default String getConfigurationRequirements() {
        return "无特殊配置要求";
    }

    /**
     * 验证策略配置
     *
     * @param rule 积分规则
     * @return 配置验证结果
     */
    default boolean validateConfiguration(ScoreRule rule) {
        return true; // 默认认为配置有效
    }
}
