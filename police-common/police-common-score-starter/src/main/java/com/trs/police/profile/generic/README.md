# 通用积分计算引擎

## 概述

这是一个高度通用化的积分计算引擎，不仅限于人员标签计算，可以适用于各种场景的积分评估，如人员评价、企业评级、设备评估、项目评分等。

## 系统架构

### 核心组件

1. **GenericScoreEngine** - 通用积分计算引擎
2. **GenericScoreStrategy** - 通用积分策略接口
3. **GenericScoreStrategyFactory** - 通用策略工厂
4. **GenericScoreService** - 通用积分服务
5. **PersonScoreAdapter** - 人员积分适配器（向后兼容）

### 数据模型

1. **ScoreContext** - 通用积分计算上下文
2. **ScoreRule** - 通用积分规则配置
3. **GenericScoreResult** - 通用积分计算结果

### 请求模型

1. **ScoreCalculationRequest** - 单个对象积分计算请求
2. **BatchScoreCalculationRequest** - 批量积分计算请求
3. **SpecificRulesScoreRequest** - 指定规则积分计算请求

## 核心特性

### 🎯 **高度通用化**

- **多场景适用**：支持人员、企业、设备、项目等各种对象的积分计算
- **灵活配置**：通过属性映射和规则配置适应不同业务场景
- **可扩展性**：易于添加新的积分策略和规则类型

### 🔧 **策略模式设计**

- **策略接口**：统一的积分计算策略接口
- **策略工厂**：自动发现和管理策略
- **策略优先级**：支持策略优先级排序
- **批量计算**：支持策略级别的批量计算优化

### 📊 **丰富的规则类型**

1. **数值范围策略** (`NumericRangeScoreStrategy`)
    - 适用于：年龄、工作年限、收入、经验等数值型指标
    - 支持：线性计算、分段计算、自定义范围配置

2. **关键词匹配策略** (`KeywordMatchScoreStrategy`)
    - 适用于：职务、获奖情况、经历、教育背景等文本型指标
    - 支持：关键词权重、匹配策略、累加/最大值/平均值计算

3. **自定义策略**
    - 易于扩展新的计算逻辑
    - 支持复杂的业务规则

## 使用示例

### 1. 基本使用

```java

@Autowired
private GenericScoreService scoreService;

// 创建积分上下文
ScoreContext context = ScoreContext.builder()
        .primaryKey("110101198001011234")
        .name("张三")
        .type("person")
        .build();

// 添加属性
context.

addProperty("age",35);
context.

addProperty("workYears",10);
context.

addProperty("position","科长");

// 创建积分规则
List<ScoreRule> rules = createScoreRules();

// 计算积分
GenericScoreResult result = scoreService.calculateScore(context, rules);
```

### 2. 人员积分计算（向后兼容）

```java

@Autowired
private PersonScoreAdapter personAdapter;

// 使用原有的人员积分上下文
PersonScoreContext personContext = createPersonContext();
List<FxActionExcavateDictVO> configs = getPersonScoreConfigs();

// 通过适配器使用新的通用引擎
ScoreResult result = personAdapter.calculatePersonScore(personContext, configs);
```

### 3. 企业评级示例

```java
// 创建企业积分上下文
ScoreContext companyContext = ScoreContext.builder()
                .primaryKey("91110000123456789X")
                .name("某某科技有限公司")
                .type("company")
                .category("technology")
                .build();

// 添加企业属性
companyContext.

addProperty("revenue",50000000.0);  // 年收入
companyContext.

addProperty("employees",200);       // 员工数量
companyContext.

addProperty("foundedYear",2010);    // 成立年份
companyContext.

addProperty("certifications","ISO9001,高新技术企业"); // 认证情况

// 创建企业评级规则
List<ScoreRule> companyRules = Arrays.asList(
        ScoreRule.builder()
                .ruleId("revenue_rule")
                .ruleName("年收入评分")
                .ruleType("numericRange")
                .scope("company")
                .enabled(true)
                .build()
                .addConfig("propertyName", "revenue")
                .addConfig("minValue", 0.0)
                .addConfig("maxValue", *********.0)
                .addConfig("minScore", 0.0)
                .addConfig("maxScore", 50.0),

        ScoreRule.builder()
                .ruleId("cert_rule")
                .ruleName("认证情况评分")
                .ruleType("keywordMatch")
                .scope("company")
                .enabled(true)
                .build()
                .addConfig("propertyName", "certifications")
);

// 计算企业评级
GenericScoreResult companyResult = scoreService.calculateScore(companyContext, companyRules);
```

## REST API 接口

### 1. 计算单个对象积分

```bash
POST /api/generic-score/calculate
Content-Type: application/json

{
  "context": {
    "primaryKey": "110101198001011234",
    "name": "张三",
    "type": "person",
    "properties": {
      "age": 35,
      "workYears": 10,
      "position": "科长"
    }
  },
  "rules": [
    {
      "ruleId": "age_rule",
      "ruleName": "年龄评分",
      "ruleType": "age",
      "enabled": true,
      "config": {
        "propertyName": "age"
      }
    }
  ],
  "includeDetails": true,
  "applyWeights": true
}
```

### 2. 批量计算积分

```bash
POST /api/generic-score/batch/calculate
Content-Type: application/json

{
  "contexts": [
    {
      "primaryKey": "person1",
      "name": "张三",
      "type": "person",
      "properties": {...}
    },
    {
      "primaryKey": "person2", 
      "name": "李四",
      "type": "person",
      "properties": {...}
    }
  ],
  "rules": [...],
  "includeDetails": true,
  "enableParallel": true,
  "batchSize": 100
}
```

### 3. 使用指定规则计算

```bash
POST /api/generic-score/calculate/specific-rules
Content-Type: application/json

{
  "context": {
    "primaryKey": "110101198001011234",
    "name": "张三",
    "type": "person",
    "properties": {...}
  },
  "ruleIds": ["rule1", "rule2", "rule3"],
  "includeDetails": true,
  "ruleFilter": {
    "ruleTypes": ["age", "workYears"],
    "enabledOnly": true
  }
}
```

### 4. 获取策略统计信息

```bash
GET /api/generic-score/strategies/statistics
```

### 5. 检查策略支持

```bash
GET /api/generic-score/strategies/exists?ruleType=age&scope=person
```

## 扩展新的积分策略

### 1. 创建策略实现类

```java

@Component
public class CustomScoreStrategy extends AbstractGenericScoreStrategy {

    @Override
    protected GenericScoreResult doCalculateScore(ScoreContext context, ScoreRule rule) {
        // 实现具体的计算逻辑
        double score = calculateCustomScore(context, rule);
        String hitData = "命中数据描述";
        String description = "计算说明";

        return createSuccessResult(context, rule, score, hitData, description);
    }

    @Override
    public String getStrategyName() {
        return "自定义策略";
    }

    @Override
    public String getSupportedRuleType() {
        return "custom";
    }

    @Override
    public String[] getApplicableScopes() {
        return new String[]{"person", "company", "device"};
    }

    private double calculateCustomScore(ScoreContext context, ScoreRule rule) {
        // 自定义计算逻辑
        return 0.0;
    }
}
```

### 2. 配置规则

```java
ScoreRule customRule = ScoreRule.builder()
        .ruleId("custom_rule")
        .ruleName("自定义规则")
        .ruleType("custom")
        .scope("person")
        .enabled(true)
        .build();

// 添加自定义配置
customRule.

addConfig("customParam1","value1");
customRule.

addConfig("customParam2",100);
```

## 向后兼容

通过 `PersonScoreAdapter` 适配器，原有的人员积分计算代码无需修改即可使用新的通用引擎：

```java
// 原有代码保持不变
PersonScoreContext personContext = ...;
List<FxActionExcavateDictVO> configs = ...;

// 自动使用新的通用引擎
ScoreResult result = personScoreService.calculatePersonScore(personContext, configs);
```

## 性能优化

1. **并行计算**：支持批量并行计算
2. **策略缓存**：策略工厂自动缓存策略实例
3. **配置验证**：提前验证配置有效性
4. **错误处理**：完善的异常处理机制
5. **分批处理**：大批量数据自动分批处理

## 监控和日志

系统提供详细的日志记录和监控指标：

- 计算过程日志
- 策略匹配日志
- 性能统计日志
- 错误异常日志

可以通过 `/api/generic-score/strategies/statistics` 接口获取策略统计信息。
