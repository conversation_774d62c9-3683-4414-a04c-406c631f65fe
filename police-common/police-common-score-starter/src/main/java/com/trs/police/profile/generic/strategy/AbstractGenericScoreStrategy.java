package com.trs.police.profile.generic.strategy;

import com.trs.police.profile.generic.dto.GenericScoreResult;
import com.trs.police.profile.generic.dto.ScoreContext;
import com.trs.police.profile.generic.dto.ScoreRule;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;

/**
 * 抽象通用积分计算策略
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 提供通用积分计算策略的通用实现
 */
@Slf4j
public abstract class AbstractGenericScoreStrategy implements GenericScoreStrategy {

    @Override
    public GenericScoreResult calculateScore(ScoreContext context, ScoreRule rule) {
        // 验证输入参数
        if (!validateContext(context)) {
            log.warn("积分上下文验证失败，策略：{}", getStrategyName());
            return createEmptyResult(context);
        }

        if (!validateRule(rule)) {
            log.warn("积分规则验证失败，策略：{}", getStrategyName());
            return createEmptyResult(context);
        }

        // 验证策略配置
        if (!validateConfiguration(rule)) {
            log.warn("策略配置验证失败，策略：{}，规则：{}", getStrategyName(), rule.getRuleName());
            return createEmptyResult(context);
        }

        try {
            // 执行具体的积分计算逻辑
            return doCalculateScore(context, rule);
        } catch (Exception e) {
            log.error("积分计算异常，策略：{}，规则：{}，错误：{}",
                    getStrategyName(), rule.getRuleName(), e.getMessage(), e);
            return createErrorResult(context, rule, e.getMessage());
        }
    }

    /**
     * 执行具体的积分计算逻辑
     * 子类需要实现此方法
     *
     * @param context 积分计算上下文
     * @param rule    积分规则
     * @return 积分计算结果
     */
    protected abstract GenericScoreResult doCalculateScore(ScoreContext context, ScoreRule rule);

    /**
     * 创建空的积分结果
     *
     * @param context 积分计算上下文
     * @return 空的积分结果
     */
    protected GenericScoreResult createEmptyResult(ScoreContext context) {
        return GenericScoreResult.builder()
                .primaryKey(context != null ? context.getPrimaryKey() : "")
                .name(context != null ? context.getName() : "")
                .type(context != null ? context.getType() : "")
                .totalScore(0.0)
                .weightedScore(0.0)
                .detailScores(new ArrayList<>())
                .build();
    }

    /**
     * 创建错误的积分结果
     *
     * @param context      积分计算上下文
     * @param rule         积分规则
     * @param errorMessage 错误信息
     * @return 错误的积分结果
     */
    protected GenericScoreResult createErrorResult(ScoreContext context, ScoreRule rule, String errorMessage) {
        GenericScoreResult result = createEmptyResult(context);

        GenericScoreResult.ScoreDetail errorDetail = GenericScoreResult.ScoreDetail.createError(
                rule != null ? rule.getRuleName() : "未知规则",
                errorMessage
        );

        result.addDetailScore(errorDetail);
        result.setRemark("计算过程中发生错误：" + errorMessage);

        return result;
    }

    /**
     * 创建成功的积分结果
     *
     * @param context     积分计算上下文
     * @param rule        积分规则
     * @param score       计算得分
     * @param hitData     命中数据
     * @param description 计算说明
     * @return 积分结果
     */
    protected GenericScoreResult createSuccessResult(ScoreContext context, ScoreRule rule,
                                                     double score, String hitData, String description) {
        GenericScoreResult result = createEmptyResult(context);
        result.setTotalScore(score);
        result.setWeightedScore(score); // 初始加权分值等于原始分值

        GenericScoreResult.ScoreDetail detail = GenericScoreResult.ScoreDetail.builder()
                .ruleId(rule.getRuleId())
                .ruleName(rule.getRuleName())
                .ruleType(rule.getRuleType())
                .category(rule.getCategory())
                .originalScore(score)
                .finalScore(score)
                .weightedScore(score)
                .hitData(hitData)
                .hitCount(1)
                .description(description)
                .dataSource(getStrategyName())
                .isValid(true)
                .build();

        result.addDetailScore(detail);

        return result;
    }

    /**
     * 创建无命中的积分结果
     *
     * @param context 积分计算上下文
     * @param rule    积分规则
     * @return 无命中的积分结果
     */
    protected GenericScoreResult createNoHitResult(ScoreContext context, ScoreRule rule) {


        GenericScoreResult.ScoreDetail detail = GenericScoreResult.ScoreDetail.createNoHit(rule.getRuleName());
        detail.setRuleId(rule.getRuleId());
        detail.setRuleType(rule.getRuleType());
        detail.setCategory(rule.getCategory());
        detail.setDataSource(getStrategyName());

        GenericScoreResult result = createEmptyResult(context);
        result.addDetailScore(detail);
        return result;
    }

    /**
     * 检查字符串是否有效
     *
     * @param str 字符串
     * @return 是否有效
     */
    protected boolean isValidString(String str) {
        return str != null && !str.trim().isEmpty() && !"无".equals(str.trim()) && !"null".equalsIgnoreCase(str.trim());
    }

    /**
     * 检查数值是否有效
     *
     * @param number 数值
     * @return 是否有效
     */
    protected boolean isValidNumber(Number number) {
        return number != null && number.doubleValue() > 0;
    }

    /**
     * 安全获取字符串值
     *
     * @param str          字符串
     * @param defaultValue 默认值
     * @return 字符串值
     */
    protected String safeGetString(String str, String defaultValue) {
        return isValidString(str) ? str : defaultValue;
    }

    /**
     * 安全获取数值
     *
     * @param number       数值
     * @param defaultValue 默认值
     * @return 数值
     */
    protected double safeGetDouble(Number number, double defaultValue) {
        return isValidNumber(number) ? number.doubleValue() : defaultValue;
    }

    /**
     * 安全获取整数
     *
     * @param number       数值
     * @param defaultValue 默认值
     * @return 整数值
     */
    protected int safeGetInt(Number number, int defaultValue) {
        return isValidNumber(number) ? number.intValue() : defaultValue;
    }

    /**
     * 从上下文中安全获取属性值
     *
     * @param context      上下文
     * @param key          属性键
     * @param defaultValue 默认值
     * @param <T>          属性值类型
     * @return 属性值
     */
    protected <T> T safeGetProperty(ScoreContext context, String key, T defaultValue) {
        if (context == null) {
            return defaultValue;
        }
        return context.getProperty(key, defaultValue);
    }

    /**
     * 从规则中安全获取配置值
     *
     * @param rule         规则
     * @param key          配置键
     * @param defaultValue 默认值
     * @param <T>          配置值类型
     * @return 配置值
     */
    protected <T> T safeGetConfig(ScoreRule rule, String key, T defaultValue) {
        if (rule == null) {
            return defaultValue;
        }
        return rule.getConfig(key, defaultValue);
    }

    /**
     * 检查上下文是否包含指定属性且值有效
     *
     * @param context 上下文
     * @param key     属性键
     * @return 是否有效
     */
    protected boolean hasValidProperty(ScoreContext context, String key) {
        if (context == null || !context.hasProperty(key)) {
            return false;
        }
        Object value = context.getProperty(key);
        if (value instanceof String) {
            return isValidString((String) value);
        } else if (value instanceof Number) {
            return isValidNumber((Number) value);
        }
        return value != null;
    }

    /**
     * 计算命中次数
     *
     * @param data     数据字符串
     * @param keywords 关键词数组
     * @return 命中次数
     */
    protected int calculateHitCount(String data, String... keywords) {
        if (!isValidString(data) || keywords == null || keywords.length == 0) {
            return 0;
        }

        int hitCount = 0;
        for (String keyword : keywords) {
            if (isValidString(keyword) && data.contains(keyword)) {
                hitCount++;
            }
        }
        return hitCount;
    }

    @Override
    public String getDescription() {
        return "通用积分计算策略：" + getStrategyName() + "，支持规则类型：" + getSupportedRuleType();
    }

    @Override
    public boolean validateConfiguration(ScoreRule rule) {
        // 默认实现：检查基本配置
        return rule != null && rule.isEnabled();
    }
}
