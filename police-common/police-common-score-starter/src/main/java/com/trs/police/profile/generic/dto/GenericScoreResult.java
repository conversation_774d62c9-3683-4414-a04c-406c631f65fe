package com.trs.police.profile.generic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用积分计算结果
 *
 * @author: AI Assistant
 * @description: 泛型化的积分计算结果，适用于各种积分计算场景
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenericScoreResult {

    /**
     * 主键标识
     */
    private String primaryKey;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 总积分
     */
    private Double totalScore;

    /**
     * 加权总分
     */
    private Double weightedScore;

    /**
     * 详细积分列表
     */
    @Builder.Default
    private List<ScoreDetail> detailScores = new ArrayList<>();

    /**
     * 计算时间
     */
    @Builder.Default
    private LocalDateTime calculateTime = LocalDateTime.now();

    /**
     * 积分等级
     */
    private String scoreLevel;

    /**
     * 积分排名
     */
    private Integer ranking;

    /**
     * 是否为高分
     */
    private Boolean isHighScore;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 扩展结果
     */
    @Builder.Default
    private Map<String, Object> extendedResults = new HashMap<>();

    /**
     * 添加详细积分
     *
     * @param detail 详细积分
     */
    public void addDetailScore(ScoreDetail detail) {
        if (detailScores == null) {
            detailScores = new ArrayList<>();
        }
        detailScores.add(detail);
    }

    /**
     * 添加扩展结果
     *
     * @param key   结果键
     * @param value 结果值
     */
    public void addExtendedResult(String key, Object value) {
        if (extendedResults == null) {
            extendedResults = new HashMap<>();
        }
        extendedResults.put(key, value);
    }

    /**
     * 获取扩展结果
     *
     * @param key          结果键
     * @param defaultValue 默认值
     * @param <T>          结果值类型
     * @return 结果值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtendedResult(String key, T defaultValue) {
        if (extendedResults == null || !extendedResults.containsKey(key)) {
            return defaultValue;
        }
        try {
            return (T) extendedResults.get(key);
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }

    /**
     * 根据总分计算积分等级
     *
     * @return 积分等级
     */
    public String calculateScoreLevel() {
        if (totalScore == null) {
            return "未知";
        }

        if (totalScore >= 90) {
            scoreLevel = "优秀";
            isHighScore = true;
        } else if (totalScore >= 80) {
            scoreLevel = "良好";
            isHighScore = true;
        } else if (totalScore >= 70) {
            scoreLevel = "中等";
            isHighScore = false;
        } else if (totalScore >= 60) {
            scoreLevel = "及格";
            isHighScore = false;
        } else {
            scoreLevel = "不及格";
            isHighScore = false;
        }

        return scoreLevel;
    }

    /**
     * 根据自定义阈值计算积分等级
     *
     * @param thresholds 阈值映射（分数 -> 等级）
     * @return 积分等级
     */
    public String calculateScoreLevelWithThresholds(Map<Double, String> thresholds) {
        if (totalScore == null || thresholds == null || thresholds.isEmpty()) {
            return "未知";
        }

        // 按分数降序排序，找到第一个小于等于当前分数的阈值
        scoreLevel = thresholds.entrySet().stream().sorted((e1, e2) -> Double.compare(e2.getKey(), e1.getKey())).filter(entry -> totalScore >= entry.getKey()).map(Map.Entry::getValue).findFirst().orElse("未知");

        // 判断是否为高分（可以根据业务需要调整）
        isHighScore = totalScore >= 80;

        return scoreLevel;
    }

    /**
     * 获取有效的详细积分数量
     *
     * @return 有效详细积分数量
     */
    public int getValidDetailScoreCount() {
        if (detailScores == null) {
            return 0;
        }
        return (int) detailScores.stream().filter(detail -> detail.getIsValid() != null && detail.getIsValid()).count();
    }

    /**
     * 获取总的命中次数
     *
     * @return 总命中次数
     */
    public int getTotalHitCount() {
        if (detailScores == null) {
            return 0;
        }
        return detailScores.stream().filter(detail -> detail.getIsValid() != null && detail.getIsValid()).mapToInt(detail -> detail.getHitCount() != null ? detail.getHitCount() : 0).sum();
    }

    /**
     * 积分详细信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ScoreDetail {

        /**
         * 规则ID
         */
        private String ruleId;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 规则类型
         */
        private String ruleType;

        /**
         * 规则分类
         */
        private String category;

        /**
         * 原始分值
         */
        private Double originalScore;

        /**
         * 最终分值
         */
        private Double finalScore;

        /**
         * 加权分值
         */
        private Double weightedScore;

        /**
         * 基础分值
         */
        private Integer baseScore;

        /**
         * 系数
         */
        private Double coefficient;

        /**
         * 权重
         */
        private Double weight;

        /**
         * 计算公式
         */
        private String formula;

        /**
         * 命中的具体数据
         */
        private String hitData;

        /**
         * 命中次数
         */
        private Integer hitCount;

        /**
         * 计算说明
         */
        private String description;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 计算时间
         */
        @Builder.Default
        private LocalDateTime calculateTime = LocalDateTime.now();

        /**
         * 是否有效
         */
        @Builder.Default
        private Boolean isValid = true;

        /**
         * 错误信息（如果计算失败）
         */
        private String errorMessage;

        /**
         * 扩展信息
         */
        @Builder.Default
        private Map<String, Object> extendedInfo = new HashMap<>();

        /**
         * 创建成功的积分详情
         *
         * @param ruleName      规则名称
         * @param originalScore 原始分值
         * @param finalScore    最终分值
         * @param hitData       命中数据
         * @return 积分详情
         */
        public static ScoreDetail createSuccess(String ruleName, Double originalScore, Double finalScore, String hitData) {
            return ScoreDetail.builder().ruleName(ruleName).originalScore(originalScore).finalScore(finalScore).hitData(hitData).hitCount(1).isValid(true).build();
        }

        /**
         * 创建失败的积分详情
         *
         * @param ruleName     规则名称
         * @param errorMessage 错误信息
         * @return 积分详情
         */
        public static ScoreDetail createError(String ruleName, String errorMessage) {
            return ScoreDetail.builder().ruleName(ruleName).originalScore(0.0).finalScore(0.0).isValid(false).errorMessage(errorMessage).build();
        }

        /**
         * 创建无命中的积分详情
         *
         * @param ruleName 规则名称
         * @return 积分详情
         */
        public static ScoreDetail createNoHit(String ruleName) {
            return ScoreDetail.builder().ruleName(ruleName).originalScore(0.0).finalScore(0.0).hitCount(0).description("未命中任何条件").isValid(true).build();
        }

        /**
         * 添加扩展信息
         *
         * @param key   信息键
         * @param value 信息值
         */
        public void addExtendedInfo(String key, Object value) {
            if (extendedInfo == null) {
                extendedInfo = new HashMap<>();
            }
            extendedInfo.put(key, value);
        }

        /**
         * 获取分值变化说明
         *
         * @return 分值变化说明
         */
        public String getScoreChangeDescription() {
            if (originalScore == null || finalScore == null) {
                return "分值计算异常";
            }

            if (originalScore.equals(finalScore)) {
                return String.format("基础分值：%.2f", finalScore);
            }

            StringBuilder desc = new StringBuilder();
            desc.append(String.format("原始分值：%.2f", originalScore));

            if (coefficient != null && coefficient != 1.0) {
                desc.append(String.format("，系数：%.2f", coefficient));
            }

            if (weight != null && weight != 1.0) {
                desc.append(String.format("，权重：%.2f", weight));
            }

            if (baseScore != null && baseScore > 0) {
                desc.append(String.format("，基础分：%d", baseScore));
            }

            if (formula != null && !formula.trim().isEmpty()) {
                desc.append(String.format("，公式：%s", formula));
            }

            desc.append(String.format("，最终分值：%.2f", finalScore));

            return desc.toString();
        }
    }
}
