package com.trs.police.profile.generic.strategy.impl;

import com.trs.police.profile.generic.dto.GenericScoreResult;
import com.trs.police.profile.generic.dto.ScoreContext;
import com.trs.police.profile.generic.dto.ScoreRule;
import com.trs.police.profile.generic.strategy.AbstractGenericScoreStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 数值范围积分计算策略
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 根据数值范围计算积分，适用于年龄、工作年限、收入等数值型指标
 */
@Slf4j
@Component
public class NumericRangeScoreStrategy extends AbstractGenericScoreStrategy {

    @Override
    protected GenericScoreResult doCalculateScore(ScoreContext context, ScoreRule rule) {
        // 获取要评估的属性名
        String propertyName = safeGetConfig(rule, "propertyName", "");
        if (!isValidString(propertyName)) {
            log.warn("数值范围策略缺少propertyName配置，规则：{}", rule.getRuleName());
            return createNoHitResult(context, rule);
        }

        // 获取属性值
        Double value = context.getNumberProperty(propertyName);
        if (value == null || value <= 0) {
            return createNoHitResult(context, rule);
        }

        // 获取分值范围配置
        Double score = calculateRangeScore(value, rule);

        if (score <= 0) {
            return createNoHitResult(context, rule);
        }

        String hitData = String.format("%s：%.2f", propertyName, value);
        String description = String.format("根据%s值%.2f计算积分", propertyName, value);

        return createSuccessResult(context, rule, score, hitData, description);
    }

    /**
     * 根据数值范围计算积分
     *
     * @param value 数值
     * @param rule  规则
     * @return 积分
     */
    private Double calculateRangeScore(Double value, ScoreRule rule) {
        // 获取范围配置，格式：[{"min":0,"max":10,"score":5},{"min":10,"max":20,"score":10}]
        String rangesConfig = safeGetConfig(rule, "ranges", "");
        if (!isValidString(rangesConfig)) {
            // 如果没有范围配置，使用简单的线性计算
            return calculateLinearScore(value, rule);
        }

        try {
            // 这里可以解析JSON配置，简化实现使用预定义范围
            return calculatePredefinedRangeScore(value, rule);
        } catch (Exception e) {
            log.warn("解析范围配置失败，使用线性计算，规则：{}，错误：{}", rule.getRuleName(), e.getMessage());
            return calculateLinearScore(value, rule);
        }
    }

    /**
     * 使用预定义范围计算积分
     *
     * @param value 数值
     * @param rule  规则
     * @return 积分
     */
    private Double calculatePredefinedRangeScore(Double value, ScoreRule rule) {
        String ruleType = rule.getRuleType();
        return 0.0;
    }


    /**
     * 线性计算积分
     *
     * @param value 数值
     * @param rule  规则
     * @return 积分
     */
    private Double calculateLinearScore(Double value, ScoreRule rule) {
        Double minValue = safeGetConfig(rule, "minValue", 0.0);
        Double maxValue = safeGetConfig(rule, "maxValue", 100.0);
        Double minScore = safeGetConfig(rule, "minScore", 0.0);
        Double maxScore = safeGetConfig(rule, "maxScore", 100.0);

        if (value <= minValue) {
            return minScore;
        }
        if (value >= maxValue) {
            return maxScore;
        }

        // 线性插值
        double ratio = (value - minValue) / (maxValue - minValue);
        return minScore + ratio * (maxScore - minScore);
    }

    @Override
    public String getStrategyName() {
        return "数值范围积分策略";
    }

    @Override
    public String getSupportedRuleType() {
        return "numericRange";
    }

    @Override
    public String[] getApplicableScopes() {
        return new String[]{"person", "company", "device", "project"};
    }

    @Override
    public boolean supports(String ruleName, String ruleType, String scope) {
        // 支持多种规则类型
        if ("numericRange".equals(ruleType)
               || "age".equals(ruleType)
                || "workYears".equals(ruleType)
                || "income".equals(ruleType)
                || "experience".equals(ruleType)) {
            return true;
        }

        // 支持包含数值范围关键词的规则名称
        if (ruleName != null && (ruleName.contains("年龄") || ruleName.contains("年限")
                || ruleName.contains("收入") || ruleName.contains("经验")
                || ruleName.contains("数值") || ruleName.contains("范围"))) {
            return true;
        }

        return false;
    }

    @Override
    public boolean validateConfiguration(ScoreRule rule) {
        if (!super.validateConfiguration(rule)) {
            return false;
        }

        // 检查是否有propertyName配置
        String propertyName = safeGetConfig(rule, "propertyName", "");
        if (!isValidString(propertyName)) {
            log.warn("数值范围策略缺少propertyName配置，规则：{}", rule.getRuleName());
            return false;
        }

        return true;
    }

    @Override
    public String getConfigurationRequirements() {
        return "需要配置：propertyName（属性名称），可选配置：ranges（范围配置）、minValue、maxValue、minScore、maxScore";
    }

    @Override
    public int getPriority() {
        return 10; // 中等优先级
    }
}
