package com.trs.police.profile.generic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 通用积分计算上下文
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 泛型化的积分计算上下文，适用于各种积分计算场景
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ScoreContext {

    /**
     * 主键标识（如：身份证号、企业代码、设备ID等）
     */
    private String primaryKey;

    /**
     * 名称（如：人员姓名、企业名称、设备名称等）
     */
    private String name;

    /**
     * 类型（如：person、company、device等）
     */
    private String type;

    /**
     * 分类（如：个人、企业、政府机构等）
     */
    private String category;

    /**
     * 状态（如：正常、异常、停用等）
     */
    private String status;

    /**
     * 等级（如：高、中、低等）
     */
    private String level;

    /**
     * 标签信息
     */
    private String tags;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 扩展属性，用于存储各种动态数据
     */
    @Builder.Default
    private Map<String, Object> properties = new HashMap<>();

    /**
     * 添加属性
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void addProperty(String key, Object value) {
        if (properties == null) {
            properties = new HashMap<>();
        }
        properties.put(key, value);
    }

    /**
     * 获取属性
     *
     * @param key          属性键
     * @param defaultValue 默认值
     * @param <T>          属性值类型
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key, T defaultValue) {
        if (properties == null || !properties.containsKey(key)) {
            return defaultValue;
        }
        try {
            return (T) properties.get(key);
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }

    /**
     * 获取属性
     *
     * @param key 属性键
     * @param <T> 属性值类型
     * @return 属性值
     */
    public <T> T getProperty(String key) {
        return getProperty(key, null);
    }

    /**
     * 获取字符串属性
     *
     * @param key 属性键
     * @return 字符串值
     */
    public String getStringProperty(String key) {
        return getProperty(key, "");
    }

    /**
     * 获取数值属性
     *
     * @param key 属性键
     * @return 数值
     */
    public Double getNumberProperty(String key) {
        Object value = getProperty(key);
        if (value == null) {
            return 0.0;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    /**
     * 获取整数属性
     *
     * @param key 属性键
     * @return 整数值
     */
    public Integer getIntProperty(String key) {
        return getNumberProperty(key).intValue();
    }

    /**
     * 获取布尔属性
     *
     * @param key 属性键
     * @return 布尔值
     */
    public Boolean getBooleanProperty(String key) {
        Object value = getProperty(key);
        if (value == null) {
            return false;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return Boolean.parseBoolean(value.toString());
    }

    /**
     * 检查是否包含指定标签
     *
     * @param tag 标签名称
     * @return 是否包含
     */
    public boolean hasTag(String tag) {
        if (tags == null || tag == null) {
            return false;
        }
        return tags.contains(tag);
    }

    /**
     * 检查是否包含指定属性
     *
     * @param key 属性键
     * @return 是否包含
     */
    public boolean hasProperty(String key) {
        return properties != null && properties.containsKey(key);
    }

    /**
     * 获取所有属性键
     *
     * @return 属性键集合
     */
    public java.util.Set<String> getPropertyKeys() {
        return properties != null ? properties.keySet() : new java.util.HashSet<>();
    }

    /**
     * 清空所有属性
     */
    public void clearProperties() {
        if (properties != null) {
            properties.clear();
        }
    }

    /**
     * 复制属性到另一个上下文
     *
     * @param target 目标上下文
     */
    public void copyPropertiesTo(ScoreContext target) {
        if (properties != null && target != null) {
            if (target.properties == null) {
                target.properties = new HashMap<>();
            }
            target.properties.putAll(properties);
        }
    }
}
