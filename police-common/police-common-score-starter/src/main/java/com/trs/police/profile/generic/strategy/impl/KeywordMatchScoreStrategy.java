package com.trs.police.profile.generic.strategy.impl;

import com.trs.police.profile.generic.dto.GenericScoreResult;
import com.trs.police.profile.generic.dto.ScoreContext;
import com.trs.police.profile.generic.dto.ScoreRule;
import com.trs.police.profile.generic.strategy.AbstractGenericScoreStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 关键词匹配积分计算策略
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 根据关键词匹配计算积分，适用于职务、获奖情况、经历等文本型指标
 */
@Slf4j
@Component
public class KeywordMatchScoreStrategy extends AbstractGenericScoreStrategy {

    // 临时存储，实际应该从配置中获取
    private Map<String, Double> keywordScores = new HashMap<>();

    @Override
    protected GenericScoreResult doCalculateScore(ScoreContext context, ScoreRule rule) {
        // 获取要评估的属性名
        String propertyName = safeGetConfig(rule, "propertyName", "");
        if (!isValidString(propertyName)) {
            log.warn("关键词匹配策略缺少propertyName配置，规则：{}", rule.getRuleName());
            return createNoHitResult(context, rule);
        }

        // 获取属性值
        String propertyValue = context.getStringProperty(propertyName);
        if (!isValidString(propertyValue)) {
            return createNoHitResult(context, rule);
        }

        // 获取关键词配置
        Map<String, Double> keywordScores = getKeywordScores(rule);
        if (keywordScores.isEmpty()) {
            log.warn("关键词匹配策略缺少关键词配置，规则：{}", rule.getRuleName());
            return createNoHitResult(context, rule);
        }

        // 计算匹配积分
        ScoreResult scoreResult = calculateKeywordMatchScore(propertyValue, keywordScores, rule);

        if (scoreResult.totalScore <= 0) {
            return createNoHitResult(context, rule);
        }

        String hitData = String.format("%s：%s", propertyName, propertyValue);
        String description = String.format("关键词匹配：%s，命中%d个关键词",
                String.join(",", scoreResult.matchedKeywords), scoreResult.matchedKeywords.size());

        return createSuccessResult(context, rule, scoreResult.totalScore, hitData, description);
    }

    /**
     * 获取关键词积分配置
     *
     * @param rule 规则
     * @return 关键词积分映射
     */
    private Map<String, Double> getKeywordScores(ScoreRule rule) {
        Map<String, Double> keywordScores = new HashMap<>();

        // 获取关键词配置，格式：{"局长":50,"处长":40,"科长":30}
        String keywordsConfig = safeGetConfig(rule, "keywords", "");
        if (isValidString(keywordsConfig)) {
            try {
                // 这里可以解析JSON配置，简化实现使用预定义关键词
                keywordScores = getPredefinedKeywords(rule.getRuleType());
            } catch (Exception e) {
                log.warn("解析关键词配置失败，使用预定义关键词，规则：{}，错误：{}", rule.getRuleName(), e.getMessage());
                keywordScores = getPredefinedKeywords(rule.getRuleType());
            }
        } else {
            keywordScores = getPredefinedKeywords(rule.getRuleType());
        }

        return keywordScores;
    }

    /**
     * 获取预定义关键词
     *
     * @param ruleType 规则类型
     * @return 关键词积分映射
     */
    private Map<String, Double> getPredefinedKeywords(String ruleType) {
        Map<String, Double> keywords = new HashMap<>();

        switch (ruleType) {
            case "position":
                // 职务关键词
                keywords.put("局长", 50.0);
                keywords.put("副局长", 45.0);
                keywords.put("处长", 40.0);
                keywords.put("副处长", 35.0);
                keywords.put("科长", 30.0);
                keywords.put("副科长", 25.0);
                keywords.put("主任", 28.0);
                keywords.put("副主任", 23.0);
                keywords.put("队长", 25.0);
                keywords.put("副队长", 20.0);
                keywords.put("组长", 15.0);
                keywords.put("班长", 12.0);
                keywords.put("所长", 30.0);
                keywords.put("副所长", 25.0);
                keywords.put("指导员", 20.0);
                keywords.put("民警", 10.0);
                keywords.put("辅警", 8.0);
                keywords.put("协警", 6.0);
                break;

            case "awards":
                // 获奖关键词
                keywords.put("国家级", 50.0);
                keywords.put("省级", 30.0);
                keywords.put("市级", 20.0);
                keywords.put("县级", 15.0);
                keywords.put("一等奖", 30.0);
                keywords.put("二等奖", 20.0);
                keywords.put("三等奖", 15.0);
                keywords.put("特等奖", 40.0);
                keywords.put("优秀奖", 10.0);
                keywords.put("先进个人", 15.0);
                keywords.put("优秀员工", 12.0);
                keywords.put("劳动模范", 35.0);
                keywords.put("先进工作者", 25.0);
                keywords.put("优秀党员", 20.0);
                keywords.put("优秀干部", 18.0);
                keywords.put("嘉奖", 8.0);
                keywords.put("表彰", 10.0);
                keywords.put("表扬", 5.0);
                keywords.put("记功", 15.0);
                keywords.put("记大功", 25.0);
                break;

            case "education":
                // 教育经历关键词
                keywords.put("博士", 25.0);
                keywords.put("硕士", 20.0);
                keywords.put("研究生", 20.0);
                keywords.put("本科", 15.0);
                keywords.put("大学", 15.0);
                keywords.put("专科", 10.0);
                keywords.put("大专", 10.0);
                keywords.put("中专", 8.0);
                keywords.put("高中", 6.0);
                keywords.put("初中", 4.0);
                break;

            case "experience":
                // 经历关键词
                keywords.put("公务员", 20.0);
                keywords.put("事业单位", 15.0);
                keywords.put("国企", 12.0);
                keywords.put("外企", 15.0);
                keywords.put("私企", 8.0);
                keywords.put("军官", 25.0);
                keywords.put("士官", 20.0);
                keywords.put("士兵", 15.0);
                keywords.put("退伍", 15.0);
                keywords.put("转业", 20.0);
                keywords.put("复员", 15.0);
                keywords.put("留学", 20.0);
                keywords.put("出国", 10.0);
                keywords.put("海外", 15.0);
                keywords.put("培训", 5.0);
                keywords.put("进修", 8.0);
                keywords.put("深造", 10.0);
                break;

            default:
                // 通用关键词
                keywords.put("优秀", 10.0);
                keywords.put("良好", 8.0);
                keywords.put("合格", 5.0);
                keywords.put("先进", 12.0);
                keywords.put("突出", 15.0);
                break;
        }

        return keywords;
    }

    /**
     * 计算关键词匹配积分
     *
     * @param text          文本内容
     * @param keywordScores 关键词积分映射
     * @param rule          规则
     * @return 积分结果
     */
    private ScoreResult calculateKeywordMatchScore(String text, Map<String, Double> keywordScores, ScoreRule rule) {
        ScoreResult result = new ScoreResult();

        // 查找匹配的关键词
        for (Map.Entry<String, Double> entry : keywordScores.entrySet()) {
            String keyword = entry.getKey();
            Double score = entry.getValue();

            if (text.contains(keyword)) {
                result.matchedKeywords.add(keyword);
                result.totalScore += score;
                log.debug("命中关键词：{}，积分：{}", keyword, score);
            }
        }

        // 应用匹配策略
        String matchStrategy = safeGetConfig(rule, "matchStrategy", "sum");
        result.totalScore = applyMatchStrategy(result.totalScore, result.matchedKeywords.size(), matchStrategy, rule);

        // 设置积分上限
        Double maxScore = safeGetConfig(rule, "maxScore", 100.0);
        result.totalScore = Math.min(result.totalScore, maxScore);

        return result;
    }

    /**
     * 应用匹配策略
     *
     * @param totalScore 总积分
     * @param matchCount 匹配数量
     * @param strategy   匹配策略
     * @param rule       规则
     * @return 调整后的积分
     */
    private double applyMatchStrategy(double totalScore, int matchCount, String strategy, ScoreRule rule) {
        switch (strategy) {
            case "max":
                // 取最高分
                return keywordScores.values().stream()
                        .filter(score -> totalScore >= score)
                        .max(Double::compare)
                        .orElse(0.0);

            case "average":
                // 取平均分
                return matchCount > 0 ? totalScore / matchCount : 0.0;

            case "weighted":
                // 加权计算
                double bonus = calculateMatchBonus(matchCount, rule);
                return totalScore + bonus;

            case "sum":
            default:
                // 累加（默认）
                return totalScore;
        }
    }

    /**
     * 计算匹配数量加成
     *
     * @param matchCount 匹配数量
     * @param rule       规则
     * @return 加成分值
     */
    private double calculateMatchBonus(int matchCount, ScoreRule rule) {
        Double bonusPerMatch = safeGetConfig(rule, "bonusPerMatch", 2.0);

        if (matchCount >= 5) {
            return bonusPerMatch * 5; // 5个以上关键词给额外加成
        } else if (matchCount >= 3) {
            return bonusPerMatch * 3;
        } else if (matchCount >= 2) {
            return bonusPerMatch * 2;
        } else if (matchCount >= 1) {
            return bonusPerMatch;
        }

        return 0.0;
    }

    @Override
    public String getStrategyName() {
        return "关键词匹配积分策略";
    }

    @Override
    public String getSupportedRuleType() {
        return "keywordMatch";
    }

    @Override
    public String[] getApplicableScopes() {
        return new String[]{"person", "company", "project", "document"};
    }

    @Override
    public boolean supports(String ruleName, String ruleType, String scope) {
        // 支持多种规则类型
        if ("keywordMatch".equals(ruleType)
                || "position".equals(ruleType)
                || "awards".equals(ruleType)
                || "education".equals(ruleType)
                || "experience".equals(ruleType)) {
            return true;
        }

        // 支持包含关键词匹配相关的规则名称
        if (ruleName != null && (ruleName.contains("职务") || ruleName.contains("获奖")
                || ruleName.contains("经历") || ruleName.contains("教育")
                || ruleName.contains("关键词") || ruleName.contains("匹配"))) {
            return true;
        }

        return false;
    }

    @Override
    public boolean validateConfiguration(ScoreRule rule) {
        if (!super.validateConfiguration(rule)) {
            return false;
        }

        // 检查是否有propertyName配置
        String propertyName = safeGetConfig(rule, "propertyName", "");
        if (!isValidString(propertyName)) {
            log.warn("关键词匹配策略缺少propertyName配置，规则：{}", rule.getRuleName());
            return false;
        }

        return true;
    }

    @Override
    public String getConfigurationRequirements() {
        return "需要配置：propertyName（属性名称），可选配置：keywords（关键词配置）、matchStrategy（匹配策略）、maxScore（最大分值）、bonusPerMatch（匹配加成）";
    }

    @Override
    public int getPriority() {
        return 15; // 较高优先级
    }

    /**
     * 积分计算结果内部类
     */
    private static class ScoreResult {
        double totalScore = 0.0;
        List<String> matchedKeywords = new ArrayList<>();
    }
}
