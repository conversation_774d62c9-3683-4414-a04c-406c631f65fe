package com.trs.police.profile.generic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用积分规则配置
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 泛型化的积分规则配置，适用于各种积分计算场景
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ScoreRule {

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 规则类型（如：age、workYears、awards等）
     */
    private String ruleType;

    /**
     * 规则分类（如：background、behavior、risk等）
     */
    private String category;

    /**
     * 父规则ID
     */
    private String parentRuleId;

    /**
     * 子规则列表
     */
    private List<ScoreRule> children;

    /**
     * 基础分值
     */
    private Integer baseScore;

    /**
     * 系数
     */
    private Double coefficient;

    /**
     * 计算公式
     */
    private String formula;

    /**
     * 最小值
     */
    private Double minValue;

    /**
     * 最大值
     */
    private Double maxValue;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 是否启用
     */
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 优先级
     */
    @Builder.Default
    private Integer priority = 0;

    /**
     * 适用范围（如：person、company、device等）
     */
    private String scope;

    /**
     * 条件表达式
     */
    private String condition;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 扩展配置
     */
    @Builder.Default
    private Map<String, Object> config = new HashMap<>();

    /**
     * 添加配置
     *
     * @param key   配置键
     * @param value 配置值
     */
    public void addConfig(String key, Object value) {
        if (config == null) {
            config = new HashMap<>();
        }
        config.put(key, value);
    }

    /**
     * 获取配置
     *
     * @param key          配置键
     * @param defaultValue 默认值
     * @param <T>          配置值类型
     * @return 配置值
     */
    @SuppressWarnings("unchecked")
    public <T> T getConfig(String key, T defaultValue) {
        if (config == null || !config.containsKey(key)) {
            return defaultValue;
        }
        try {
            return (T) config.get(key);
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }

    /**
     * 获取配置
     *
     * @param key 配置键
     * @param <T> 配置值类型
     * @return 配置值
     */
    public <T> T getConfig(String key) {
        return getConfig(key, null);
    }

    /**
     * 获取字符串配置
     *
     * @param key 配置键
     * @return 字符串值
     */
    public String getStringConfig(String key) {
        return getConfig(key, "");
    }

    /**
     * 获取数值配置
     *
     * @param key 配置键
     * @return 数值
     */
    public Double getNumberConfig(String key) {
        Object value = getConfig(key);
        if (value == null) {
            return 0.0;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    /**
     * 判断是否为叶子规则
     *
     * @return 是否为叶子规则
     */
    public boolean isLeafRule() {
        return children == null || children.isEmpty();
    }

    /**
     * 判断是否启用
     *
     * @return 是否启用
     */
    public boolean isEnabled() {
        return enabled != null && enabled;
    }

    /**
     * 判断是否适用于指定范围
     *
     * @param targetScope 目标范围
     * @return 是否适用
     */
    public boolean isApplicableToScope(String targetScope) {
        if (scope == null || scope.trim().isEmpty()) {
            return true; // 没有指定范围则适用于所有
        }
        return scope.equals(targetScope) || scope.contains(targetScope);
    }

    /**
     * 获取有效的基础分值
     *
     * @return 基础分值
     */
    public double getEffectiveBaseScore() {
        return baseScore != null ? baseScore.doubleValue() : 0.0;
    }

    /**
     * 获取有效的系数
     *
     * @return 系数
     */
    public double getEffectiveCoefficient() {
        return coefficient != null ? coefficient : 1.0;
    }

    /**
     * 获取有效的权重
     *
     * @return 权重
     */
    public double getEffectiveWeight() {
        return weight != null ? weight : 1.0;
    }

    /**
     * 检查分值是否在有效范围内
     *
     * @param score 分值
     * @return 是否在有效范围内
     */
    public boolean isScoreInRange(double score) {
        boolean minOk = minValue == null || score >= minValue;
        boolean maxOk = maxValue == null || score <= maxValue;
        return minOk && maxOk;
    }

    /**
     * 限制分值在有效范围内
     *
     * @param score 原始分值
     * @return 限制后的分值
     */
    public double constrainScore(double score) {
        if (minValue != null && score < minValue) {
            return minValue;
        }
        if (maxValue != null && score > maxValue) {
            return maxValue;
        }
        return score;
    }
}
