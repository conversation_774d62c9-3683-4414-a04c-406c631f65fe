package com.trs.police.profile.generic.engine;

import com.trs.police.profile.generic.dto.GenericScoreResult;
import com.trs.police.profile.generic.dto.ScoreContext;
import com.trs.police.profile.generic.dto.ScoreRule;
import com.trs.police.profile.generic.factory.GenericScoreStrategyFactory;
import com.trs.police.profile.generic.strategy.GenericScoreStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 通用积分计算引擎
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 通用的积分计算引擎，支持各种场景的积分计算
 */
@Slf4j
@Component
public class GenericScoreEngine {

    @Autowired
    private GenericScoreStrategyFactory strategyFactory;

    /**
     * 计算总积分
     *
     * @param context 积分计算上下文
     * @param rules   积分规则列表（树形结构）
     * @return 积分计算结果
     */
    public GenericScoreResult calculateTotalScore(ScoreContext context, List<ScoreRule> rules) {
        log.info("开始计算积分，主键：{}，类型：{}", context.getPrimaryKey(), context.getType());

        GenericScoreResult totalResult = GenericScoreResult.builder()
                .primaryKey(context.getPrimaryKey())
                .name(context.getName())
                .type(context.getType())
                .totalScore(0.0)
                .weightedScore(0.0)
                .detailScores(new ArrayList<>())
                .build();

        if (CollectionUtils.isEmpty(rules)) {
            log.warn("积分规则为空，返回0分");
            return totalResult;
        }

        // 递归计算每个规则的积分
        for (ScoreRule rule : rules) {
            if (!rule.isEnabled()) {
                log.debug("规则已禁用，跳过：{}", rule.getRuleName());
                continue;
            }

            GenericScoreResult ruleResult = calculateRuleScore(context, rule);
            if (ruleResult != null && ruleResult.getTotalScore() > 0) {
                totalResult.setTotalScore(totalResult.getTotalScore() + ruleResult.getTotalScore());
                totalResult.setWeightedScore(totalResult.getWeightedScore() + ruleResult.getWeightedScore());
                totalResult.getDetailScores().addAll(ruleResult.getDetailScores());
            }
        }

        // 计算积分等级
        totalResult.calculateScoreLevel();

        log.info("积分计算完成，主键：{}，总分：{}，加权分：{}",
                context.getPrimaryKey(), totalResult.getTotalScore(), totalResult.getWeightedScore());
        return totalResult;
    }

    /**
     * 计算单个规则的积分
     *
     * @param context 积分计算上下文
     * @param rule    积分规则
     * @return 积分计算结果
     */
    private GenericScoreResult calculateRuleScore(ScoreContext context, ScoreRule rule) {
        try {
            GenericScoreResult result = GenericScoreResult.builder()
                    .primaryKey(context.getPrimaryKey())
                    .name(context.getName())
                    .type(context.getType())
                    .totalScore(0.0)
                    .weightedScore(0.0)
                    .detailScores(new ArrayList<>())
                    .build();

            // 判断是否为叶子规则
            if (rule.isLeafRule()) {
                // 叶子规则，执行具体的积分计算策略
                result = executeScoreStrategy(context, rule);
            } else {
                // 父规则，递归计算所有子规则的积分并累加
                if (!CollectionUtils.isEmpty(rule.getChildren())) {
                    for (ScoreRule childRule : rule.getChildren()) {
                        if (!childRule.isEnabled()) {
                            continue;
                        }

                        GenericScoreResult childResult = calculateRuleScore(context, childRule);
                        if (childResult != null) {
                            // 累加子规则的总分
                            result.setTotalScore(result.getTotalScore() + childResult.getTotalScore());
                            result.setWeightedScore(result.getWeightedScore() + childResult.getWeightedScore());
                            // 合并详细积分记录
                            if (!CollectionUtils.isEmpty(childResult.getDetailScores())) {
                                result.getDetailScores().addAll(childResult.getDetailScores());
                            }
                        }
                    }
                }
                log.debug("父规则积分计算完成，规则名称：{}，总分：{}", rule.getRuleName(), result.getTotalScore());
            }

            return result;
        } catch (Exception e) {
            log.error("计算规则积分失败，规则名称：{}，错误信息：{}", rule.getRuleName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 执行具体的积分计算策略（仅对叶子规则）
     *
     * @param context 积分计算上下文
     * @param rule    积分规则
     * @return 积分计算结果
     */
    private GenericScoreResult executeScoreStrategy(ScoreContext context, ScoreRule rule) {
        // 确保只有叶子规则才执行策略计算
        if (!rule.isLeafRule()) {
            log.warn("尝试对非叶子规则执行策略计算，规则名称：{}", rule.getRuleName());
            return createEmptyResult(context);
        }

        // 根据规则获取对应的策略
        GenericScoreStrategy strategy = strategyFactory.getStrategy(
                rule.getRuleName(), rule.getRuleType(), context.getType());

        if (strategy == null) {
            log.debug("未找到对应的积分策略，规则名称：{}，规则类型：{}，跳过计算",
                    rule.getRuleName(), rule.getRuleType());
            return createEmptyResult(context);
        }

        log.debug("执行叶子规则积分计算，规则名称：{}，策略：{}", rule.getRuleName(), strategy.getStrategyName());

        // 执行策略计算
        GenericScoreResult result = strategy.calculateScore(context, rule);

        // 应用系数、权重和公式
        if (result != null && result.getTotalScore() > 0) {
            result = applyFormulaAndCoefficient(result, rule);
            log.debug("叶子规则积分计算完成，规则名称：{}，最终分值：{}", rule.getRuleName(), result.getTotalScore());
        } else {
            log.debug("叶子规则积分计算无结果，规则名称：{}", rule.getRuleName());
        }

        return result;
    }

    /**
     * 应用计算公式、系数和权重
     *
     * @param result 原始积分结果
     * @param rule   积分规则
     * @return 应用公式后的积分结果
     */
    private GenericScoreResult applyFormulaAndCoefficient(GenericScoreResult result, ScoreRule rule) {
        double originalScore = result.getTotalScore();
        double finalScore = originalScore;

        // 应用系数
        double coefficient = rule.getEffectiveCoefficient();
        if (coefficient != 1.0) {
            finalScore = finalScore * coefficient;
        }

        // 应用计算公式（如果有的话）
        if (rule.getFormula() != null && !rule.getFormula().trim().isEmpty()) {
            finalScore = applyFormula(finalScore, rule.getFormula());
        }

        // 应用基础分值
        double baseScore = rule.getEffectiveBaseScore();
        if (baseScore > 0) {
            finalScore = finalScore + baseScore;
        }

        // 限制分值在有效范围内
        finalScore = rule.constrainScore(finalScore);

        // 计算加权分值
        double weight = rule.getEffectiveWeight();
        double weightedScore = finalScore * weight;

        result.setTotalScore(finalScore);
        result.setWeightedScore(weightedScore);

        // 更新详细积分记录
        if (!CollectionUtils.isEmpty(result.getDetailScores())) {
            double finalScore1 = finalScore;
            result.getDetailScores().forEach(detail -> {
                detail.setRuleId(rule.getRuleId());
                detail.setRuleName(rule.getRuleName());
                detail.setRuleType(rule.getRuleType());
                detail.setCategory(rule.getCategory());
                detail.setOriginalScore(originalScore);
                detail.setFinalScore(finalScore1);
                detail.setWeightedScore(weightedScore);
                detail.setCoefficient(coefficient);
                detail.setWeight(weight);
                detail.setFormula(rule.getFormula());
                detail.setBaseScore(rule.getBaseScore());
            });
        }

        return result;
    }

    /**
     * 应用计算公式
     *
     * @param score   当前分值
     * @param formula 计算公式
     * @return 计算后的分值
     */
    private double applyFormula(double score, String formula) {
        try {
            // 这里可以实现更复杂的公式计算逻辑
            // 目前简单实现，可以根据需要扩展
            switch (formula.toLowerCase()) {
                case "square":
                    return score * score;
                case "sqrt":
                    return Math.sqrt(score);
                case "log":
                    return Math.log(score + 1); // 避免log(0)
                case "double":
                    return score * 2;
                case "half":
                    return score / 2;
                default:
                    // 默认返回原分值
                    return score;
            }
        } catch (Exception e) {
            log.warn("应用计算公式失败，公式：{}，分值：{}，错误：{}", formula, score, e.getMessage());
            return score;
        }
    }

    /**
     * 创建空的积分结果
     *
     * @param context 积分计算上下文
     * @return 空的积分结果
     */
    private GenericScoreResult createEmptyResult(ScoreContext context) {
        return GenericScoreResult.builder()
                .primaryKey(context.getPrimaryKey())
                .name(context.getName())
                .type(context.getType())
                .totalScore(0.0)
                .weightedScore(0.0)
                .detailScores(new ArrayList<>())
                .build();
    }

    /**
     * 批量计算积分
     *
     * @param contexts 积分计算上下文列表
     * @param rules    积分规则列表
     * @return 积分计算结果列表
     */
    public List<GenericScoreResult> batchCalculateScore(List<ScoreContext> contexts, List<ScoreRule> rules) {
        if (CollectionUtils.isEmpty(contexts)) {
            return new ArrayList<>();
        }

        return contexts.parallelStream()
                .map(context -> calculateTotalScore(context, rules))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
