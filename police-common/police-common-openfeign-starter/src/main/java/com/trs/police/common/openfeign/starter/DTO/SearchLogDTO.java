package com.trs.police.common.openfeign.starter.DTO;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseListDTO;
import com.trs.common.utils.StringUtils;
import lombok.Data;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * @ClassName SearchLogDTO
 * @Description 全景日志检索DTO
 * <AUTHOR>
 * @Date 2023/10/23 11:00
 **/
@Data
public class SearchLogDTO extends BaseListDTO {

    /**
     * 起始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 组织检索信息
     */
    private String deptInfo;

    /**
     * 功能模块 全部，人员，车辆，手机，案件，全景通查
     */
    private String operateModule;

    /**
     * 行为类型
     */
    private String operateType;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 登记事由
     */
    private String djsy;

    /**
     * 关键字类型 all：全部，user：用户名称，ip：ip地址
     */
    private String keywordType;

    /**
     * 关键字内容
     */
    private String keyword;

    /**
     * 排序类型 DESC  ASC
     */
    private String orderType;

    /**
     * 排序字段   TIME
     */
    private String orderField;

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(this.getOrderType(), "排序方式不能为空！");
        checkNotEmpty(this.getOrderField(), "排序类型不能为空！");
        if (StringUtils.isNotEmpty(this.getKeyword())) {
            checkNotEmpty(this.getKeywordType(), "检索类型不能为空！");
        }
        return super.checkParams();
    }
}
